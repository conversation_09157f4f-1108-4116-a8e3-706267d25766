import useOutsideClick from '@shared/hooks/useOutsideClick';
import { DocTypeSummary, DocumentCount } from '@shared/models/inbox';
import s from '@shared/styles/component/inbox/inbox-overlay.module.scss';
import { ReactComponent as InboxIcon } from '@svg/inbox-icon.svg';
import React, { useRef } from 'react';
import { CSSTransition } from 'react-transition-group';

interface Props {
  docTypes: DocumentCount[];
  selectType: (docType: DocTypeSummary) => void;
  setIsOpen: (isOpen) => void;
  isOpen: boolean;
  children?: any;
}

const InboxTableToolbarDropdown: React.FC<Props> = ({
  children,
  docTypes,
  selectType,
  setIsOpen,
  isOpen,
}) => {
  const ref = useRef(null);
  useOutsideClick(ref, () => setIsOpen(false));
  return (
    <>
      <div ref={ref}>
        <div
          data-testid="inbox-overlay-dropdown"
          onClick={() => {
            setIsOpen(!isOpen);
          }}
        >
          {children}
        </div>

        <CSSTransition
          timeout={300}
          classNames={'inbox-dropdown-anim'}
          in={isOpen}
          unmountOnExit
          mountOnEnter
        >
          <div className={s.dropdown}>
            <div className={s.dropdown__title}>Move to</div>
            {docTypes.map((type) => {
              return (
                <React.Fragment key={type.id}>
                  <div
                    data-testid="inbox-overlay-option"
                    onClick={() => selectType({ docTypeId: type.id })}
                    className={s.dropdown__item}
                  >
                    <InboxIcon className={s.dropdown__icon} />
                    <div className={s.dropdown__text}>{type.name}</div>
                  </div>
                  {type.subTypes?.map((subType) => {
                      return (
                        <div
                          data-testid="inbox-overlay-option-child"
                          onClick={() => {
                            setIsOpen(false);
                            selectType({ docTypeId: type.id, subTypeId: subType.id });
                          }}
                          key={subType.id}
                          className={s.dropdown__item}
                        >
                          <InboxIcon className={s.dropdown__icon} />
                          <div className={s.dropdown__text}>
                            {type.name}/{subType.name}
                          </div>
                        </div>
                      );
                    })}
                </React.Fragment>
              );
            })}
          </div>
        </CSSTransition>
      </div>
    </>
  );
};

export default InboxTableToolbarDropdown;
