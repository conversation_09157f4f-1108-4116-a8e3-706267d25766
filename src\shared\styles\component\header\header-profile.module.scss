@import "../../vars/_vars";


.container {
  position: relative;
  display: flex;
  align-items: center;
  height: rem(32);
}


.avatar {
  height: 100%;
  margin-right: rem(10);
  cursor: pointer;
  border-radius: 50px;
}


.name {
  font-family: $base-font;
  font-size: rem(16);
  font-weight: 400;
  margin-right: rem(10);
}


.name_light {
  color: $white;
}


.dropdown__select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
}


.dropdown {
  position: absolute;
  z-index: 1000;
  top: 40px;
  right: -8px;
  overflow: visible;
  width: 260px;
  padding: 18px;
  border: 1px solid $medium-light-gray;
  border-radius: 10px;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 13, 33, 0.10);

  &__top {
    top: unset;
    right: unset;
    bottom: 40px;
    left: -15px;
  }

}


.dropdown__arrow {
  width: auto;
  transition: transform 0.2s ease-in-out;
  transform: rotate(180deg);
}


.dropdown__arrow_active {
  transition: transform 0.2s ease-in-out;
  transform: rotate(0deg);
}


.header {
  display: flex;
  align-items: center;
  flex-direction: column;
  margin-bottom: 20px;
}


.profile {
  width: 50px;
  height: 50px;
  margin-bottom: 14px;
  border-radius: 50px;
}


.dropdown_name {
  font-weight: 500;
}


.stats {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid $medium-light-gray;
}


.stat_value {
  font-weight: 800;
}


@keyframes grow {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.15);

  }
  100% {
    transform: scale(1);

  }
}


.divider {
  width: 100%;
  height: 1px;
  margin-top: 10px;
  margin-bottom: 5px;
  background-color: #EEEEEE;
}


.nav_item {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 8px 11px;
  cursor: pointer;
  color: black;
  border-radius: 5px;
  background: $white;

  &__success {
    animation: grow 0.3s;

    .nav_icon {
      color: $success;
    }
  }

  &:hover:not(&__no_hover) {
    background: $light-gray;
  }

}


.nav_text {
  font-family: $base-font;
  font-size: rem(14);
}


.email {
  line-height: 1.5;
  width: 400px;
  padding: 25px;
  border-radius: 5px;
  background: white;

  svg {
    width: 20%;
    transform: unset;
  }
}


.nav_icon {
  width: auto;
  height: 14px;
  margin-right: 8px;
  color: #AEBCD0;

}
.version{
    font-size: 12px;
    font-weight: 400;
    color: $dark-gray;
    margin-top: 10px;
    position: absolute;
    bottom:10px;
    right:10px;
}


:global {
  .profile-dropdown-anim-enter {
    transform: scale(0.8);
    opacity: 0;
  }

  .profile-dropdown-anim-enter-active {
    transition: opacity 0.150s ease-in-out, transform 0.150s ease-in-out;
    transform: scale(1);
    transform-origin: top center;
    opacity: 1;
  }

  .profile-dropdown-anim-exit {
    transform: scale(1);
    opacity: 1;
  }

  .profile-dropdown-anim-exit-active {
    transition: opacity 0.150s ease-in-out, transform 0.150s ease-in-out;
    transform: scale(0.8);
    transform-origin: top center;
    opacity: 0;
  }
}
