import AdminItemRow from '@components/admin/components/AdminItemRow.tsx';
import FormSection from '@components/admin/components/form/FormSection.tsx';
import ConfirmationDialog from '@components/shared/confirmation-dialog/ConfirmationDialog.tsx';
import { IClientMetadata } from '@shared/helpers/converters/metadata.ts';
import { extendedSearch, globalFuseOptions } from '@shared/helpers/helpers.ts';
import { useModal } from '@shared/hooks/useModal.tsx';
import { useNotification } from '@shared/hooks/useNotificationBar.tsx';
import { useMetadataUsageMap } from '@shared/hooks/useUsageMap.ts';
import { deleteMetadataTypes } from '@shared/store/adminSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store.ts';
import p from '@shared/styles/component/admin/admin-pages/admin-page.module.scss';
import s from '@shared/styles/component/admin/admin-section.module.scss';
import clsx from 'clsx';
import Fuse from 'fuse.js';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';

const fuseOptions = {
  ...globalFuseOptions,
  keys: ['name'],
};
const AdminInboxesMetadata: React.FC = () => {
  const inboxMetadataTypes = useSelector((state) => state.admin.inboxMetadataTypes);
  const inboxDocTypes = useSelector((state) => state.admin.inboxDocTypes) ?? [];

  const dispatch = useDispatch();
  const { showDialog } = useModal();
  const { t } = useTranslation();
  const { showNotification } = useNotification();
  const navigate = useNavigate();
  const [fuseData, setFuseData] = useState([]);
  const [searchResults, setSearchResults] = useState<IClientMetadata[]>(null);
  const [selectedMetadataTypes, setSelectedMetadataTypes] = useState<string[]>([]);
  const fuse = new Fuse(fuseData, fuseOptions);

  // Calculate metadata usage across document types
  const metadataUsageMap = useMetadataUsageMap(inboxDocTypes);

  const handleMatches = (input) => {
    setSearchResults(extendedSearch(input, fuse));
  };

  const handleSearchInput = (value: string) => {
    if (value === '') {
      setSearchResults(inboxMetadataTypes);
    } else {
      handleMatches(value);
    }
  };
  const handleDelete = (setting: IClientMetadata) => {
    showDialog(
      <ConfirmationDialog
        confirmAction={() => dispatch(deleteMetadataTypes([setting.id]))}
        text={t('admin:inboxes.sections.metadataTypeDelete')}
      />,
    );
  };

  const handleMultiDelete = () => {
    showDialog(
      <ConfirmationDialog
        confirmAction={() => {
          dispatch(deleteMetadataTypes(selectedMetadataTypes));
          setSelectedMetadataTypes([]);
        }}
        text={t('admin:inboxes.sections.metadataTypeDelete')}
      />,
    );
  };

  useEffect(() => {
    if (inboxMetadataTypes) {
      setSearchResults(inboxMetadataTypes);
      setFuseData(inboxMetadataTypes);
    }
  }, [inboxMetadataTypes]);

  return (
    <div className={clsx(s.form_body, s.form_body_scroll)}>
      <div className={p.body_header}>
        <h2>{t('admin:inboxes.metadataTypes')}</h2>
      </div>
      <p className={p.body_description}>{t('admin:inboxes.metadataTypesDescription')}</p>

      <FormSection
        noStyle
        scroll
        title={t('admin:inboxes.metadataTypes')}
        add={{
          testId: 'metadata-add',
          onClick: () => navigate('new'),
          label: t('admin:inboxes.sections.addNew'),
        }}
        search={{
          onChange: (e) => handleSearchInput(e),
          placeholder: t('admin:inboxes.sections.metadataTypeSearch'),
        }}
        select={{
          handleDelete: handleMultiDelete,
          allValues: inboxMetadataTypes?.map((e) => e.id) ?? [],
          selectedValues: selectedMetadataTypes,
          setSelectedValues: setSelectedMetadataTypes,
        }}
        copy={{
          copyValues: inboxMetadataTypes?.map((dt) => dt.id) ?? [],
        }}
      >
        <div className={clsx(s.row_list)}>
          {searchResults?.map((setting, index) => {
            const isChecked = selectedMetadataTypes.findIndex((e) => e === setting.id) !== -1;
            const usageInfo = metadataUsageMap.get(setting.id) || { count: 0, docTypeNames: [] };
            return (
              <AdminItemRow
                animationSettings={{
                  enabled: true,
                  delay: 50 + index * 25,
                }}
                isChecked={isChecked}
                setIsChecked={(value) => {
                  if (value) {
                    setSelectedMetadataTypes([...selectedMetadataTypes, setting.id]);
                  } else {
                    setSelectedMetadataTypes(selectedMetadataTypes.filter((id) => id !== setting.id));
                  }
                }}
                handleDelete={() => handleDelete(setting)}
                handleNav={() => {
                  navigate(setting.id);
                }}
                handleCopy={() => {
                  navigator.clipboard.writeText(setting.id);
                  showNotification(t('home:notifications.copied'), 'success');
                }}
                key={`metadataType${setting.id}`}
                metadataType={setting}
                title={setting.name}
                usageInfo={usageInfo}
              />
            );
          })}
          {searchResults?.length === 0 && (
            <div className={s.no_results}>{t('admin:inboxes.sections.noMetadataTypeFound')}</div>
          )}
        </div>
      </FormSection>
    </div>
  );
};

export default AdminInboxesMetadata;
