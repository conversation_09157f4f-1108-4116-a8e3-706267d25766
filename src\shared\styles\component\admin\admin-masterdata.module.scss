@import "../../vars/_vars";


.mapping {
  padding-top: 20px;
}


.mapping_rows {
  display: grid;
  overflow: auto;
  max-height: 500px;
  margin-right: -10px;
  padding-right: 10px;
  gap: 10px;
  row-gap: 10px;
  //grid-template-columns: repeat(8, auto); /* Adjust number of columns */
  grid-template-columns:  minmax(max-content, auto) min-content minmax(auto, 400px) repeat(5, min-content);

}


.mapping_row_header {
  position: sticky;
  z-index: 1;
  top: 0;
  display: flex;
  align-self: flex-start;
  height: 25px;
  background: white;
  gap: 8px;

  &__center {
    justify-content: center;

  }

  svg {
    width: 14px;
    min-width: 14px;
    transform: translateY(-1px) rotate(180deg);
    color: rgba(0, 0, 0, 0.23);
  }
}


.mapping_row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  padding: 10px 0px;
  border-bottom: 1px solid #EEEEEE;
  gap: 10px;

  & > * {
    display: flex;
    width: 100%;
    max-width: 25%;

    &:first-child {
      max-width: 15%;
    }
  }

  &:last-of-type {
    border-bottom: none;
  }

}


.mapping_row_item {
  display: flex;
  flex-grow: 0;
  width: auto;
  min-width: 0;

  &__center {
    justify-content: center;
  }

}


.icon__info {
  width: 14px;
  margin-left: 8px;
  transform: translateY(-1px) rotate(180deg);
  color: rgba(0, 0, 0, 0.46);
}


.icon {
  width: auto;
  height: 18px;
  cursor: pointer;
  color: #898B99;

  &:hover {
    color: $error;
  }
}


.switcher {
  display: flex;
  height: 35px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
}


.switcher_option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  cursor: pointer;
  transition: color 0.2s ease;
  color: #cccccc;

  svg {
    width: 18px;
    height: auto;
  }

  & + & {
    border-left: 1px solid #EEEEEE;
  }

  &__active:not(&:disabled), &:hover:not(&:disabled) {
    color: $paperbox-blue;
  }

  &__delete:hover:not(&:disabled) {
    color: $error;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.3;
  }
}


.tags_button {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #969FAD;
  gap: 10px;

  span {
    margin-top: 2px;
  }

  div {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    height: 35px;
    vertical-align: center;

    svg {
      width: auto;
      height: 16px;
    }
  }

  &__active, &:hover {
    color: $paperbox-blue;
  }

}


.tag_selector {
  position: fixed;
  z-index: 10;
  top: 40px;
  display: flex;

  padding: 19px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: white;

  gap: 10px;

  input {
    width: 35px;
    padding: 0;
    text-align: center;
  }
}


.selectors {
  display: flex;
  flex-wrap: wrap;
  max-width: 290px;
  gap: 6px;
}


.selector_option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  cursor: pointer;
  transition: color 0.2s ease, border-color 0.2s ease;
  color: #7f7f7f;

  border: 1px solid #EEEEEE;
  border-radius: 5px;

  svg {
    width: 18px;
    height: auto;
  }

  &:hover {
    color: $error;
    border: 1px solid rgba($error, 0.5);
  }
}
