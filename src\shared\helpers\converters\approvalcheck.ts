export interface IRawApprovalCheck {
  name: string;
  id: string;
  description?: string;
  is_archived?: boolean;
  type: string;
}

export interface IApprovalCheck {
  name: string;
  id: string;
  description?: string;
  isArchived?: boolean;
  type: string;
}

export function approvalCheckRawToClient(raw: IRawApprovalCheck): IApprovalCheck {
  const obj: IApprovalCheck = {
    name: raw.name,
    id: raw.id,
    type: raw.type,
  };
  if (raw.description) obj.description = raw.description;
  if (raw.is_archived) obj.isArchived = raw.is_archived;
  return obj;
}

export function approvalCheckClientToRaw(approvalCheck: IApprovalCheck): IRawApprovalCheck {
  const obj: IRawApprovalCheck = {
    name: approvalCheck.name,
    id: approvalCheck.id,
    type: approvalCheck.type,
  };
  if (approvalCheck.description) obj.description = approvalCheck.description;
  if (approvalCheck.isArchived) obj.is_archived = approvalCheck.isArchived;
  return obj;
}
