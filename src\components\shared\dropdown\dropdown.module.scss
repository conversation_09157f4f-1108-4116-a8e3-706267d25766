@import "./src/shared/styles/vars/_vars";


.label {
  font-size: 12px;
  line-height: 9px;
  flex-shrink: 0;
  margin-right: 0px;
  padding: 4px 7px;
  color: white;
  border-radius: 5px;
  background: $paperbox-blue;
}


.input_override {
  input {
    background: none !important;
  }
}


.option {

  display: flex;
  align-items: center;
  margin: 5px;
  padding: 7px 10px;
  border-radius: 7px;
  gap: 6px;

  .text {
    display: -webkit-box;
    overflow: hidden;
    max-width: 250px;
    white-space: normal;
    text-overflow: ellipsis;
    word-break: normal;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .label {
    margin-right: unset;
    margin-left: auto;
  }

  &.active {
    color: $paperbox-blue;
  }

  &:focus {
    outline: none;
  }

  &:hover:not(.option__placeholder), &:focus {
    cursor: pointer;
    color: $paperbox-blue;
    outline: none;
    background-color: #E9EFFF;
  }
}

