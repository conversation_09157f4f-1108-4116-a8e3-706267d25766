@import "../../vars/_vars";


.container {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: auto;
  min-width: 568px;
  height: 100%;
  padding: 18px;
  border: 1px solid $medium-gray;
  border-radius: 10px;
  background: white;
}


.container_small {
  width: auto;
  min-width: 360px;
  max-width: 550px;
  height: 195px;

  &:only-child {
    max-width: 450px;
  }

  &:last-of-type:after {
    display: block;
    height: 30px;
    content: "";
  }

}


.header {
  display: flex;
  align-items: center;
  flex-grow: 0;
  flex-shrink: 0;
  margin-bottom: 18px;
}


.title {
  font-family: $headings-font;
  font-size: 16px;
  font-weight: 600;

}


.group {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  width: 250px;
  margin-right: 50px;
  margin-left: auto;

}


.column {
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: $medium-dark-gray;

}


.activity_indicator {
  width: 10px;
  height: 10px;
  margin-right: 8px;
  border: 1px solid $error;
  border-radius: 5px;
  background: transparent;

  &__active {
    border: 1px solid $success;

  }

  &__paperbox {
    border: 1px solid $paperbox-blue;

  }
}


.details {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 0;
  max-height: 0;
  border-radius: 0 0 10px 10px;
  background: $light-gray;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04), 0 0 2px rgba(0, 0, 0, 0.06), 0 0 1px rgba(0, 0, 0, 0.04);

}


.details__open {
  overflow-y: auto;
  flex: 1;
  max-height: 100%;
  margin-top: -10px;
  padding: 15px 0 0 0;

}


.wrapper {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  flex-grow: 0;
  flex-shrink: 0;
  width: 100%;
  height: auto;
  min-height: 34px;
  max-height: 100%;
  border-radius: 5px;

  & > .details__open:last-child {
    padding-bottom: 5px;
  }

  & + & {
    margin-top: 4px;
  }

}


.row {
  z-index: 10;
  display: flex;
  align-items: center;
  width: 100%;
  height: 34px;
  padding: 10px 10px;
  user-select: none;
  border: 1px solid #EFEFEF;
  border-radius: 7px;
  background: #FFFFFF;

}


.row_group {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  width: 250px;
  margin-right: 20px;
  margin-left: auto;

}


.count {
  font-size: 16px;
  font-weight: normal;
  font-style: normal;
  line-height: 21px;
  text-align: center;

}


.arrow {
  width: 20px;
  height: 20px;
  transition: transform 0.2s ease-in-out;
  color: $font-color-black;

}


.arrow__open {
  transition: transform 0.2s ease-in-out;
  transform: rotateZ(180deg);
}


.name {
  font-weight: 500;
  display: flex;
  overflow: hidden;
  align-items: center;
  flex-grow: 1;
  text-overflow: ellipsis;

  svg {
    width: auto;
    height: 12px;
    margin-left: 8px;
    color: $medium-dark-gray;
  }

  &:first-child {
    margin-left: 18px;
  }
}


.icon {
  width: 24px;
  height: 24px;
  margin-right: 5px;
  margin-left: 10px;
  color: $paperbox-blue;

  svg {
    width: auto;
    height: 100%;
  }
}


@keyframes opacity_fade {
  0% {
    opacity: 0.6;
    transform: scale(0.98);
  }

  50% {
    opacity: 0.8;
    transform: scaleX(1.005);
  }
  100% {
    opacity: 1;
    backface-visibility: visible;
    transform: scaleX(1);
  }
}


.sub_row {
  display: flex;
  align-items: center;
  width: 100%;
  height: 28px;
  padding: 10px 19px 10px 10px;
  animation: opacity_fade 0.25s ease-in-out forwards;
  opacity: 0;
  will-change: opacity;

  .name {
    font-size: 14px;
    width: 45%;
    max-width: 45%;
    margin-left: 18px;
    text-transform: capitalize;
    text-overflow: ellipsis;
  }

  .count {
    font-size: 14px;
  }

}


.chart_wrapper {
  display: flex;
  width: 100%;
  height: 100%;
}


:global {

  .recharts-cartesian-axis-tick-line {
    transform: translateY(-5px) scaleY(0.3);
    transform-origin: bottom;
  }

}


.chart {
  flex-grow: 0;
  flex-shrink: 0;
  height: calc(100% - 50px);
  margin-top: 20px;
}


.chart_bar {
  display: flex;
  width: 100%;
  height: 100%;
  background: $light-gray;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
}


.chart_bar_data {
  font-size: 12px;
  margin-top: 2px;
  margin-right: 8px;
  margin-left: -6px;
}


::-webkit-scrollbar {
  width: 4px;
}


::-webkit-scrollbar-track {
  background: white;
}


::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: $medium-light-gray;
}
