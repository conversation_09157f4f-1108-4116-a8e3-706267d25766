import { cloneDeep } from 'lodash';
import { useCallback } from 'react';
import { useNavigate } from 'react-router';
import { normalizeEntity, uuid4hex } from '../helpers/helpers';
import { useDispatch, useSelector } from '../store/store';

// Import document slice actions and selectors
// These will be created in the documentSlice.ts file
import {
  selectEffectiveNextSiblingId,
  selectEffectivePrevSiblingId,
  setActivePage,
  setIsViewerLoaded,
  setSelectedMutationId,
  // Add other actions as needed
} from '../store/documentSlice';

// Import RTK Query APIs
import { firestoreApi } from '../helpers/rtk-query/firestoreApi';

// Import document services
import {
  IDocumentEntityPatchPayload,
  IDocumentEntityPostPayload,
  addEntityToDocument,
  bounceDocument,
  changeDocumentDoctype,
  changeDocumentInbox,
  createDocumentMutation,
  deleteDocument,
  deleteMutation,
  editEntityInDocument,
  getRawPDF,
  importMasterDataResult,
  patchDocument,
  patchTopologyPart,
  removeEntityFromDocument,
  unlinkDocument,
  validateDocument,
} from '../services/documentService';

// Import helpers
import {
  IDocumentEntity,
  IDocumentEntityComplexValue,
  convertPartToRaw,
  entityToRawPayload,
} from '../helpers/converters/document';

// Import types
import { ActiveEntityPair } from '../models/document';

// Import pending operations actions
import { addPendingOperation, removePendingOperation } from '../store/pendingOperationsSlice';

/**
 * Hook that provides document operations without causing unnecessary rerenders
 */
export function useDocumentOperations() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Select only the state needed for operations
  // These selectors will be defined in documentSlice.ts
  const selectedMutationId = useSelector((state) => state.document.selectedMutationId);
  const mainDocId = useSelector((state) => state.document.mainDocId);
  const inboxId = useSelector((state) => state.document.inboxId);
  const activePageNo = useSelector((state) => state.document.activePageNo);
  const pageIndexMap = useSelector((state) => state.document.pageIndexMap);
  const prevSiblingId = useSelector(selectEffectivePrevSiblingId);
  const nextSiblingId = useSelector(selectEffectiveNextSiblingId);
  const historical = useSelector((state) => state.document.historical);

  // Additional selectors for specific operations
  const mainDocument = useSelector((state) => state.document.mainDocument);
  const activeDocument = useSelector((state) => state.document.activeDocument);
  const mutations = useSelector((state) => state.document.mutations);
  const userAccount = useSelector((state) => state.user.userAccount);

  // Derived values
  const isMutationActive = selectedMutationId !== 'original';

  // Helper function to determine if all document parts will be processed after current action
  const shouldAutoNavigateAfterAction = useCallback(() => {
    if (!mainDocument) return false;

    // If no mutations exist, can navigate immediately
    if (!mutations || mutations.length === 0) return true;

    // If mutations exist, check if ALL parts will be processed after this action
    if (selectedMutationId === 'original') {
      // We're acting on the main document - check if all mutations already have actions
      const mutationsWithoutAction = mutations.find((m) => !m.action);
      return mutationsWithoutAction == null; // All mutations already have actions
    }
    // We're acting on a mutation - check if main doc and all other mutations have actions
    const mainDocHasAction = !!mainDocument.action;
    const otherMutationsWithoutAction = mutations.find((m) => m.id !== selectedMutationId && !m.action);
    return mainDocHasAction && otherMutationsWithoutAction == null;
  }, [mainDocument, mutations, selectedMutationId]);

  // Action: Set active page
  const setActivePageWithReset = useCallback(
    (pageNo: number) => {
      if (!pageNo || !pageIndexMap || pageIndexMap.length === 0) return;

      const boundary = pageIndexMap[pageIndexMap.length - 1];
      if (!boundary) return;

      const boundPageNo = Math.max(0, Math.min(boundary, pageNo));
      if (boundPageNo !== activePageNo) {
        dispatch(setIsViewerLoaded(false));
        dispatch(setActivePage(boundPageNo));
      }
    },
    [activePageNo, pageIndexMap, dispatch],
  );

  // Action: Navigation
  const handleNavNext = useCallback(() => {
    console.log(nextSiblingId);
    if (nextSiblingId) {
      let path = `/inbox/${inboxId}/documents/${nextSiblingId}`;
      console.log(path);

      if (historical) path = `/inbox/${inboxId}/historical/${nextSiblingId}`;
      navigate(path);
    } else {
      navigate(`/inbox/${inboxId}`);
    }
  }, [inboxId, nextSiblingId, historical, navigate]);

  const handleNavPrev = useCallback(() => {
    if (prevSiblingId) {
      let path = `/inbox/${inboxId}/documents/${prevSiblingId}`;
      if (historical) path = `/inbox/${inboxId}/historical/${prevSiblingId}`;
      navigate(path);
    } else {
      navigate(`/inbox/${inboxId}`);
    }
  }, [inboxId, prevSiblingId, historical, navigate]);

  // Action: Get document PDF
  const getDocumentPDF = useCallback(
    (topologyId?: string) => {
      if (!mainDocId || !inboxId) return Promise.reject('Missing document or inbox ID');
      const mutationId = isMutationActive ? selectedMutationId : undefined;
      return getRawPDF(inboxId, mainDocId, mutationId, topologyId);
    },
    [inboxId, mainDocId, selectedMutationId, isMutationActive],
  );
  const unLinkMasterdata = useCallback(() => {
    if (!mainDocId || !inboxId) return Promise.reject('Missing document or inbox ID');
    const mutationId = isMutationActive ? selectedMutationId : undefined;
    return unlinkDocument(inboxId, mainDocId, mutationId);
  }, [inboxId, mainDocId, selectedMutationId, isMutationActive]);

  // Action: Add entity
  const addEntity = useCallback(
    async (entityData: IDocumentEntity) => {
      if (!mainDocId || !inboxId || !activeDocument || !mainDocument) {
        return Promise.reject('Missing required data');
      }

      const newEntity: IDocumentEntity = { ...entityData };
      const id = newEntity.id ?? uuid4hex();

      // Create the entity
      const normalizedEntity = normalizeEntity(newEntity, mainDocument);
      const entityPayload = entityToRawPayload(normalizedEntity);

      // Add to pending operations
      dispatch(
        addPendingOperation({
          type: 'add',
          entityId: id,
          timestamp: Date.now(),
          data: normalizedEntity,
        }),
      );

      // Optimistic update using RTK Query
      let optimisticUpdateResult;
      if (isMutationActive) {
        optimisticUpdateResult = dispatch(
          firestoreApi.util.updateQueryData('getDocumentMutations', { docId: mainDocId }, (draft: any[]) => {
            const mutationIndex = draft.findIndex((m) => m.id === selectedMutationId);
            if (mutationIndex !== -1) {
              if (!draft[mutationIndex].entities) draft[mutationIndex].entities = [];
              draft[mutationIndex].entities.push(normalizedEntity);
            }
          }),
        );
      } else {
        optimisticUpdateResult = dispatch(
          firestoreApi.util.updateQueryData('getDocument', { docId: mainDocId }, (draft: any) => {
            if (!draft.entities) draft.entities = [];
            draft.entities.push(normalizedEntity);
          }),
        );
      }

      try {
        const entityPostPayload: IDocumentEntityPostPayload = {
          pageNo: entityPayload.pageNo,
          rawValue: entityPayload.rawValue ?? null,
          value: entityPayload.value,
          type: entityPayload.type,
          valueLocations: entityPayload.valueLocations,
        };

        await addEntityToDocument(
          inboxId,
          mainDocId,
          id,
          entityPostPayload,
          isMutationActive ? selectedMutationId : undefined,
        );

        return { success: true, entityId: id };
      } catch (error) {
        // Rollback optimistic update on error
        optimisticUpdateResult.undo();

        dispatch(removePendingOperation({ entityId: id }));
        console.error('Failed to add entity:', error);
        throw error;
      }
    },
    [dispatch, inboxId, mainDocId, selectedMutationId, isMutationActive, activeDocument, mainDocument],
  );

  // Action: Edit entity
  const editEntity = useCallback(
    async (details: ActiveEntityPair, entityData: Partial<IDocumentEntity>) => {
      if (!mainDocId || !inboxId || !activeDocument || !mainDocument) {
        return Promise.reject('Missing required data');
      }

      const { entityId, childId } = details;
      const existing = activeDocument.entities?.find((e) => e.id === entityId);

      if (!existing) {
        console.error('Entity not found:', entityId);
        return Promise.reject('Entity not found');
      }

      // Prepare updated entity
      let updatedEntity;
      if (childId) {
        const complexValue = cloneDeep(existing.value) as IDocumentEntityComplexValue;
        if (complexValue?.complex) {
          complexValue.complex[childId] = { ...complexValue.complex[childId], ...entityData };
          updatedEntity = { ...existing, value: complexValue };
        } else {
          updatedEntity = existing;
        }
      } else {
        updatedEntity = { ...existing, ...entityData };
      }

      const normalizedEntity = normalizeEntity(updatedEntity, mainDocument);
      const entityPayload = entityToRawPayload(normalizedEntity);

      // Add to pending operations
      dispatch(
        addPendingOperation({
          type: 'edit',
          entityId,
          childId,
          timestamp: Date.now(),
          data: entityPayload,
          originalEntity: cloneDeep(existing),
        }),
      );

      // Optimistic update using RTK Query
      let optimisticUpdateResult;
      if (isMutationActive) {
        optimisticUpdateResult = dispatch(
          firestoreApi.util.updateQueryData('getDocumentMutations', { docId: mainDocId }, (draft: any[]) => {
            const mutationIndex = draft.findIndex((m) => m.id === selectedMutationId);
            if (mutationIndex !== -1) {
              const existingIndex = draft[mutationIndex].entities.findIndex((e) => e.id === entityId);
              if (existingIndex !== -1) {
                draft[mutationIndex].entities[existingIndex] = entityPayload;
              }
            }
          }),
        );
      } else {
        optimisticUpdateResult = dispatch(
          firestoreApi.util.updateQueryData('getDocument', { docId: mainDocId }, (draft: any) => {
            const existingIndex = draft.entities.findIndex((e) => e.id === entityId);
            if (existingIndex !== -1) {
              draft.entities[existingIndex] = entityPayload;
            }
          }),
        );
      }

      try {
        // Create properly typed patch payload
        const entityDataToSend: IDocumentEntityPatchPayload = childId
          ? { value: { complex: entityPayload.value.complex } }
          : Object.keys(entityData).reduce((acc, key) => {
              acc[key] = entityPayload[key];
              return acc;
            }, {} as IDocumentEntityPatchPayload);

        await editEntityInDocument(
          inboxId,
          mainDocId,
          entityId,
          entityDataToSend,
          isMutationActive ? selectedMutationId : undefined,
        );

        return { success: true };
      } catch (error) {
        // Rollback optimistic update on error
        optimisticUpdateResult.undo();

        dispatch(removePendingOperation({ entityId, childId }));
        console.error('Failed to edit entity:', error);
        throw error;
      }
    },
    [dispatch, inboxId, mainDocId, selectedMutationId, isMutationActive, activeDocument, mainDocument],
  );

  // Action: Delete entity
  const deleteEntity = useCallback(
    async (details: ActiveEntityPair) => {
      if (!mainDocId || !inboxId || !activeDocument) {
        return Promise.reject('Missing required data');
      }

      const { entityId, childId } = details;
      const existing = activeDocument.entities?.find((e) => e.id === entityId);

      if (!existing) {
        console.error('Entity not found:', entityId);
        return Promise.reject('Entity not found');
      }

      // Add to pending operations
      dispatch(
        addPendingOperation({
          type: 'delete',
          entityId,
          childId,
          timestamp: Date.now(),
          data: existing,
          originalEntity: cloneDeep(existing),
        }),
      );

      // Optimistic update using RTK Query
      let optimisticUpdateResult;
      if (childId) {
        // For complex field - prepare the updated entity with empty child
        const complexValue = cloneDeep(existing.value) as IDocumentEntityComplexValue;
        if (complexValue?.complex?.[childId]) {
          const child = complexValue.complex[childId];
          complexValue.complex[childId] = {
            id: childId,
            pageNo: null,
            rawValue: null,
            value: null,
            confidence: 0,
            type: child.type,
            valueLocations: [],
            source: 'user',
          };

          // const updatedEntity = { ...existing, value: complexValue };
          // const entityPayload = entityToRawPayload(normalizeEntity(updatedEntity, mainDocument));

          if (isMutationActive) {
            optimisticUpdateResult = dispatch(
              firestoreApi.util.updateQueryData(
                'getDocumentMutations',
                { docId: mainDocId },
                (draft: any[]) => {
                  const mutationIndex = draft.findIndex((m) => m.id === selectedMutationId);
                  if (mutationIndex !== -1) {
                    const existingIndex = draft[mutationIndex].entities.findIndex((e) => e.id === entityId);
                    if (existingIndex !== -1) {
                      draft[mutationIndex].entities[existingIndex].value = complexValue;
                    }
                  }
                },
              ),
            );
          } else {
            optimisticUpdateResult = dispatch(
              firestoreApi.util.updateQueryData('getDocument', { docId: mainDocId }, (draft: any) => {
                const existingIndex = draft.entities.findIndex((e) => e.id === entityId);
                if (existingIndex !== -1) {
                  draft.entities[existingIndex].value = complexValue;
                }
              }),
            );
          }
        }
      } else {
        // For regular entity - remove it entirely
        if (isMutationActive) {
          optimisticUpdateResult = dispatch(
            firestoreApi.util.updateQueryData(
              'getDocumentMutations',
              { docId: mainDocId },
              (draft: any[]) => {
                const mutationIndex = draft.findIndex((m) => m.id === selectedMutationId);
                if (mutationIndex !== -1) {
                  draft[mutationIndex].entities = draft[mutationIndex].entities.filter(
                    (e) => e.id !== entityId,
                  );
                }
              },
            ),
          );
        } else {
          optimisticUpdateResult = dispatch(
            firestoreApi.util.updateQueryData('getDocument', { docId: mainDocId }, (draft: any) => {
              draft.entities = draft.entities.filter((e) => e.id !== entityId);
            }),
          );
        }
      }

      try {
        if (childId) {
          // For complex field - clear the child value but keep the field
          const complexValue = cloneDeep(existing.value) as IDocumentEntityComplexValue;
          if (complexValue?.complex?.[childId]) {
            const child = complexValue.complex[childId];
            const emptyDefaultEntity = {
              id: childId,
              pageNo: null,
              rawValue: null,
              value: null,
              confidence: 0,
              type: child.type,
              valueLocations: [],
              source: 'user',
            };
            complexValue.complex[childId] = emptyDefaultEntity;

            await editEntityInDocument(
              inboxId,
              mainDocId,
              entityId,
              { value: complexValue },
              isMutationActive ? selectedMutationId : undefined,
            );
          }
        } else {
          await removeEntityFromDocument(
            inboxId,
            mainDocId,
            entityId,
            isMutationActive ? selectedMutationId : undefined,
          );
        }

        return { success: true };
      } catch (error) {
        // Rollback optimistic update on error
        if (optimisticUpdateResult) {
          optimisticUpdateResult.undo();
        }

        dispatch(removePendingOperation({ entityId, childId }));
        console.error('Failed to delete entity:', error);
        throw error;
      }
    },
    [dispatch, inboxId, mainDocId, selectedMutationId, isMutationActive, activeDocument, mainDocument],
  );

  const validateDoc = useCallback(async () => {
    if (!mainDocId || !inboxId) {
      return Promise.reject('Missing required data');
    }
    try {
      await validateDocument(inboxId, mainDocId, isMutationActive ? selectedMutationId : undefined);
    } catch (error) {
      console.error('Failed to validate document:', error);
      throw error;
    }
  }, [inboxId, mainDocId, selectedMutationId, isMutationActive]);

  // Action: Create mutation
  const createMutation = useCallback(
    async (partsToCopy?: string[]) => {
      if (!mainDocId || !inboxId || !activeDocument) {
        return Promise.reject('Missing required data');
      }

      try {
        const response = await createDocumentMutation(inboxId, mainDocId);

        if (!response.data.id) {
          throw new Error('No mutation ID returned');
        }

        const mutationId = response.data.id;

        if (!partsToCopy) {
          dispatch(setSelectedMutationId(mutationId));
        }

        if (activeDocument.topology?.parts) {
          const partsMap: Record<string, any> = {};

          activeDocument.topology.parts.forEach((p) => {
            partsMap[p.id] = { ...p, archived: !partsToCopy?.includes(p.id) };
            delete partsMap[p.id].id;
          });

          // Reassign IDs
          Object.keys(partsMap).forEach((key) => {
            const { id, ...rest } = partsMap[key];
            partsMap[key] = { ...rest, id: key };
          });

          // Build the new topology object
          const topology = {
            parts: Object.values(partsMap).reduce(
              (acc, currPart) => {
                acc[currPart.id] = convertPartToRaw(currPart);
                return acc;
              },
              {} as Record<string, any>,
            ),
          };

          await patchDocument(inboxId, { topology }, mainDocId, mutationId);
          dispatch(setSelectedMutationId(mutationId));
        }

        return response;
      } catch (error) {
        console.error('Failed to create mutation:', error);
        throw error;
      }
    },
    [dispatch, inboxId, mainDocId, activeDocument],
  );

  // Action: Delete document
  const deleteDoc = useCallback(async () => {
    if (!mainDocId || !inboxId || !userAccount?.email) {
      return Promise.reject('Missing required data');
    }

    try {
      if (isMutationActive) {
        // Deleting a mutation - switch to next unprocessed part
        await deleteMutation(inboxId, mainDocId, selectedMutationId);

        const mutationWithoutAction = mutations.find((m) => !m.action && m.id !== selectedMutationId);
        if (mutationWithoutAction) {
          dispatch(setSelectedMutationId(mutationWithoutAction.id));
        } else {
          dispatch(setSelectedMutationId('original'));
        }
      } else {
        // Deleting the main document - this deletes the entire document structure
        await deleteDocument(inboxId, mainDocId, userAccount.email);

        // Always auto-navigate after deleting the main document
        setTimeout(() => {
          handleNavNext();
        }, 100); // Small delay to ensure the action is processed
      }

      return { success: true };
    } catch (error) {
      console.error('Failed to delete document:', error);
      throw error;
    }
  }, [
    dispatch,
    inboxId,
    mainDocId,
    selectedMutationId,
    isMutationActive,
    mutations,
    userAccount,
    handleNavNext,
  ]);

  // Action: Patch document
  const patchDoc = useCallback(
    async (payload: Record<string, any>) => {
      if (!mainDocId || !inboxId) {
        return Promise.reject('Missing required data');
      }

      const mutationId = isMutationActive ? selectedMutationId : undefined;

      try {
        return await patchDocument(inboxId, payload, mainDocId, mutationId);
      } catch (error) {
        console.error('Failed to patch document:', error);
        throw error;
      }
    },
    [inboxId, mainDocId, selectedMutationId, isMutationActive],
  );

  // Action: Delete topology part
  const deleteTopologyPart = useCallback(
    async (partId: string) => {
      if (!mainDocId || !inboxId) {
        return Promise.reject('Missing required data');
      }

      try {
        const mutationId = isMutationActive ? selectedMutationId : undefined;

        await patchTopologyPart({
          inboxId,
          partId,
          documentId: mainDocId,
          mutationId,
          payload: { archived: true },
        });

        return { success: true };
      } catch (error) {
        console.error('Failed to delete topology part:', error);
        throw error;
      }
    },
    [inboxId, mainDocId, selectedMutationId, isMutationActive],
  );

  // Action: Import masterdata result
  const importMasterdataResult = useCallback(
    async (result: any) => {
      if (!mainDocId || !inboxId) {
        return Promise.reject('Missing required data');
      }

      try {
        const mutationId = isMutationActive ? selectedMutationId : undefined;

        return await importMasterDataResult(inboxId, result, mainDocId, mutationId);
      } catch (error) {
        console.error('Failed to import masterdata result:', error);
        throw error;
      }
    },
    [inboxId, mainDocId, selectedMutationId, isMutationActive],
  );

  // Action: Bounce document
  const bounceDoc = useCallback(
    async (value: Record<string, any>) => {
      if (!mainDocId || !inboxId || !userAccount?.email) {
        return Promise.reject('Missing required data');
      }

      try {
        await bounceDocument(
          value,
          userAccount.email,
          inboxId,
          mainDocId,
          isMutationActive ? selectedMutationId : undefined,
        );

        // Auto-navigate only if all document parts are processed
        setTimeout(() => {
          if (shouldAutoNavigateAfterAction()) {
            handleNavNext();
          } else {
            // Switch to next unprocessed mutation or main document
            const mutationWithoutAction = mutations.find((m) => !m.action && m.id !== selectedMutationId);
            if (mutationWithoutAction) {
              dispatch(setSelectedMutationId(mutationWithoutAction.id));
            } else if (!mainDocument?.action) {
              dispatch(setSelectedMutationId('original'));
            }
          }
        }, 100); // Small delay to ensure the action is processed

        return { success: true };
      } catch (error) {
        console.error('Failed to bounce document:', error);
        throw error;
      }
    },
    [
      inboxId,
      mainDocId,
      selectedMutationId,
      isMutationActive,
      userAccount,
      handleNavNext,
      shouldAutoNavigateAfterAction,
      mutations,
      mainDocument,
      dispatch,
    ],
  );

  // Action: Change document type
  const changeDocType = useCallback(
    async (docTypeId: string, subtypeId?: string, partId?: string, changeAll?: boolean) => {
      if (!mainDocId || !inboxId) {
        return Promise.reject('Missing required data');
      }

      try {
        const docsToChange = changeAll ? [mainDocId, ...mutations.map((m) => m.id)] : [mainDocId];

        return await Promise.all(
          docsToChange.map((docId) =>
            changeDocumentDoctype(
              inboxId,
              mainDocId,
              docTypeId,
              subtypeId,
              docId === mainDocId ? null : docId,
              partId,
            ),
          ),
        );
      } catch (error) {
        console.error('Failed to change document type:', error);
        throw error;
      }
    },
    [inboxId, mainDocId, mutations],
  );

  // Action: Change inbox
  const changeInbox = useCallback(
    async (newInboxId: string) => {
      if (!mainDocId || !inboxId) {
        return Promise.reject('Missing required data');
      }

      try {
        return await changeDocumentInbox(mainDocId, inboxId, newInboxId);
      } catch (error) {
        console.error('Failed to change inbox:', error);
        throw error;
      }
    },
    [mainDocId, inboxId],
  );

  // Action: Reset topology
  const resetTopology = useCallback(async () => {
    if (!mainDocId || !inboxId || !activeDocument?.topology?.parts) {
      return Promise.reject('Missing required data');
    }

    try {
      // Unarchive every part and its pages
      const unarchivePart = (part: any) => ({
        ...part,
        archived: false,
        pages: part.pages.map((page: any) => ({ ...page, archived: false })),
      });

      const unarchivedParts = activeDocument.topology.parts.map(unarchivePart);
      const uniqueParts = unarchivedParts.filter(
        (part, index, parts) => parts.findIndex((p) => p.contentId === part.contentId) === index,
      );

      const partsMap: Record<string, any> = {};
      uniqueParts.forEach((part) => {
        const { id, ...rest } = part;
        partsMap[id] = { ...rest };
      });

      Object.keys(partsMap).forEach((key) => {
        partsMap[key].id = key;
      });

      const topology = {
        parts: Object.values(partsMap).reduce(
          (acc, currPart) => {
            acc[currPart.id] = convertPartToRaw(currPart);
            return acc;
          },
          {} as Record<string, any>,
        ),
      };

      const mutationId = isMutationActive ? selectedMutationId : undefined;
      return await patchDocument(inboxId, { topology }, mainDocId, mutationId);
    } catch (error) {
      console.error('Error during reset topology:', error);
      throw error;
    }
  }, [inboxId, mainDocId, selectedMutationId, isMutationActive, activeDocument]);

  // Action: Approve document
  const approveDoc = useCallback(async () => {
    if (!mainDocId || !inboxId || !userAccount?.email) {
      return Promise.reject('Missing required data');
    }

    try {
      const mutationId = isMutationActive ? selectedMutationId : undefined;
      const result = await patchDocument(
        inboxId,
        { action: { actor_email: userAccount.email, type: 'approve' } },
        mainDocId,
        mutationId,
      );

      // Auto-navigate only if all document parts are processed
      setTimeout(() => {
        if (shouldAutoNavigateAfterAction()) {
          handleNavNext();
        } else {
          // Switch to next unprocessed mutation or main document
          const mutationWithoutAction = mutations.find((m) => !m.action && m.id !== selectedMutationId);
          if (mutationWithoutAction) {
            dispatch(setSelectedMutationId(mutationWithoutAction.id));
          } else if (!mainDocument?.action) {
            dispatch(setSelectedMutationId('original'));
          }
        }
      }, 100); // Small delay to ensure the action is processed

      return result;
    } catch (error) {
      console.error('Failed to approve document:', error);
      throw error;
    }
  }, [
    inboxId,
    mainDocId,
    selectedMutationId,
    isMutationActive,
    userAccount,
    handleNavNext,
    shouldAutoNavigateAfterAction,
    mutations,
    mainDocument,
    dispatch,
  ]);

  // Action: Reject document
  const rejectDoc = useCallback(async () => {
    if (!mainDocId || !inboxId || !userAccount?.email) {
      return Promise.reject('Missing required data');
    }

    try {
      const mutationId = isMutationActive ? selectedMutationId : undefined;
      const result = await patchDocument(
        inboxId,
        { action: { actor_email: userAccount.email, type: 'reject' } },
        mainDocId,
        mutationId,
      );

      // Auto-navigate only if all document parts are processed
      setTimeout(() => {
        if (shouldAutoNavigateAfterAction()) {
          handleNavNext();
        } else {
          // Switch to next unprocessed mutation or main document
          const mutationWithoutAction = mutations.find((m) => !m.action && m.id !== selectedMutationId);
          if (mutationWithoutAction) {
            dispatch(setSelectedMutationId(mutationWithoutAction.id));
          } else if (!mainDocument?.action) {
            dispatch(setSelectedMutationId('original'));
          }
        }
      }, 100); // Small delay to ensure the action is processed

      return result;
    } catch (error) {
      console.error('Failed to reject document:', error);
      throw error;
    }
  }, [
    inboxId,
    mainDocId,
    selectedMutationId,
    isMutationActive,
    userAccount,
    handleNavNext,
    shouldAutoNavigateAfterAction,
    mutations,
    mainDocument,
    dispatch,
  ]);

  // Action: Set selected mutation ID
  const setSelectedMutationIdAction = useCallback(
    (mutationId: string) => {
      dispatch(setSelectedMutationId(mutationId));
    },
    [dispatch],
  );

  // Action: Set is viewer loaded
  const setIsViewerLoadedAction = useCallback(
    (isLoaded: boolean) => {
      dispatch(setIsViewerLoaded(isLoaded));
    },
    [dispatch],
  );

  // Return all document operations
  return {
    // Navigation and UI
    setActivePage: setActivePageWithReset,
    handleNavNext,
    handleNavPrev,
    getDocumentPDF,
    setSelectedMutationId: setSelectedMutationIdAction,
    setIsViewerLoaded: setIsViewerLoadedAction,

    // Entity operations
    addEntity,
    editEntity,
    deleteEntity,

    // Document operations
    createMutation,
    deleteDoc,
    patchDoc,
    deleteTopologyPart,
    importMasterdataResult,
    bounceDoc,
    changeDocType,
    changeInbox,
    resetTopology,
    approveDoc,
    rejectDoc,
    validateDoc,
    unLinkMasterdata,
  };
}
