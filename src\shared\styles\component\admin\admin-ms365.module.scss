@import "../../vars/_vars";


.wrapper {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  padding: 0;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: white;
}


.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 85px;
  padding: 25px;
  cursor: pointer;
  gap: 14px;
}


.content {
  display: none;

  &__visible {
    display: block;
  }
}


.sectionWarning {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 85px;
  padding: 25px;
  cursor: pointer;
  gap: 14px;
}


.headerIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  cursor: pointer;
  color: #989898;
  border-radius: 50px;
  flex-shrink: 0;
  background: #EEEEEE;
}


.headerText {
  display: flex;
  flex-direction: column;
  margin-right: auto;
  margin-left: 0;

  gap: 6px;

  h3 {
    font-size: 16px;
    font-weight: 500;
  }

  span {
    font-size: 14px;
    color: #898B99;
  }
}


.headerArrow {
  cursor: pointer;
}


.section {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  padding: 25px;
  border-top: 1px solid #EEEEEE;
  gap: 20px;

}


.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 10px;
  gap: 14px;

  h3 {
    font-size: 16px;
    font-weight: 500;
    margin-right: auto;
    margin-left: 0;

  }

  button {
    font-size: 14px;
    color: $paperbox-blue;

  }
}


.outbound {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px;

}


.sectionContent {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 6px;
}


.row {
  display: grid;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  grid-template-columns: 45% 1fr 45%;
  gap: 14px;

  &> :first-child {
    justify-self: start;
  }

  &> :nth-child(2) {
    justify-self: center;
  }

  &> :last-child {
    justify-self: end;
  }
}


.selector {

  span {
    text-align: start;
  }
}


.outboundRow {

  .name {
    font-size: 15px;
    font-weight: 500;
    width: 250px;
  }

  .selector {
    font-size: 14px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 300px;
    height: 37px;
    padding: 12px;

    border: 1px solid #EEEEEE;
    border-radius: 5px;
    background: #FCFBFC;

    svg {
      color: #898B99;
    }

    input {
      position: absolute;
      z-index: -1;
      tab-index: -1;
    }

    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

    }

  }
}


.inbound {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 20px;

}


.inboundRow {

  .mapping {
    display: flex;
    flex-direction: column;
    gap: 20px;

  }

  .mappingItem {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 20px;
  }

  .selector {
    font-size: 15px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 300px;
    height: 100%;
    padding: 12px;
    border: 1px solid #EEEEEE;
    border-radius: 5px;
    background: #FCFBFC;

    input {
      position: absolute;
      z-index: -1;
      tab-index: -1;
    }

    .icons {
      display: flex;
      align-items: center;
      gap: 10px;

      svg {
        width: auto;
        height: 14px;
        color: #898B99;

        &:hover {
          cursor: pointer;
          color: $paperbox-blue;
        }
      }

      .delete {
        transform: scale(1.1);

        &:hover {
          cursor: pointer;
          color: $error;
        }

      }
    }

  }
}


.userPicker,
.emailPicker {
  flex-direction: column;
  width: 60vw;
  min-width: 400px;
  max-width: 90vw;
  border-radius: 10px;
  background: $white;
  box-shadow: 0 2px 15px rgba(0, 13, 33, 0.30);

}


.emailPicker {
  flex-grow: 1;
  height: auto;
  max-height: 600px;
}


.userPickerHeader,
.emailPickerHeader {
  font-size: 16px;
  padding: 20px;
}


.userPickerBody {
  display: grid;
  border-top: 1px solid #EEEEEE;
  border-bottom: 1px solid #EEEEEE;
  background: #EEEEEE;
  grid-template-columns: repeat(4, 1fr);
  gap: 1px;

  .column {
    display: flex;
    overflow: auto;
    flex-direction: column;
    height: 100%;
    min-height: 400px;
    max-height: 600px;
    padding: 20px;
    background: white;
    gap: 3px;
  }

  .columnItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    //height: 30px;
    padding: 8px 10px;
    text-align: start;
    border-radius: 5px;

    svg {
      flex-shrink: 0;
      height: 18px;
    }

    &__active {
      color: $white;
      background: $paperbox-blue;
    }

    &__disabled {
      cursor: not-allowed;
      opacity: 0.8;
      color: $medium-gray;
    }

    &:hover {
      cursor: pointer;

      color: $white;
      background: $paperbox-blue-light;
    }
  }

}


.userPickerFooter {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 20px;
  gap: 10px;
}


.emailPickerBody {
  display: flex;
  align-items: stretch;
  height: 480px;
  border-top: 1px solid #EEEEEE;
  border-bottom: 1px solid #EEEEEE;
  background: #EEEEEE;
  gap: 1px;

  .column {
    display: flex;
    flex-direction: column;
    width: 100%;
    background: white;
    gap: 3px;
  }

  .columnItems {
    display: flex;
    overflow: auto;
    flex-direction: column;
    width: 100%;
    height: 500px;
    background: white;
    gap: 3px;
  }

  .columnHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    border-bottom: 1px solid #EEEEEE;

    b {
      font-size: 14px;
      font-weight: 500;
    }

    button {
      font-size: 14px;
      color: $paperbox-blue;
    }
  }

  .columnItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 30px;
    padding: 10px 20px;

    svg {
      height: 18px;
    }

    &__flipped {
      flex-direction: row-reverse;
    }

    &:hover {
      cursor: pointer;
      color: $paperbox-blue;
    }
  }
}