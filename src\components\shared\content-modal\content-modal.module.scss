@import "src/shared/styles/vars/_vars";

.modal {
  display: flex;
  flex-direction: column;
  width: auto;
  color: $font-color-black;
  border-radius: 7px;
  background-color: $white;
  min-width: 400px;
}

.title {
  font-size: 18px;
  font-weight: 600;
  font-family: $headings-font;
}

.header {
  display: flex;
  justify-content: space-between;
  padding: 35px 0px 25px 0px;
  border-bottom: 1px solid #eeeeee;
  margin-inline: 25px;
}

.content {
  padding: 10px 25px 25px 25px;
  display: flex;
  flex-direction: column;
}

.close {
  cursor: pointer;
  color: $font-color-black;
  width: 20px;
  height: 20px;

  &:hover {
    color: $error;
  }
}

.description {
  margin-top: 10px;
  font-size: 15px;
  line-height: 22px;
  color: #666A72;
  white-space: pre-wrap;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 20px;
  gap: 10px;
  --btn-color: #{$paperbox-blue};
}

.button {
  color: white;
  border: 1px solid #EEEEEE;
  background-color: var(--btn-color);
  border-radius: 7px;
  padding: 0 32px;
  height: 40px;
  min-width: 100px;
  font-size: 14px;
  font-weight: 700;
  transition: transform 0.15s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;

  &__alt {
    background-color: $white;
    color: var(--btn-color);
    border: 1px solid var(--btn-color);
  }
  &:hover{
    opacity: 0.7;
    outline: none;
    box-shadow: inset 0 2px 8px rgba(var(--btn-color), 0.15);

  }
  &:focus{
    opacity: 0.9;
    outline: none;

  }
  &:disabled{
    opacity: .5;
  }
}
.footer__warning {
  @extend .footer;
  --btn-color: #{$error};

}
