import InboxSidebarNew from '@components/inbox/sidebar/InboxSidebarNew.tsx';
import SuspenseLoader from '@components/shared/suspense-loader/SuspenseLoader';
import { useGetActiveInboxQuery } from '@shared/helpers/rtk-query/firestoreApi.ts';
import useFilterSync from '@shared/hooks/useFilterSync.ts';
import { UrlParams } from '@shared/models/generic';
import { dashboardSlice } from '@shared/store/dashboardSlice.ts';
import s from '@shared/styles/component/inbox/inbox.module.scss';
import React, { useEffect } from 'react';
import { Outlet, useNavigate, useParams } from 'react-router';

import { useDispatch, useSelector } from '../shared/store/store';

const HomeContainerNew: React.FC = () => {
  const { inboxId }: UrlParams = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const userAccount = useSelector((state) => state.user.userAccount);
  const inboxes = useSelector((state) => state.user.inboxes);

  const isInboxesLoading = useSelector((state) => state.user.isInboxesLoading);
  useGetActiveInboxQuery({ inboxId }, { skip: !inboxId });

  // Use filter sync but disable auto-sync to prevent interference during navigation
  // The individual document components will handle their own filter syncing
  useFilterSync({ autoSync: false });

  useEffect(() => {
    if (!isInboxesLoading && inboxes?.length === 0 && userAccount?.isAdmin) {
      dispatch(dashboardSlice.actions.setShowTenantOnboarding(true));
      navigate('/onboarding');
    }
  }, [isInboxesLoading, inboxes, dispatch, navigate, userAccount]);

  if (!userAccount.email) return <SuspenseLoader name={'home-placeholder-01'} fullPage />;

  return (
    <div data-testid={'home-grid'} className={s.grid}>
      <div
        style={{
          willChange: 'width, minWidth',
          display: 'flex',
          width: 300,
          minWidth: 300,
        }}
      >
        <InboxSidebarNew />
      </div>
      <div style={{ display: 'flex', flex: 1, minWidth: 0, flexDirection: 'column', height: '100vh' }}>
        <Outlet />
      </div>
    </div>
  );
};

export default HomeContainerNew;
