// Define shared types for tenant onboarding components

export interface InboxItem {
  id: string;
  name: string;
  type: string;
  language: string;
}

export interface AdditionalInfo {
  // Brio fields
  office_id?: string;
  sub_office_id?: string;
  pi2_key?: string;
  environment?: string;
  recipient_default_service?: string;

  // CCS fields
  url?: string;
  username?: string;
  password?: string;
  account?: string;
}

export interface OnboardingState {
  system?: string;
  connector?: string;
  inboxes?: InboxItem[];
  additionalInfo?: AdditionalInfo;
}

export interface TemplateInfo {
  languages: string[];
  connector: string;
}

export interface SystemTemplates {
  [key: string]: {
    [key: string]: TemplateInfo;
  };
}

export type SystemOptions = {
  label: string;
  value: string;
  description: string;
}[];

// Helper function to create connector payload based on system and additional info
export function createConnectorPayload(
  connector: string,
  additionalInfo?: AdditionalInfo,
): Record<string, any> {
  if (!connector) return {};

  if (connector === 'brio') {
    const { environment, office_id, sub_office_id, pi2_key } = additionalInfo || {};
    return {
      type: 'portimabrio',
      environment,
      office_id,
      sub_office_id,
      pi2_key,
      name: 'Brio Connector',
    };
  }

  if (connector === 'ccs') {
    const { url, account, username, password } = additionalInfo || {};
    return {
      type: 'ccs',
      url,
      account,
      username,
      password: password ? { '.psv': '@PB_SECRET', secret: password } : undefined,
    };
  }

  // Handle 'other' connector type
  return {
    type: 'custom',
    name: 'Custom Connector',
  };
}

export function createEndpointPayload(additionalInfo?: AdditionalInfo): Record<string, any> {
  if (additionalInfo.recipient_default_service) {
    return {
      inbox_mapping: {
        _INBOX_ID_: {
          activity_settings: {
            recipient_default_service: additionalInfo.recipient_default_service,
          },
        },
      },
    };
  }
}

// Create a template type string from system, inbox type and language
export function createTemplateType(system: string, inboxType: string, language: string): string {
  return `${system}_${inboxType}_${language}`;
}
