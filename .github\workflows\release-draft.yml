name: Release Draft
on:
  push:
    branches:
      - master
  pull_request:
    types: [opened, reopened, synchronize]
  workflow_dispatch:

jobs:
  update_release_draft:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
    steps:
      - uses: release-drafter/release-drafter@v5
        with:
          disable-autolabeler: true
          prerelease: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  pr_history:
    name: Pull Request Body
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Pull Request Body
        uses: paperboxai/Github-Action-PR-Commit-Changelog@v1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
