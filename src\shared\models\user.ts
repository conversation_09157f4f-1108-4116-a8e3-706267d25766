export interface UserAccount {
  email: string;
  inboxes: string[];
  isAdmin: boolean;
  id?: string;
  isHidden?: boolean;
  isLabeler?: boolean;
  preferences?: UserPreferences;
}
export interface UserPreferences {
  language?: string;
  emailPreferenceSet?: boolean;
}

export interface Provider {
  type: string;
  idpEntityId: string;
  name: string;
}

export interface NoticeInfo {
  message: string;
  priority: number;
}
