# Netlify Deployment with GitHub Releases

This document provides instructions for setting up Netlify deployments triggered by GitHub releases.

## Overview

Our deployment strategy uses:
- **GitHub Releases** for versioning and release management
- **Two deployment branches**:
  - `pre-release`: For pre-release deployments (TST and ACC)
  - `release`: For published release deployments (TST, ACC, and PRD)
- **Beta Branch** for split deployments across all environments

## Setup Instructions

### 1. Create Netlify Sites

Create three separate Netlify sites:

1. **paperbox-tst** (Test Environment)
   - Connect to GitHub repository
   - Set branches to deploy: `pre-release` and `release`
   - Build command: `yarn run build`
   - Publish directory: `build`
   - Environment variables: Set for test environment

2. **paperbox-acc** (Acceptance Environment)
   - Connect to GitHub repository
   - Set branches to deploy: `pre-release` and `release`
   - Build command: `yarn run build`
   - Publish directory: `build`
   - Environment variables: Set for acceptance environment

3. **paperbox-prd** (Production Environment)
   - Connect to GitHub repository
   - Set branch to deploy: `release` only
   - Build command: `yarn run build`
   - Publish directory: `build`
   - Environment variables: Set for production environment

### 2. Configure Beta Branch for Split Deployments

For each Netlify site:

1. Go to Site settings > Build & deploy > Continuous deployment
2. Under "Deploy contexts", configure the beta branch
3. Ensure the beta branch is set to deploy to all three sites

### 3. Add Secrets to GitHub Repository

Add the following secrets to your GitHub repository:

1. `SENTRY_AUTH_TOKEN` - Your Sentry authentication token
2. `SENTRY_ORG` - Your Sentry organization name

## Deployment Process

### Release-Based Deployments

1. **Creating Releases**:
   - When PRs are merged to `master`, a draft release is automatically created/updated
   - Draft releases do not trigger any deployments
   - When you're ready to deploy to TST and ACC, create a pre-release on GitHub
   - When you're ready to deploy to all environments (TST, ACC, and PRD), publish the release

2. **Deployment Process**:
   - When a pre-release is created, GitHub Actions:
     - Updates the `pre-release` branch to point to the release tag
     - Netlify deploys to TST and ACC environments (which are configured to deploy from the `pre-release` branch)
   - When a release is published, GitHub Actions:
     - Updates both the `pre-release` and `release` branches
     - Netlify deploys to all environments (TST, ACC from either branch, and PRD from the `release` branch)
   - A Sentry release is also created for published releases

3. **Content Security Policy (CSP)**:
   - The `netlify-toml-builder.mjs` script dynamically generates the CSP headers based on environment variables
   - This ensures that the correct CSP settings are applied for each environment

### Split Deployments via Beta Branch

- When changes are pushed to the `beta` branch, Netlify automatically deploys to all environments
- This provides a way to deploy changes to all environments without creating a formal release

## Workflow

1. **Development**:
   - Developers work on feature branches
   - PRs are created to merge changes into `master`
   - When PRs are merged, the release draft is automatically updated

2. **Staging Deployment**:
   - When ready to deploy to TST and ACC, create a pre-release on GitHub
   - GitHub Actions updates the `pre-release` branch
   - Netlify deploys to TST and ACC sites

3. **Production Deployment**:
   - When ready to deploy to all environments, publish the release on GitHub
   - GitHub Actions updates both the `pre-release` and `release` branches
   - Netlify deploys to all sites (TST, ACC, and PRD)

4. **Split Deployments**:
   - For quick deployments to all environments, push to the `beta` branch
   - Netlify automatically deploys all sites from the `beta` branch

## Troubleshooting

If you encounter issues with deployments:

1. Check the Netlify deploy logs
2. Verify your netlify.toml configuration
3. Ensure environment variables are correctly set
4. Check GitHub Actions logs for release management issues
5. Verify the branch configurations in Netlify
