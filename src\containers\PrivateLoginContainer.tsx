import Recaptcha from '@components/auth/helpers/Recaptcha';
import AuthButton from '@components/auth/methods/AuthButton';
import GoogleButton from '@components/auth/methods/GoogleButton';
import { errorTextMap } from '@components/reset/PasswordReset';
import { TwoFactorCodeInput } from '@components/two-factor/TwoFactorCodeInput';
import { PhoneAuthProvider, PhoneMultiFactorGenerator, getMultiFactorResolver } from 'firebase/auth';
import React, { useCallback, useState } from 'react';
import { useNavigate } from 'react-router';
import { isPbxEmail, sleep } from '../shared/helpers/helpers';
import { auth } from '../shared/store/setup/firebase-setup';
import { useDispatch } from '../shared/store/store';
import userSlice from '../shared/store/userSlice';
import s from '../shared/styles/component/auth/auth.module.scss';

const PrivateLoginContainer: React.FC = () => {
  const [userHas2FA, setUserHas2FA] = useState(false);
  const dispatch = useDispatch();
  const [MFAResolver, setMFAResolver] = useState<any>();
  const [verificationId, setVerificationId] = useState<string>();
  const [error, setError] = useState<string>();
  const [phoneNumber, setPhoneNumber] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleError = useCallback(
    (err) => {
      setError(err.code);
      dispatch(userSlice.actions.setIsAuthPopupActive(false));
      if (err.code === 'auth/multi-factor-auth-required') {
        // The user is a multi-factor user. Second factor challenge is required.
        const resolver = getMultiFactorResolver(auth, err);
        const appVerifier = window['recaptchaVerifier'];
        setMFAResolver(resolver);
        setUserHas2FA(true);
        const phoneInfoOptions = {
          multiFactorHint: resolver.hints[0],
          session: resolver.session,
        };
        const hint = resolver.hints[0] as any;
        setPhoneNumber(hint.phoneNumber);

        const phoneAuthProvider = new PhoneAuthProvider(auth);
        // Send SMS verification code.
        phoneAuthProvider.verifyPhoneNumber(phoneInfoOptions, appVerifier).then((verificationId) => {
          setVerificationId(verificationId);
        });
      }
    },
    [dispatch],
  );
  const navigate = useNavigate();
  const handleSubmit = () => {
    if (userHas2FA) {
      setIsLoading(true);
      if (error === 'auth/invalid-verification-code') setError('e');
      const cred = PhoneAuthProvider.credential(verificationId, smsCode);
      const multiFactorAssertion = PhoneMultiFactorGenerator.assertion(cred);
      MFAResolver.resolveSignIn(multiFactorAssertion)
        .then((res) => {
          let allowed = false;
          if (isPbxEmail(res.user.email)) {
            allowed = true;
          }
          if (allowed) {
            navigate('inbox');
          } else {
            setError('auth/not-allowed');
            sleep(3000).then(() => {
              auth.signOut();
              navigate('login');
            });
          }
        })
        .catch((err) => {
          setError(err.code);
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      navigate('/inbox', { replace: true });
    }
  };
  return (
    <>
      <Recaptcha />
      <div className={s.container}>
        <div className={s.card}>
          {error ? (
            <>
              <h2 className={s.title}>
                {userHas2FA ? (
                  <span>
                    We've sent your 2FA Code to {phoneNumber}. <br /> Please enter it below.
                  </span>
                ) : (
                  (errorTextMap[error] ?? 'Something went wrong.')
                )}
              </h2>
              <div className={s.mfa}>
                <TwoFactorCodeInput
                  setValue={setSmsCode}
                  error={error !== 'auth/multi-factor-auth-required' ? error : null}
                />
              </div>
              <AuthButton isLoading={isLoading} onClick={handleSubmit} isActive>
                {userHas2FA ? 'Continue' : 'Login With Google'}
              </AuthButton>
            </>
          ) : (
            <GoogleButton isPrivate handleError={handleError} text={'Continue with Google'} />
          )}
        </div>
      </div>
    </>
  );
};

export default PrivateLoginContainer;
