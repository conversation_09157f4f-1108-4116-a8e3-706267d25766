@import "src/shared/styles/vars/_vars";


.container {
  width: 800px;
  color: $font-color-black;
  border-radius: 5px;
  background-color: $white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);
}


.header {
  display: flex;
  justify-content: space-between;
  height: 55px;
  padding: 20px;
  border-bottom: 1px solid $medium-gray;
}


.content {
  padding: 15px 25px 25px 25px;
}


.search {
  display: flex;
  flex-direction: column;
  gap: 10px;
}


.status {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eeeeee;
}


.results {
  display: grid;
  overflow: auto;
  max-height: 60vh;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eeeeee;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

}
