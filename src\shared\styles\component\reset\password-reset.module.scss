@import "./src/shared/styles/vars/_vars";

.container {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 100vh;
  background: #0F1725;

}

.logo {
  width: 200px;
  height: auto;
  margin-bottom: 100px;
  cursor: pointer;
}

.card {
  display: flex;
  flex-direction: column;
  width: 360px;
  margin: 0 auto 15vh;
  padding: 26px 26px 16px;
  color: $font-color-black;
  background-color: $white;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);

}

.button {
  font-size: rem(14) !important;
  width: 100px !important;
  margin: 18px auto 0;
}

.title {
  font-size: rem(18);
  font-weight: 700;
}

.sub {
  font-size: rem(14);
  line-height: 1.25;
  width: 90%;
  margin-top: 16px;

  b {
    font-weight: 700;
  }
}

.input_container {
  margin-top: 25px;

  input {
    height: 35px;
  }

  svg {
    top: 10px;
    right: 28px;
  }
}

