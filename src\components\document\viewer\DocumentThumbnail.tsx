// DocumentThumbnail.tsx - New robust thumbnail component with loading states
import { Ring } from '@uiball/loaders';
import clsx from 'clsx';
import React, { useState, useRef } from 'react';
import { useSelector } from 'react-redux';
import { selectPageImage, selectIsDocumentThumbnailsLoading } from '@shared/store/documentSlice';

interface DocumentThumbnailProps {
  docId: string;
  pageNo: number;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
  style?: React.CSSProperties;
  showPageNumber?: boolean;
  animationDelay?: number;
  isVisible?: boolean; // For controlling the scale animation
}

const DocumentThumbnail = React.forwardRef<HTMLDivElement, DocumentThumbnailProps>(
  (
    {
      docId,
      pageNo,
      isActive = false,
      onClick,
      className,
      style,
      showPageNumber = false,
      animationDelay = 0,
      isVisible = true,
    },
    ref,
  ) => {
    const [isLoaded, setIsLoaded] = useState(false);
    const [hasError, setHasError] = useState(false);
    const imgRef = useRef<HTMLImageElement>(null);

    // Get thumbnail data and loading states from Redux
    const pageImage = useSelector(selectPageImage(docId, pageNo));
    const isDocumentLoading = useSelector(selectIsDocumentThumbnailsLoading(docId));

    const thumbnailSrc = pageImage.thumbUrl;
    const isLoading = isDocumentLoading && !thumbnailSrc;

    const handleImageLoad = () => {
      console.log(`Thumbnail loaded for page ${pageNo}`);
      setIsLoaded(true);
      setHasError(false);
    };

    const handleImageError = () => {
      console.error(`Thumbnail failed to load for page ${pageNo}`);
      setHasError(true);
      setIsLoaded(false);
    };

    const handleClick = () => {
      if (onClick && !isLoading && !hasError) {
        onClick();
      }
    };

    // Container styles - match the original CSS behavior
    const containerStyle: React.CSSProperties = {
      ...style,
      position: 'relative',
      cursor: onClick ? 'pointer' : 'default',
      border: isActive ? '2px solid #0085ff' : '1px solid #ddd',
      overflow: 'hidden',
      backgroundColor: '#f9f9f9',
      // Match the original CSS transition and transform behavior
      transition: isVisible ? 'opacity 0.2s, transform 0.3s ease' : 'opacity 0.2s, transform 0s',
      transform: isVisible ? `scale(${isActive ? 1.02 : 1})` : 'scale(0)',
      opacity: isVisible ? (isLoaded || hasError ? 1 : 0.7) : 0,
      transformOrigin: 'center',
      animationDelay: `${animationDelay}ms`,
      // Add the same styling as the original thumbnails
      width: 'calc(100% - 2px)',
      height: 'auto',
      margin: '0 5px',
      borderRadius: 3,
      boxShadow: 'rgba(0, 0, 0, 0.24) 0 3px 8px',
      willChange: 'transform',
      imageRendering: 'optimizeSpeed' as any,
    };

    // Image styles with fade-in animation
    const imageStyle: React.CSSProperties = {
      width: '100%',
      height: '100%',
      objectFit: 'contain',
      opacity: isLoaded ? 1 : 0,
      transition: 'opacity 300ms ease-out',
    };

    // Loading overlay styles
    const loadingOverlayStyle: React.CSSProperties = {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      opacity: isLoading ? 1 : 0,
      transition: 'opacity 300ms ease-out',
      pointerEvents: 'none',
    };

    return (
      <div
        ref={ref}
        className={clsx('document-thumbnail', className, {
          'document-thumbnail--active': isActive,
          'document-thumbnail--loading': isLoading,
          'document-thumbnail--error': hasError,
        })}
        style={containerStyle}
        onClick={handleClick}
        role={onClick ? 'button' : undefined}
        tabIndex={onClick ? 0 : undefined}
        onKeyDown={(e) => {
          if (onClick && (e.key === 'Enter' || e.key === ' ')) {
            e.preventDefault();
            handleClick();
          }
        }}
      >
        {/* Thumbnail image */}
        {thumbnailSrc && (
          <img
            ref={imgRef}
            src={thumbnailSrc}
            alt={`Page ${pageNo} thumbnail`}
            style={imageStyle}
            onLoad={handleImageLoad}
            onError={handleImageError}
            draggable={false}
            data-hj-suppress
          />
        )}

        {/* Loading indicator */}
        <div style={loadingOverlayStyle}>
          <div style={{ textAlign: 'center' }}>
            <Ring color="#0085ff" size={20} lineWeight={4} />
            <div style={{ marginTop: 4, fontSize: 10, color: '#666' }}>Loading...</div>
          </div>
        </div>

        {/* Error state */}
        {hasError && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#f5f5f5',
              color: '#999',
              fontSize: 12,
            }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 16 }}>⚠️</div>
              <div style={{ marginTop: 4 }}>Error</div>
            </div>
          </div>
        )}

        {/* Page number overlay */}
        {showPageNumber && (
          <div
            style={{
              position: 'absolute',
              bottom: 4,
              right: 4,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              padding: '2px 6px',
              borderRadius: 3,
              fontSize: 10,
              fontWeight: 'bold',
            }}
          >
            {pageNo}
          </div>
        )}

        {/* Active indicator */}
        {isActive && (
          <div
            style={{
              position: 'absolute',
              top: 4,
              left: 4,
              width: 8,
              height: 8,
              backgroundColor: '#0085ff',
              borderRadius: '50%',
              boxShadow: '0 0 0 2px white',
            }}
          />
        )}
      </div>
    );
  },
);

DocumentThumbnail.displayName = 'DocumentThumbnail';

export default DocumentThumbnail;
