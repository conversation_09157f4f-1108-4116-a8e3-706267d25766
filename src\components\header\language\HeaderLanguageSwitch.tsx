import useOutsideClick from '@shared/hooks/useOutsideClick';
import { lngs } from '@shared/store/setup/i18n';
import { useDispatch } from '@shared/store/store';
import { patchUserPreferences } from '@shared/store/userSlice';
import s from '@shared/styles/component/header/header-language.module.scss';
import { ReactComponent as ArrowDownIcon } from '@svg/chevron-down.svg';
import clsx from 'clsx';
import React, { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

const HeaderLanguageSwitch: React.FC = () => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const ref = useRef();
  const dispatch = useDispatch();
  useOutsideClick(ref, () => setIsOpen(false));

  const handleClick = (lng: string) => {
    i18n.changeLanguage(lng);
    dispatch(patchUserPreferences({ language: lng }));

    setIsOpen(false);
  };
  return (
    <div ref={ref} className={s.wrapper}>
      <div onClick={() => setIsOpen(!isOpen)} className={clsx(s.inner, { [s.inner__open]: isOpen })}>
        <div className={clsx(s.flag)}>{lngs[i18n.resolvedLanguage]?.shortName}</div>
        <ArrowDownIcon className={s.arrow} />
      </div>
      <div className={clsx(s.container, { [s.container__open]: isOpen })}>
        {Object.keys(lngs).map((lng) => (
          <div
            key={lng}
            onClick={() => handleClick(lng)}
            className={clsx(s.dropdown_item, {
              [s.dropdown_item__active]: lng === i18n.resolvedLanguage,
            })}
          >
            <div className={s.flag}>{lngs[lng]?.shortName}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HeaderLanguageSwitch;
