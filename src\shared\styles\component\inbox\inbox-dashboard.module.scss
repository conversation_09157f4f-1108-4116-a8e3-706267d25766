@import '../../vars/_vars';


.container {
  display: flex;
  overflow: scroll;
  flex-direction: column;
  height: 100%;
  max-height: calc(100vh - 115px);
  margin: 0 auto;
  padding: 0 50px 0 50px;
}


.inboxes {
  display: flex;
  justify-content: space-evenly;
  width: auto;
  height: 40px;
}


.inbox_button {
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 100px;

  &__active {

  }
}


.no_data {
  font-size: 14px;
  font-weight: 500;
}


.chart_wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex-basis: 400px;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  margin-top: 30px;
  border: 1px solid #eeeeee;
  border-radius: 5px;
}


.chart_axis {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 2%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  height: 100%;
  padding: 45px 20px;

  span {
    font-size: 15px;
    font-weight: 500;
    opacity: 0.6;
  }
}


.chart_inbox_tooltip {

  b {
    font-weight: 800;
    padding: 5px;

  }

  .tooltip_inboxes {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    max-height: 50vh;
    margin-top: 10px;

    .tooltip_inbox {
      font-weight: 700;
      margin: 5px;
      padding: 10px;
      opacity: 0.7;
      border: 1px solid #e0e0e0;

      border-radius: 5px;
      gap: 10px;

      .tooltip_doctypes {
        font-weight: 400;
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        margin-top: 10px;
      }
    }
  }
}


.card_grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  height: auto;
  margin-top: 40px;
  gap: 20px;
}


.seperator {
  align-self: stretch;
  width: 1px;
  min-width: 1px;
  max-width: 1px;
  background: #EEEEEE;
}


.card {
  display: flex;
  flex-basis: 300px;
  flex-direction: column;
  padding: 18px;
  border: 1px solid #eeeeee;
  border-radius: 5px;
}


.card_top {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
}


.card_number {
  font-size: 42px;
  font-weight: 600;
}


.card_title {
  font-size: 18px;
  font-weight: 400;
  color: #34405480;
}


.card_top_info {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 32px;
  color: #d2d5da;

  svg {
    width: 16px;
    height: auto;
  }
}


.card_top_toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 32px;
  transition: color 0.15s ease;
  color: #34405480;
  border: 1px solid #eeeeee;

  border-radius: 7px;

  svg {
    width: 20px;
    height: auto;
  }

  &:hover {
    color: black;
  }

  &__active {
    color: $paperbox-blue;
  }

}


.card_bottom {
  display: flex;
  overflow: auto;
  flex-direction: column;
  min-height: 100px;
  max-height: 143px;
  margin-top: 14px;
  gap: 5px;

  &::-webkit-scrollbar-thumb {
    background: #f5f7fa77;
  }

  &:hover {
    &::-webkit-scrollbar-thumb {
      background: #f5f7fa;
    }
  }
}


.card_bottom_item {
  display: flex;
  align-items: center;
  height: 32px;
  gap: 20px;

  span {
    font-weight: 400;
    color: #34405480;
  }
}


.card_bottom_item_icon {
  width: 16px;
  height: 16px;
  margin-left: 0px;
}


.card_bottom_item_counter {
  font-size: 14px;
  font-weight: 600;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  padding: 8px 2px;
  color: #70ddba;
  border: 1px solid transparent;
  border-radius: 3px;
  background-color: rgba(#70ddba, 0.1);

  &__overdue {
    border: 1px dashed $error
  }
}
