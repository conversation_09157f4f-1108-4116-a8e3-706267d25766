import InboxTableNew from '@components/inbox/table/InboxTableNew.tsx';
import InboxTableToolbarHistorical from '@components/inbox/table/InboxTableToolbarHistorical.tsx';
import InboxTags from '@components/inbox/table/components/InboxTags.tsx';
import StyledSelect, { DropdownOption } from '@components/shared/dropdown/StyledSelect.tsx';
import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import { isSameDate } from '@shared/helpers/helpers.ts';
import { isDefaultDateRange } from '@shared/helpers/newHelpers.ts';
import { useGetDoctypesQuery } from '@shared/helpers/rtk-query/firestoreApi.ts';
import useFilterSync from '@shared/hooks/useFilterSync.ts';
import useHistoricalDocumentList from '@shared/hooks/useHistoricalDocumentList.ts';
import { resetFilters, updateFilters } from '@shared/store/documentListSlice.ts';
import s from '@shared/styles/component/inbox/inbox-content.module.scss';
import { useDispatch, useSelector } from '@src/shared/store/store.ts';
import { ReactComponent as PaperboxLogoStrike } from '@svg/paperbox-logo-small-strike.svg';
import { ReactComponent as PaperboxLogo } from '@svg/paperbox-logo-small.svg';
import { ReactComponent as SearchIcon } from '@svg/search-icon.svg';

import clsx from 'clsx';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useParams, useSearchParams } from 'react-router';
import { DateRangePicker } from 'rsuite';
import { RangeType } from 'rsuite/DateRangePicker';
import { allowedRange } from 'rsuite/esm/DateRangePicker/disabledDateUtils';
import { addDays, subDays } from 'rsuite/esm/utils/dateUtils';
import HeaderProfile from '../../header/profile/HeaderProfile.tsx';
import InboxTableFooter from '../table/InboxTableFooter.tsx';
import usePagination from '../table/hooks/usePagination.tsx';

interface Props {}

const InboxHistoricalDocumentsNew: React.FC<Props> = () => {
  const { inboxId } = useParams();
  const { pageCount } = usePagination();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const location = useLocation();

  const filters = useSelector((state) => state.documentList.filters);
  const { documents, isLoading } = useHistoricalDocumentList(inboxId);
  const { all: docTypes } = useGetDoctypesQuery({ inboxId }).data ?? {};

  // Enable individual filter sync for this component
  useFilterSync({ autoSync: true });

  // Track previous location to detect view switching
  const previousLocation = useRef<string | null>(null);
  const isFirstMount = useRef(true);

  // Reset filters when switching from normal to historical view, but preserve direct navigation
  useEffect(() => {
    const currentPath = location.pathname;
    const isHistoricalView = currentPath.includes('/historical');

    if (isFirstMount.current) {
      isFirstMount.current = false;
      previousLocation.current = currentPath;

      // Check if there are any filter-related URL parameters
      const hasFilterParams = Array.from(searchParams.keys()).some((key) =>
        [
          'docTypeId',
          'subTypeId',
          'searchTerm',
          'activeTagId',
          'sortBy',
          'actorId',
          'isSortDescending',
          'action',
          'actorIdFilterMode',
          'approvalChecks',
        ].includes(key),
      );

      // Only reset if there are no URL parameters (clean navigation) and we're on historical view
      if (!hasFilterParams && isHistoricalView) {
        dispatch(resetFilters());
        setSearchParams(new URLSearchParams(), { replace: true });
      }
    } else {
      // Detect view switching: coming from normal to historical
      const wasNormal =
        previousLocation.current &&
        !previousLocation.current.includes('/historical') &&
        previousLocation.current.includes(`/inbox/${inboxId}`) &&
        !previousLocation.current.includes('/dashboard');
      const isNowHistorical = isHistoricalView;

      if (wasNormal && isNowHistorical) {
        // Reset filters when switching from normal to historical view
        dispatch(resetFilters());
        setSearchParams(new URLSearchParams(), { replace: true });
      }

      previousLocation.current = currentPath;
    }
  }, [location.pathname, inboxId, dispatch, searchParams, setSearchParams]);

  const title = useMemo(() => {
    const { docTypeId, subTypeId } = filters;
    const selectedDocType = docTypes?.find((docType) => docType.id === docTypeId);
    if (!selectedDocType) return t('home:allDocuments');
    let documentTitle = selectedDocType.name;

    if (subTypeId) {
      const selectedSubType = selectedDocType.subtypes?.find((subType) => subType.id === subTypeId);
      documentTitle += ` / ${selectedSubType?.name}`;
    }

    return documentTitle;
  }, [filters, docTypes, t]);

  const pbxFilterState = useMemo(() => {
    const { actorId, actorIdFilterMode } = filters;

    if (!actorId) {
      return 'inactive'; // Inactive state
    }
    if (actorId === 'paperbox' && !actorIdFilterMode) {
      return 'active'; // Active state
    }
    if (actorId === 'paperbox' && actorIdFilterMode === 'exclude') {
      return 'disabled'; // Disabled state
    }
  }, [filters]);

  const handlePbxFilterClick = () => {
    const { actorId, actorIdFilterMode } = filters;

    if (!actorId) {
      dispatch(updateFilters({ actorId: 'paperbox', actorIdFilterMode: null, currentPageIndex: 0 }));
    } else if (actorId === 'paperbox' && !actorIdFilterMode) {
      dispatch(updateFilters({ actorId: 'paperbox', actorIdFilterMode: 'exclude', currentPageIndex: 0 }));
    } else {
      dispatch(updateFilters({ actorId: null, actorIdFilterMode: null, currentPageIndex: 0 }));
    }
  };
  const actionOptions: DropdownOption[] = [
    { label: t('document:actions.all'), value: 'all' },
    { label: t('document:actions.approve'), value: 'approve' },
    { label: t('document:actions.bounce'), value: 'bounce' },
    { label: t('document:actions.delete'), value: 'delete' },
  ];
  const [actionValue, setActionValue] = useState(
    filters?.action
      ? actionOptions.find((o) => o.value === filters.action)
      : { label: t('document:actions.all'), value: 'all' },
  );

  const enabledColumns = {
    name: true,
    actor: true,
    actionDate: true,
    confidence: true,
    digitizedDate: false,
    docTypeId: true,
    tagTypeId: true,
    lastUserUpdate: false,
    locker: true,
    approvalChecks: false,
    initialApprovalChecks: true,
    actionType: true,
  };
  const filterCount = useMemo(() => {
    let counter = 0;
    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'dateRange') {
        if (isDefaultDateRange(value[0], value[1])) return;
      }
      if (
        key === 'sortBy' ||
        key === 'isSortDescending' ||
        key === 'actorIdFilterMode' ||
        key === 'pageSize' ||
        key === 'navDirection' ||
        key === 'docTypeId' ||
        key === 'currentPageIndex'
      )
        return;
      if (value === null || value === undefined || value === '') {
        return;
      }
      counter++;
    });
    return counter;
  }, [filters]);

  const predefinedBottomRanges: RangeType[] = [
    {
      label: t('home:dashboard.rangeDay'),
      value: [new Date(), new Date()],
    },
    {
      label: t('home:dashboard.rangeYesterday'),
      value: [addDays(new Date(), -1), addDays(new Date(), -1)],
    },
    {
      label: t('home:dashboard.rangeWeek'),
      value: [subDays(new Date(), 6), new Date()],
    },
    {
      label: t('home:dashboard.rangeXDays', { days: 29 }),
      value: [subDays(new Date(), 29), new Date()],
    },
  ];

  return (
    <div className={s.container} style={{ marginBottom: 0 }}>
      <div className={s.header}>
        <h3 className={s.title} data-testid="inbox-title">
          {title}
        </h3>
        <HeaderProfile />
      </div>

      <div className={s.sub_header}>
        <div className={s.search}>
          <SearchIcon />
          <input
            onChange={(e) => {
              dispatch(updateFilters({ searchTerm: e.target.value }));
            }}
            value={filters?.searchTerm ?? ''}
            type="text"
            placeholder={'Search Subject, Recipient, Sender, Actor or Document Name'}
          />
        </div>
        <Tooltip content={t('home:filterPbx')} position={'bottom'}>
          <button
            className={clsx(s.search_button, {
              [s.search_button__active]: filters?.actorId === 'paperbox',
            })}
            onClick={handlePbxFilterClick}
          >
            {pbxFilterState === 'disabled' ? <PaperboxLogoStrike /> : <PaperboxLogo />}
          </button>
        </Tooltip>
        <div>
          <StyledSelect
            style={{
              maxHeight: 'unset',
              height: '40px',
              border: '1px solid #eeeeee',
              fontWeight: '700 !important',
            }}
            options={actionOptions}
            value={actionValue}
            onChange={(val: any) => {
              if (val.value === actionValue.value) return;
              setActionValue(val);
              dispatch(updateFilters({ action: val.value === 'all' ? null : val.value }));
            }}
          />
        </div>
        <InboxTags
          historical={true}
          activeTagId={filters?.activeTagId}
          setActiveTagId={(tagId) => dispatch(updateFilters({ activeTagId: tagId }))}
        />

        <div className={s.info}>
          <DateRangePicker
            size={'md'}
            ranges={predefinedBottomRanges}
            shouldDisableDate={allowedRange(subDays(new Date(), 29), new Date())}
            placeholder="Filter Date Range"
            placement={'auto'}
            editable={false}
            cleanable={false}
            renderValue={(value) => {
              const test = predefinedBottomRanges.find(
                (e) =>
                  isSameDate(new Date(e.value[0]), new Date(value[0])) &&
                  isSameDate(new Date(e.value[1]), new Date(value[1])),
              );
              return (
                <div style={{ marginTop: 2 }}>
                  {test ? test.label : `${value[0].toLocaleDateString()} - ${value[1].toLocaleDateString()}`}
                </div>
              );
            }}
            value={filters.dateRange}
            isoWeek
            character={'  -  '}
            onChange={(e) => {
              if (e) dispatch(updateFilters({ dateRange: e }));
            }}
          />
        </div>
      </div>
      {filterCount > 0 && (
        <div className={s.filter_bar}>
          {filterCount ?? 1} Filters Active:
          <span onClick={() => dispatch(resetFilters(['docTypeId']))}>Reset Filters</span>
        </div>
      )}
      <InboxTableNew
        enabledColumns={enabledColumns}
        documents={documents}
        loading={isLoading}
        pageCount={pageCount}
        toolbarComponent={InboxTableToolbarHistorical}
        goToDocument={(docId) => {
          navigate(`/inbox/${inboxId}/historical/${docId}`);
        }}
      />
      <InboxTableFooter />
    </div>
  );
};

export default InboxHistoricalDocumentsNew;
