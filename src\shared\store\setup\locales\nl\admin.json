{"actionType": {"alreadyExists": "Bo<PERSON>ceveld bestaat reeds", "config": "Configuratie", "dangerZone": "Gevarenzone", "deleteType": "Verwijder type", "deleteTypeDescription": "Het verwijderen van dit bounceveld zal dit veld verwijderen uit de opties die gebruikers kunnen selecteren bij het versturen van een actie.", "error": "Er trad een fout op, probeer opnieuw.", "id": "Id", "idDescription": "ID te gebruiken in verbonden services zoals webhook, API,...", "name": "<PERSON><PERSON>", "nameDescription": "<PERSON><PERSON> voor de gebruikers", "newOption": "Nieuwe bounce optie", "newType": "<PERSON><PERSON><PERSON> bounceveld", "optionAlreadyExists": "<PERSON><PERSON> met deze naam bestaat reeds.", "optionDelete": "<PERSON>tie verwijderen", "optionDeleteDescription": "Door deze optie te verwijderen wordt deze verwijderd uit de opties die een gebruiker kan selecteren bij het versturen van een actie.", "providerId": "Provider Id", "providerIdDescription": "ID te gebruiken in verbonden services. Als de provider Id niet ingevuld is, zal de gewone ID gebruikt worden.", "sensitive": "Gevoelige data", "sensitiveDescription": "Paperbox anonimiseert gevoelige data", "valueOptions": "Toegestane opties", "valueOptionsDescription": "Opties die beschikbaar zullen zijn voor de gebruiker bij het invoeren van dit veld.", "valueType": "<PERSON>rt waarde", "valueTypeDescription": "Pas deze optie aan om het type van de in te vullen waarde aan te passen.", "values": "<PERSON><PERSON><PERSON>"}, "connectors": {"addConnector": "Connector aanmaken", "auth": {"dataConfig": "Dataconfiguratie", "enabled": "Actief", "enabledDescription": "<PERSON><PERSON><PERSON>-verz<PERSON><PERSON> in of uit. <PERSON><PERSON> dit ingeschakeld is, wordt er een extra request gedaan bij het gebruik van de<PERSON> connector", "headers": "Headers", "headersDescription": "Voeg headers toe", "payload": "OAuth Payload", "payloadDescription": "Definieer de values die in het OAuth-verzoek worden meegegeven.", "queryParams": "Query Params", "queryParamsDescription": "Deze parameters worden als queryparameters aan uw URL toegevoegd.", "responseTokenKey": "Response Token key", "responseTokenKeyDescription": "<PERSON><PERSON> 'key' in de OAuth Response die de authenticatietoken bevat.", "title": "Authenticatie", "url": "Authenticatie-URL", "urlDescription": "De URL die gebruikt wordt bij het maken van het authenticatieverzoek."}, "ccs": {"account": "Account", "accountDescription": "Het account dat gebruikt zal worden bij het versturen van requests met deze connector.", "password": "Wachtwoord", "passwordDescription": "Het wachtwoord dat wordt gebruikt om te authenticeren op de CCS-server.", "url": "URL", "urlDescription": "De URL die gebruikt zal worden bij het versturen van requests met deze connector.", "username": "Gebruikersnaam", "usernameDescription": "De gebruikersnaam die wordt gebruikt om te authenticeren op de CCS-server."}, "dangerZone": "Gevarenzone", "dataConfig": "Gegevensconfiguratie", "delete": "Connector verwijderen", "deleteDescription": "<PERSON><PERSON> actie is o<PERSON><PERSON><PERSON><PERSON><PERSON>, controleer of er bestaande webhooks / API's zijn die deze connector nog gebruiken.", "environment": "Brio Omgeving", "environmentDescription": "De Brio omgeving waar deze connector mee verbonden is.", "generalInfo": "Algemene Info", "headers": "Headers", "headersDescription": "Sleutel-waardeparen die toegevoegd worden als headers in alle verzoeken.", "mailboxes": "Mailboxes", "ms365": {"addRule": "Nieuwe regel toe<PERSON>n", "approve": "Goedgekeurde Documenten", "bounce": "Bounced Documenten", "connected": "Verbonden", "connectedTo": "Verbonden met", "connectedToMS365": "<PERSON><PERSON><PERSON><PERSON> met Microsoft 365", "delete": "Verwijderde Documenten", "description": "Hier kan je Paperbox toestemming geven om te verbinden met uw Microsoft 365 omgeving.", "editMailboxes": "Verbonden mailboxes aanpassen", "emailPicker": {"disableAll": "Alles u<PERSON>", "disabled": "Uitgeschakeld", "enableAll": "Alles <PERSON>ren", "enabled": "Actief", "selectEmails": "Actieve emails selecteren"}, "inbound": "Email import regels", "inboundDescription": "Configureer de routering van uw Microsoft 365 e-mails naar specifieke Paperbox inboxes/documenttypes", "mailbox": "Mailbox", "outbound": "Uitgaande <PERSON>", "outboundDescription": "Configureer de Microsoft-inbox voor documenten verwerkt in Paperbox", "title": "Microsoft 365 Verbinding", "userPicker": {"selectPath": "Mailbox / folder selecteren"}}, "name": "<PERSON><PERSON>", "nameDescription": "De naam die gebruikt wordt om de connector te identificeren in de workspace instellingen.", "newConnector": "Nieuwe Connector", "officeId": "Office ID", "officeIdDescription": "De <PERSON> van het kantoor/database waarmee u verbinding wilt maken.", "payload": "Basis-payload", "payloadDescription": "Dit zijn key-value pairs die toegevoegd worden aan het gegevensgedeelte van elk verzoek dat deze connector gebruikt.", "pi2Key": "PI2 Sleutel", "pi2KeyDescription": "De sleutel die wordt gebruikt om verbinding te maken met de juiste Portima Brio-database.", "queryParams": "Query Params", "queryParamsDescription": "Deze parameters worden als queryparameters aan uw URL toegevoegd.", "save": "Wijzigingen Opslaan", "saving": "Opsla<PERSON>", "sftp": {"ip": "IP-adres", "ipDescription": "Het IP-adres van de SFTP-server.", "password": "Wachtwoord", "passwordDescription": "Het wachtwoord die kan worden gebruikt voor authenticatie op de SFTP-server.", "port": "Poort", "portDescription": "<PERSON> van de SFTP-server.", "privateKey": "Private Key", "privateKeyDescription": "De private key die gebruikt wordt voor authenticatie op de SFTP-server.", "username": "Gebruiksersnaam", "usernameDescription": "De gebruikersnaam die wordt gebruikt voor authenticatie op de SFTP-server."}, "subOfficeId": "Sub Office ID", "subOfficeIdDescription": "<PERSON> <PERSON> van de gebruiker die verbonden is met het kantoor/database.", "title": "Connectors", "type": "Connector Type", "typeDescription": "Definieer het type van de connector.", "url": "Base URL", "urlDescription": "De base URL die gebruikt wordt bij het maken van requests met behulp van deze connector."}, "datasources": {"basicInfo": "Basisinformatie", "dangerZone": "Gevarenzone", "delete": "Verwijder datasource", "deleteConfirmation": "Weet je zeker dat je deze datasource wilt verwijderen?", "deleteDescription": "Het verwijderen van deze datasource zal deze en alle bijbehorende gegevens uit Paperbox verwijderen. Deze actie kan niet ongedaan worden gemaakt.", "derivedFields": "Afgeleide Velden", "derivedMappings": "Afgeleide Mappings", "derivedMappingsDescription": "Configureer berekende velden die worden afgeleid van andere veldwaarden met be<PERSON><PERSON> van expressies.", "errorExists": "Datasource met deze naam best<PERSON> al.", "errorGeneric": "Er ging iets mis, probeer het later opnieuw.", "exampleData": "Voorbeeldgegevens", "exampleDataDescription": "Verstrek voorbeeldgegevensrecords om te helpen bij het testen en valideren van de datasource-configuratie.", "fieldMappings": "Veldmappings", "frequency": "Update Frequentie", "frequencyDescription": "Hoe vaak de datasource automatisch moet worden bijgewerkt (in seconden). Laat leeg voor alleen handmatige updates.", "id": "ID", "idDescription": "Unieke identificatie voor deze datasource.", "mappings": "Veldmappings", "mappingsDescription": "Configureer hoe velden uit uw gegevensbron worden toegewezen en verwerkt door Paperbox.", "name": "<PERSON><PERSON>", "nameDescription": "<PERSON>en beschrijvende naam voor deze datasource.", "newDatasource": "Nieuwe Datasource", "operationRetention": "Operatie Retentie", "operationRetentionDescription": "Aantal operaties om te behouden in de geschiedenis.", "recordIdField": "Record ID Veld", "recordIdFieldDescription": "Het veld dat elk record in uw datasource uniek identificeert.", "sampleFile": "Voorbeeldbestand", "sampleFileDescription": "Upload een voorbeeldbestand (JSON, CSV of Excel) om automatisch veldmappings te genereren.", "sampleRequired": "Upload eerst een voorbeeldbestand om veldmappings te genereren.", "sampleUploadError": "Verwerking van voorbeeldbestand mislukt. Controleer het formaat en probeer opnieuw.", "uploadSample": "Upload Voorbeeld & Configureer"}, "docType": {"add": "Bevestigen", "ageThreshold": "Maximale ouderdom", "ageThresholdDescription": "Vanaf deze ouderdom worden documenten gemarkeerd als over tijd", "alreadyExists": "Documenttype bestaat reeds.", "approvalChecks": "Approval checks", "approvalThreshold": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van de classificatie", "approvalThresholdDescription": "De zekerheid die moet worden bereikt om een document automatisch goed te keuren", "autoApprove": "Automatisch goedkeuren", "autoApproveDescription": "Keur document automatisch goed", "automation": "Automatisatie", "categories": "Veldcategoriëen", "categoriesDescription": "<PERSON><PERSON><PERSON><PERSON> velden in categoriëen", "categoriesEdit": {"addFieldDescription": "Selecteer veldtypes om toe te wijzen aan deze categorie.", "addFieldTitle": "Selecteer veldtypes"}, "config": "Configuratie", "dangerZone": "Gevarenzone", "deleteType": "Verwijder documenttype", "deleteTypeDescription": "Verwijderde documenttypes kunnen niet meer worden gebruikt. \nOpenstaande documenten met dit type zullen niet worden verwijderd.", "docSubTypes": "Subtypes", "docSubTypesNotFound": "Geen subtypes gevonden", "docSubTypesSearch": "Doorzoek subtypes", "entityTypes": "Veldtypes", "entityTypesDescription": "Wijs veldtypes toe aan dit doctype.\nPas de volgorde aan door te slepen.", "fieldAutomation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldName": "<PERSON><PERSON>", "fieldOccurrences": "Aantal Unieke Velden", "fieldSearchDescription": "Selecteer veldtypes om toe te wijzen aan dit documenttype", "fields": "<PERSON><PERSON><PERSON>", "fixed": "Vast type", "fixedDescription": "Wanneer deze optie is inges<PERSON><PERSON>d, kunnen gebruikers het documenttype voor documenten met dit type niet wijzigen, het wijzigen van het subtype blijft mogelijk.", "id": "Id", "idDescription": "ID te gebruiken in verbonden services zoals webhook, API,...", "masterdataCheck": "Masterdata Check", "masterdataCheckDescription": "Wanneer deze optie is ingeschakeld, zal Paperbox de aangeleverde masterdata-tabel gebruiken om gegevens op te zoeken voor dit documenttype.", "metaSearchDescription": "Selecteer metadatatypes om toe te wijzen aan dit documenttype", "metadata": "<PERSON><PERSON><PERSON>", "metadataKeys": "Metadatatypes", "metadataKeysDescription": "Selecteer de toegestane metadataypes die je aan dit document type wilt toewijzen.\nWanneer een type een waarde heeft, wordt deze waarde automatisch toegewezen wanneer het document wordt verwerkt.", "name": "<PERSON><PERSON>", "nameDescription": "<PERSON><PERSON> voor de gebruikers.", "newType": "Nieuw documenttype", "ocrThreshold": "OCR Zekerheidsgrens", "ocrThresholdDescription": "De zekerheid die tekst over de tekst in een document, om deze automatisch goed te keuren", "promptConfig": {"description": "Beschrijving", "descriptionDescription": "Dit veld voegt context toe om het GenAI-model te helpen documenten beter te begrijpen en te classificeren.\nBijvoorbeeld, het specificeren van 'Een medisch document ...' voor een gezondheidsrapport helpt bij een nauwkeurigere categorisatie", "title": "GenAI Configuratie"}, "providerId": "Provider Id", "providerIdDescription": "ID te gebruiken in verbonden services. Als de provider Id niet ingevuld is, zal de gewone ID gebruikt worden.", "quickEditCategories": {"metadata": "<PERSON><PERSON><PERSON> velden"}, "subType": {"delete": "Subtype verwijderen", "deleteDescription": "Het verwijderen van dit sub-type voorkomt dat toekomstige documenten als dit sub-type worden gedetecteerd.\nHuidige documenten met dit sub-type worden niet verwijderd."}, "type": "<PERSON><PERSON>", "typeDescription": "<PERSON><PERSON><PERSON> in welke context dit documenttype gebruikt kan worden."}, "errors": {"401": "U bent niet gemachtigd deze actie uit te voeren.", "403": "U hebt geen toestemming om deze actie uit te voeren.", "404": "Er ging iets mis, deze actie kon niet worden uitgevoerd.", "409": "Er bestaat reeds een item met dit ID.", "500": "Er is een interne fout opgetreden. <PERSON><PERSON>r het later nog eens.", "inboxCreate": "Aanmaken van inbox mislukt, bekijk de gegevens na en probeer opnieuw."}, "fieldType": {"alreadyExists": "Veldtype bestaat reeds", "complexFields": "Complex fields", "complexFieldsDescription": "Definieer de velden die aanwezig zijn als items in het complexe veld. Je kunt ook kiezen of een veld verplicht is of niet.", "config": "Configuratie", "dangerZone": "Gevarenzone", "deleteType": "Verwijder veldtype", "deleteTypeDescription": "Verwijderde veldtypes kunnen niet meer worden gebruikt. Huidige velden van dit type zullen niet worden verwijderd.", "id": "Id", "idDescription": "ID te gebruiken in verbonden services zoals webhook, API,... ", "name": "<PERSON><PERSON>", "nameDescription": "<PERSON><PERSON> voor de gebruikers", "newType": "Nieuw veldtype", "options": "<PERSON><PERSON><PERSON>", "optionsDescription": "De waarden die beschikbaar zullen zijn voor de gebruiker bij het maken van een veld van dit type.", "promptConfig": {"description": "Beschrijving", "descriptionDescription": "Dit veld verbetert het vermogen van het GenAI-model om dit veld in een document te identificeren en te begrijpen.\nBij<PERSON>orbeeld, het specificeren van 'Een polis-ID bestaat uit 6-8 tekens beginnend met BE' stelt het model in staat om deze beter te herkennen.", "title": "GenAI Configuratie"}, "providerId": "Provider Id", "providerIdDescription": "ID te gebruiken in verbonden services. Als de provider-Id niet ingevuld is, zal de gewone ID gebruikt worden.", "sensitive": "Gevoelige data", "sensitiveDescription": "Paperbox anonimiseert gevoelige data", "tableFields": "Tabelvelden", "tableFieldsDescription": "Kolomhoofden in de tabel", "tableMandatory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableSelectDescription": "Select het veldtype voor de tabel", "tableSelectTitle": "Selecteer veldtypes", "type": "Type", "typeDescription": "Veldtype. Wordt gebruikt in de UI.", "usage": {"unused": "Ongebruikt", "usedIn": "<PERSON><PERSON><PERSON><PERSON><PERSON> in:", "andMore": "En {{count}} andere"}}, "inboxes": {"actionTypes": "Actietypes", "actionTypesDescription": "Definieer de beschikbare opties die gebruikers kunnen selecteren bij het 'bouncen' van een document.", "addInbox": "Inbox toevoegen", "autoDelete": {"actionType": "<PERSON><PERSON>", "actionTypeDescription": "Kies welke actie wordt ondernomen wanneer een document automatisch wordt verwerkt, kies tussen 'Bounce', 'Delete', 'Approve' of geen actie. \n\nLet op: Wan<PERSON> 'Geen Actie' wordt geselecteerd, worden er geen webhooks getriggerd.", "enabled": "Cleanup rules", "enabledDescription": "Met de cleanup rules kan je er voor zorgen dat documenten automatisch verwerkt worden nadat een vooraf ingestelde tijd is verstreken. Hier kunt u het inschakelen/uitschakelen.", "options": {"approve": "<PERSON><PERSON><PERSON><PERSON>", "bounce": "<PERSON><PERSON><PERSON>", "delete": "Verwijderen", "noAction": "<PERSON><PERSON>"}, "time": "Periode", "timeDescription": "<PERSON><PERSON> de tijdsperiode op waarna documenten automatisch worden verwerkt.", "title": "Inbox Cleanup"}, "createInbox": "Inbox aanmaken", "days": "dagen", "docTypes": "Documenttypes", "docTypesDescription": "Definieer de types die gebruikt worden om elk document te classificeren, verdeeld in 3 categorieën: bundeltypes, documenttypes en mailtypes.", "documentRetentionTime": "Document bewaartijd", "documentRetentionTimeDescription": "Het aantal dagen dat documenten worden bewaard door Paperbox nadat deze zijn verwerkt / goedgekeurd.", "fieldTypes": "Veldtypes", "fieldTypesDescription": "Configureer de velden die Paperbox moet extraheren uit het document, deze zullen ook beschikbaar zijn bij het manueel verwerken van een document.", "id": "Id", "idDescription": "Identifier voor de inbox in onze API", "inboxMove": {"enabled": "Whitelist g<PERSON><PERSON><PERSON>n", "enabledDescription": "Als deze functie is ingeschakeld, kunnen gebruikers alleen documenten verplaatsen naar de inboxen in de onderstaande lijst.", "list": "Whitelisted Inboxes", "listDescription": "Selecteer de inboxen waar gebruikers documenten naartoe mogen verplaatsen.", "title": "Document Inbox Move"}, "inboxSettings": "Inbox instellingen", "inboxType": "Inbox Type", "inboxTypeDescription": "Selecteer het soort inbox die je wilt aanmaken.", "language": "Taal", "languageDescription": "Selecteer <PERSON> <PERSON><PERSON> van de configuratie dat je wilt gebruiken.", "masterdata": "Masterdata", "masterdataDescription": "Alle configuratie gere<PERSON>erd aan het zoeken in masterdatatabellen. Masterdatatabellen kunnen worden geüpload via de workspace instellingen of via de Paperbox Integration API (zie <1>https://docs.paperbox.ai</1>)", "metadataTypes": "Metadatatypes", "metadataTypesDescription": "Configureer de overige velden die Paperbox niet direct uit het document moet extraheren, maar wel beschikbaar moet stellen bij de zoekfunctie en output.", "name": "Inbox Naam", "nameDescription": "<PERSON><PERSON> voor de gebruikers", "saveChanges": "Wijzigingen opslaan", "saving": "Opslaan...", "sections": {"actionTypeDelete": "Weet je zeker dat je dit bounce veld wilt verwijderen?", "actionTypeOptionDelete": "Weet je zeker dat je deze bounce optie wilt verwijderen?", "actionTypeSearch": "<PERSON><PERSON>", "addNew": "<PERSON><PERSON><PERSON>", "docTypeDelete": "Weet je zeker dat je dit document type wilt verwijderen?", "docTypeMultiDelete": "Weet je zeker dat je deze document types wilt verwijderen?", "docTypeSearch": "Zoek documenttypes", "fieldTypeDelete": "Weet je zeker dat je deze veld types wilt verwijderen?", "fieldTypeMultiDelete": "Weet je zeker dat je deze veld types wilt verwijderen?", "fieldTypeSearch": "Zoek veldtypes", "masterdataSearch": "<PERSON><PERSON> in lijst van tabellen", "masterdataTableDelete": "Weet je zeker dat je deze masterdata tabel wilt verwijderen?", "metadataTypeDelete": "Weet je zeker dat je dit metadata type wilt verwijderen?", "metadataTypeSearch": "Zoek metadatatypes", "noActionTypeFound": "<PERSON><PERSON> gevonden", "noDocTypeFound": "Geen documenttypes gevonden", "noFieldTypeFound": "Geen veldtypes gevonden", "noMasterdataFound": "Geen masterdata tabellen gevonden", "noMetadataTypeFound": "Geen metadatatypes gevonden", "noTagTypeFound": "<PERSON><PERSON> g<PERSON>", "tagTypeDelete": "Weet je zeker dat je deze tag wilt verwijderen?", "tagTypeMultiDelete": "Weet je zeker dat je deze tags wilt verwijderen?", "tagTypeSearch": "<PERSON><PERSON> tags"}, "selectConnector": "Connector", "selectConnectorDescription": "<PERSON><PERSON> een bestaande connector om te gebruiken met deze inbox.", "settings": {"autoAdvance": "Auto Advance", "autoAdvanceDesc": "Met deze functionaliteit gaan gebruikers automatisch naar het volgende document in de wachtrij zodra ze klaar zijn met het huidige document, wat de verwerkingssnelheid verhoogt.", "bounce": "Document Bounce", "bounceDesc": "De Document Bounce functie biedt een optie om een document te weigeren en terug te sturen. Dit is handig wanneer een document de benodigde informatie mist om correct verwerkt te worden.", "documentCopy": "Document Copy", "documentCopyDesc": "Met de Document Copy functie kunnen gebruikers kopieën maken van documenten. Di<PERSON> is handig als er verschillende versies van een document nodig zijn die elk op een andere manier moeten worden verwerkt.", "documentDownload": "Document Download", "documentDownloadDesc": "Deze functie stelt gebruikers in staat om documenten rechtstreeks naar hun lokale machine te downloaden voor offline gebruik of verdere analyse.", "documentTransform": "Document Transformaties", "documentTransformDesc": "Met deze functie kunnen gebruikers specifieke secties van een documentbundel, zoals een e-mailbijlage, opsplitsen en elke sectie afzonderlijk binnen dezelfde bundel classificeren. Daarnaast kunnen gebruikers, wanneer de document copy is ingeschakeld, gedeeltelijke kopieën maken door geselecteerde delen van het document uit te sluiten.", "fileUpload": "UI Upload", "fileUploadDesc": "<PERSON>elt gebruikers in staat om documenten rechtstreeks vanuit de Paperbox UI te uploaden, handig voor testdoeleinden.", "labelingMode": "Labeling Modus", "labelingModeDesc": "In de labelmodus worden diverse instellingen aangepast om het labelen en beoordelen van gelabelde documenten voor verdere training te optimaliseren.", "mailroom": "Mailroom Inbox", "mailroomDesc": "Activeer 'Mailroom' modus voor deze inbox."}, "system": "Systeem", "systemDescription": "Selecteer het systeem dat deze inbox zal gebruiken.", "tableCols": "Inbox Tabel kolommen", "tableColsDescription": "Configureer welke kolommen zichtbaar zijn voor de gebruikers in het documentoverzichtsscherm.", "tagTypes": "Tags", "tagTypesDescription": "Tags kunnen gebruikt worden om documenten te markeren met een specifieke status (bijv. Prioriteit, Status, ...). Je kunt deze tags ook gebruiken om je documenten te filteren.", "templateSelection": "Template <PERSON><PERSON>", "templates": {"brio": "Brio Inbox", "brioDescription": "Maak een inbox aan die afgestemd is op gebruik met Brio", "ccs": "CCS", "ccsDescription": "Maak een inbox aan die afgestemd is op gebruik met CCS", "custom": "Custom Inbox", "customDescription": "Maak een custom inbox aan die je later kunt configureren met je eigen instellingen."}, "title": "Inbox configuratie", "uploadMail": "Document upload email", "uploadMailDescription": "Emailadres gelinkt aan deze inbox", "useExistingConnector": "Bestaande Connector Gebruiken", "useExistingConnectorDescription": "Gebruik een bestaande connector om verbinding te maken met uw systeem.", "workflow": "Actieve workflow", "workflowDescription": "<PERSON><PERSON><PERSON> van de workflow die momenteel actief is op deze inbox."}, "masterdata": {"boost": "Boost", "boostDescription": "Definieert hoe relevant deze tabel is in vergelijking met de andere tabellen. Bij de lookup en search zal de tabel met de hoogste boost eerst worden weergegeven of prioriteit krijgen.  (0 - 10)", "config": "Configuratie", "copyId": "Masterdata-tabel-ID gekopieerd naar klembord.", "dangerZone": "Gevarenzone", "delete": "Verwijder de masterdata-tabel", "deleteDescription": "Het verwijderen van deze tabel verwijdert alle gegevens van Paperbox. Deze actie kan niet ongedaan worden gemaakt.", "downloadCSV": "Download CSV", "downloadCSVDescription": "Download de meest recent geüploade gegevens van deze tabel in CSV-formaat.", "errorExists": "<PERSON>r bestaat reeds een tabel met deze naam.", "errorGeneric": "Er trad een fout op, probeer opnieuw.", "errorMismatch": "Ontbrekende kolommen. Controleer en probeer het opnieuw.", "fileSelect": "Bestand Selecteren", "fileUpload": "Upload ", "fileUploaded": "Upload successvol", "fileUploading": "Uploading", "headerChars": "Gefilterde Karakters", "headerCharsInput": "<PERSON><PERSON><PERSON> een karakter in", "headerCharsTooltip": "Voeg karakters toe die gefilterd moeten worden bij het zoeken op deze specifieke kolom.", "headerDisplay": "Veld / Weergavenaam", "headerDisplayTooltip": "Als er geen veld of weergavenaam is ingesteld,\nzal deze masterdata kolom worden genegeerd.", "headerLabel": "Tabel label", "headerLabelTooltip": "<PERSON><PERSON> in de geüploade data", "headerMapping": "Mapping", "headerMappingTooltip": "<PERSON><PERSON><PERSON><PERSON> tussen:\n- <PERSON><PERSON> / Verborgen\n- <PERSON><PERSON>\n- Paperbox Veld", "headerPin": "Vastzetten", "headerPinTooltip": "Het vastzetten van een item zorgt ervoor dat het hoger in de zoekresultaten verschijnt.", "headerSearchable": "<PERSON><PERSON>ek<PERSON><PERSON>", "headerSearchableTooltip": "Bij het geb<PERSON>iken van de masterdata-zoekfunctie zal deze kolom vind<PERSON>ar zijn.", "headerType": "Type", "headerTypeTooltip": "Sc<PERSON><PERSON>t tussen zoekgeoptimaliseerde types:\n- Identifiers, zoals een claims-ID of SAP-nummer\n- Volledige Tekst, zoals een adres of naam", "id": "Id", "idDescription": "ID te gebruiken in verbonden services zoals webhook, API,... ", "latestUpload": "Laatste Upload", "latestUploadDescription": "Toont wanneer de gegevens voor deze tabel voor het laatst zijn bijgewerkt", "lookup": "Lookup", "lookupDescription": "<PERSON>n ingeschakeld, worden de gegevens in deze tabel gebruikt automatisch geraadpleegd tijdens het documentverwerkingsproces.", "name": "Masterdata-tabellen", "tableMapping": "Table Mapping", "tableMappingDescription": "Hier kunt u de mapping van uw masterdata-tabelgegevens configureren, zodat ze in Paperbox kunnen worden gebruikt.", "tableSearch": "<PERSON><PERSON>", "tableSearchDescription": "<PERSON><PERSON> binnen de geselecteerde tabellen", "title": "Voeg tabel toe", "totalLineCount": "Aantal rijen", "totalLineCountDescription": "Toont het totale aantal rijen dat is toegevoegd aan deze tabel in de laatste upload.", "uploadData": "Upload nieuwe data", "uploadDataDescription": "Maakt het mogelijk om nieuwe gegevens up te loaden voor deze tabel, die - na verwerking - gebruikt kan worden door de gebruikers."}, "metadataType": {"alreadyExists": "Metadatatype bestaat reeds.", "config": "Configuratie", "dangerZone": "Gevarenzone", "deleteType": "Verwijder metadatatype", "deleteTypeDescription": "Verwijderde metadatatypes kunnen niet meer worden gebruikt. Huidige velden van dit type zullen niet worden verwijderd.", "error": "Er trad een fout op, probeer opnieuw.", "hidden": "Intern", "hiddenDescription": "<PERSON><PERSON> aangeduid als 'intern', wordt dit veld verborgen voor gebruikers.\nDit kan worden gebruikt voor interne/aanvullende metadata die enkel relevant zijn voor verdere verwerking buiten Paperbox.", "id": "Id", "idDescription": "ID te gebruiken in verbonden services zoals webhook, API,...", "name": "<PERSON><PERSON>", "nameDescription": "<PERSON><PERSON> voor de gebruikers", "newType": "Nieuw metadatatype", "providerId": "Provider Id", "providerIdDescription": "ID te gebruiken in verbonden services. Als de provider Id niet ingevuld is, zal de gewone ID gebruikt worden.", "sensitive": "Gevoelige data", "sensitiveDescription": "Paperbox anonimiseert gevoelige data", "useTopologyPartsToAggregate": "Aggregeer naar bundel metadata", "useTopologyPartsToAggregateDescription": "<PERSON><PERSON> ing<PERSON>, zal Paperbox alle toegewezen waarden van dit metadata-veld binnen een bundel combineren tot één veld, volgens de gespecificeerde volgorde van opties.\nDi<PERSON> is nuttig in situaties zoals het gebruiken van de hoogste prioriteitswaarde van een metadata-veld over alle onderdelen in een bundel.", "valueType": "Value type", "valueTypeDescription": "The type of the value", "values": {"addDescription": "<PERSON>oer hier een nieuwe waarde toe die als optie kan worden gebruikt.", "addNew": "<PERSON><PERSON><PERSON>", "addTitle": "<PERSON><PERSON><PERSON> waarde", "description": "Dit zijn de opties die je krijgt bij het instellen van dit metadataveld bij een documenttype. \nDe volgorde waarin je ze plaatst, bepaalt hun prioriteit bij een conflict.", "error": "<PERSON><PERSON><PERSON> waarde bestaat reeds.", "title": "Toegestane Opties"}}, "multiSave": {"changeLogDescription": "Deze eigenschappen worden bij {{number}} item(s) aangepast.", "description": "Enkel eigenschappen die je hebt aangepast zullen worden toegepast, deze aanpassingen worden op {{number}} item(s) toegepast.", "title": "Weet je het zeker?", "unsavedChangeLogDescription": "De a<PERSON>en van deze eigenschappen gaan verloren."}, "multiSelect": {"confirm": "Bevestigen", "deselect": "Alles Desele<PERSON>en", "select": "Alles <PERSON>n"}, "notifications": {"updateError": "Update mislukt", "updateSuccess": "Update voltooid"}, "page": {"backToOverview": "Terug naar overzicht", "next": "Volgende", "previous": "Vorige"}, "tagType": {"alreadyExists": "Tag met deze naam bestaat reeds.", "color": "<PERSON><PERSON><PERSON>", "colorDescription": "Deze wordt gebruikt om tags visueel te onderscheiden in the UI.", "config": "Configuratie", "dangerZone": "Gevarenzone", "deleteType": "<PERSON><PERSON><PERSON><PERSON><PERSON> tag", "deleteTypeDescription": "Verwijderde tags kunnen niet meer worden gebruikt. Huidige tags van dit type zullen niet worden verwijderd.", "error": "Er trad een fout op, probeer opnieuw.", "id": "Id", "idDescription": "ID te gebruiken in verbonden services zoals webhook, API,...", "name": "<PERSON><PERSON>", "nameDescription": "<PERSON><PERSON> voor de gebruikers", "newType": "<PERSON><PERSON><PERSON> tag", "providerId": "Provider Id", "providerIdDescription": "ID te gebruiken in verbonden services. Als de provider Id niet ingevuld is, zal de gewone ID gebruikt worden."}, "tenant": {"apiKey": "API Key", "apiKeyDescription": "Haal de API key op die kan worden gebruikt om API requests naar deze workspace te authenticeren.<1/><0>Zie de documentatie voor meer informatie.</0>", "domains": "<PERSON><PERSON>", "domainsAddDescription": "Voeg een domein toe aan de whitelist. bvb. gmail.com", "domainsAddTitle": "<PERSON><PERSON>", "domainsDescription": "<PERSON> domeinen in deze whitelist in te stellen kunnen gebruikers met dit e-maildomein inloggen op de workspace.", "fetchApiKey": "API key ophalen", "fetchPrivateKey": "Private key ophalen", "general": "Algemene instellingen", "id": "Id", "idDescription": "Het ID voor deze workspace", "language": "Standaardtaal", "languageDescription": "Hier kunt u de standaardtaal instellen die in de UI wordt gebruikt voor de gebruikers, gebruikers kunnen hun taal later handmatig wijzigen.", "locale": "Lokalisatie", "name": "<PERSON><PERSON>", "nameDescription": "Configureer de naam van uw workspace, deze wordt gebruikt in gepersonaliseerde e-mails, wachtwoordresets, gebruikersuitnodigingen, enz.", "netlifyBeta": "Bètafuncties", "netlifyBetaDescription": "Schakel Paperbox bètafuncties in of uit voor alle gebruikers binnen deze tenant. Wijzigingen worden toegepast na het herladen van de pagina.", "privateKey": "Private Key", "privateKeyDescription": "Haal de private key op die kan worden gebruikt om op beveiligde manier JWT Tokens aan te maken.<1/><0>Zie de documentatie voor meer informatie.</0>", "timeZone": "Tijdzone", "timeZoneDescription": "Het instellen van de tijdzone van uw workspace heeft invloed op het dashboard en de rapportering", "title": "Workspace instellingen"}, "unsavedChanges": {"description": "Het lijkt erop dat je iets aan het bewerken was. Als je weggaat voordat je hebt opgeslagen, gaan je wijzigingen verloren.", "title": "<PERSON>et op<PERSON>lagen a<PERSON>passingen"}, "users": {"adminAccess": "Adminrechten", "createUser": "Geb<PERSON><PERSON><PERSON>", "creatingUser": "Toevoegen", "deleteText": "De gebruiker zal niet meer kunnen inloggen. Deze actie kan niet ongedaan gemaakt worden.", "emailSent": "<PERSON><PERSON>", "filter": "Gebruikers filteren", "inboxes": {"addDescription": "Selecteer de inboxen die je wilt toewijzen aan deze gebruiker", "addTitle": "Inbox Aanwijzen", "title": "Inboxen"}, "resetText": "Verzend een password-reset email naar deze gebruiker", "success": "Success", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "webhookValues": {"@ACTION": "Actie die werd uitgevoerd op het document: 'approve', 'delete' of 'bounce'.", "@ACTION_METADATA": "Metadata van de actie die werd uitgevoerd op het document.", "@ACTOR": "<PERSON><PERSON> van de verwerker. Dit kan 'paperbox' of een gebruiker zijn.", "@CONFIDENCE": "<PERSON>rheid waarmee het document werd verwerkt. Waarde tussen 0 en 1.", "@DOCUMENT_CLASS": "Documenttype van het verwerkte document.", "@DOCUMENT_SUBCLASS": "Subdoctype van het verwerkte document.", "@ENTITIES": "De gedetecteerde velden van het verwerkte document.", "@ID": "ID van het document.", "@METADATA": "Metadata van het verwerkte document.", "@MUTATION_ID": "De id van een mutation (als het verwerkte document geen origineel is).", "@MUTATION_TYPE": "Het type mutation (als het verwerkte document geen origineel is).", "@PAPERBOX_ID": "Paperbox ID van het document.", "@PB_JSON_PAYLOAD": "Standaard Paperbox JSON Payload", "@PB_PRIMITIVE_JSON_PAYLOAD": "Primitive Paperbox JSON Payload", "@PB_RANDOM_UUIDv4": "Een random UUIDv4 ID", "@PB_STRING_PAYLOAD": "Stringified Paperbox Payload", "@PROCESSED_DATE": "Verwerkingsdatum van het document (ISO string).", "placeholder": "Voeg data of text in."}, "webhooks": {"BRIO_NOT_SUPPORTED": "<PERSON><PERSON><PERSON><PERSON> van een Brio webhook is niet mogelijk.", "add": "Webhook aanmaken", "auth": {"customPayload": "OAuth Payload", "customPayloadDescription": "Definieer de values die in het OAuth-verzoek worden meegegeven.", "dataConfig": "Dataconfiguratie", "extend": "Connector Auth instellingen uitbreiden", "extendDescription": "<PERSON><PERSON><PERSON> deze optie in om de configuratie die is ingesteld in uw geselecteerde Connector uit te breiden.", "headers": "Headers", "headersDescription": "Voeg headers toe", "queryParams": "Query parameters", "queryParamsDescription": "Deze parameters worden als queryparameters aan uw URL toegevoegd.", "responseTokenKey": "Response Token key", "responseTokenKeyDescription": "<PERSON><PERSON> 'key' in de OAuth Response die de authenticatietoken bevat.", "title": "OAuth <PERSON>", "url": "Authenticatie-URL", "urlDescription": "De URL die gebruikt wordt bij het maken van het authenticatieverzoek."}, "brio": {"activitySettings": {"description": "Configureer de standaardinstellingen voor nieuwe activiteiten", "rds": "<PERSON><PERSON><PERSON>", "rdsDescription": "<PERSON><PERSON><PERSON> de <PERSON> in van de standaard Brio-dienst die moet worden gebruikt wanneer het beheerderveld leeg is.", "title": "Activiteit Instellingen"}, "docTypeConfig": {"add": "Document type toevoegen", "addDescription": "Add a new document type to the Brio configuration", "addTitle": "Add new document type", "createActivity": "Activiteit aanmaken", "description": "Configureer de verwerking van verschillende documenttypes in BRIO.", "docType": "Document type", "hierarchy": "Hierarchy voorkeur", "options": {"claims": "<PERSON><PERSON><PERSON>", "contracts": "Contract", "documents": "Document", "parties": "Partij"}, "title": "Documenttype Configuratie"}, "metadataActivityMapping": {"archive_code": "Archiveercode", "archive_codeDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de archiveercode in Brio.", "description": "Beschrijving", "descriptionDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de activiteitbeschrijving in Brio.", "due_date_days_delta": "Vervaldatum Dagen Delta", "due_date_days_deltaDescription": "Selecteer het metadatatype waarvan de numerieke waarde wordt toegevoegd in dagen aan de ontvangstdatum van het document om de vervaldatum van de activiteit te bepalen.", "handler": "<PERSON><PERSON><PERSON><PERSON>", "handlerDescription": "Selecteer het metadatatype waar<PERSON> de waarde de Brio-gebruiker bepaalt waaraan de activiteit is toegewezen.", "is_completed": "Is Voltooid", "is_completedDescription": "Selecteer het metadatatype waar<PERSON> de waarde aangeeft of dit item is voltooid in Brio.", "is_visible_my_broker": "Zichtbaarheid Activiteit My Broker", "is_visible_my_brokerDescription": "Selecteer het metadatatype waar<PERSON> de waarde aangeeft of een activiteit zichtba<PERSON> zal zijn in My Broker vanuit Brio.", "priority": "Prioriteit", "priorityDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de activiteitprioriteit in Brio.", "sub_type": "Subtype", "sub_typeDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als het subtype in Brio.", "title": "Koppel de geconfigureerde Paperbox-metadata aan de corresponderende Brio-activiteitswaarden.", "type": "Type", "typeDescription": "Selecteer het metadatatype waar<PERSON> de waarde het type activiteit bepaalt dat wordt aangemaakt."}, "metadataDocumentMapping": {"archive_delay": "Archiveringsvertraging", "archive_delayDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de archiveringsvertraging in Brio.", "carrier_type": "Documentmedium", "carrier_typeDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als het documentmedium in Brio.", "category": "Categorie", "categoryDescription": "Selecteer het metadatatype wa<PERSON><PERSON> de waarde wordt weergegeven als de categorie in Brio; dit is gebaseerd op Brio-tabel 962.", "delete_delay": "Verwijdervertraging", "delete_delayDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de verwijdervertraging in Brio.", "description": "Beschrijving", "descriptionDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de documentbeschrijving in Brio.", "description_my_broker": "Besch<PERSON>jving My Broker", "description_my_brokerDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de brokerbeschrijving in Brio.", "is_favorite": "Is Favoriet", "is_favoriteDescription": "Selecteer het metadatatype wa<PERSON><PERSON> de waarde aangeeft of dit een favoriet is in Brio.", "is_visible_my_broker": "Zichtbaarheid Document My Broker", "is_visible_my_brokerDescription": "Selecteer het metadatatype wa<PERSON><PERSON> de waarde aangeeft of een document zichtbaar zal zijn in My Broker vanuit Brio.", "language": "Taal", "languageDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de taal in Brio.", "origin": "Oorsprong", "originDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de oorsprong in Brio; is het document ontvangen of verzonden?", "qualifier": "Brio Doctype", "qualifierDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als het Brio Doctype in Brio; dit is geb<PERSON><PERSON> op Brio-tabel 970.", "reference": "Referentie", "referenceDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als de referentie in Brio.", "security_level": "Beveiligingsniveau", "security_levelDescription": "Selecteer het metadatatype waar<PERSON> de waarde wordt weergegeven als het beveiligingsniveau in Brio.", "title": "Stel mappings in tussen Paperbox metadata-velden en Brio-velden, zodat wanneer een document wordt verwerkt, gespecificeerde metadatawaarden automatisch worden ingevuld in Brio."}, "metadataHierarchy": {"description": "Koppel de geconfigureerde Paperbox-velden uit de referentiële data aan de juiste BRIO-locaties.", "title": "Referentiële Data Mapping"}}, "brioEndpointId": "Brio Endpoint ID", "brioEndpointIdDescription": "De interne ID van het Brio Endpoint die gebruikt wordt voor deze webhook.", "ccs": {"agendaMapping": {"description": "Koppel de Paperbox-metadata aan de CCS-agendavelden voor automatische invulling.", "fields": {"create_agenda": "Agenda a<PERSON>maken", "create_agendaDescription": "Selecteer het metadata-type om te bepalen of een agenda moet worden aangemaakt.", "description": "Beschrijving", "descriptionDescription": "Selecteer het metadata-type voor de agenda beschrijving in CCS.", "due_date_delta": "Vervaldatum-delta", "employee_number": "Werknemersnummer", "employee_numberDescription": "Selecteer het metadata-type voor het werknemersnummer.", "reason": "Reden", "reasonDescription": "Selecteer het metadata-type voor de agend reden in CCS."}, "title": "CCS-agenda-mapping"}, "archiveMapping": {"description": "\n<PERSON><PERSON> de Paperbox-metadata aan de CCS-archiefvelden", "fields": {"description": "Description", "descriptionDescription": "Selecteer het metadata-type voor de archiefbeschrijving in CCS.", "document_type": "Documenttype", "document_typeDescription": "Selecteer het metadata-type voor het documenttype in CCS.", "is_secretDescription": "Selecteer het metadata-type om te bepalen of het archief als 'geheim' is gemarkeerd."}, "title": "CCS-archiefmapping"}, "claimMapping": {"description": "Koppel de Paperbox-metadata aan de CCS-claimvelden", "fields": {"claim_number_office": "<PERSON><PERSON><PERSON><PERSON><PERSON> kantoor", "claim_number_officeDescription": "Selecteer het metadata-type voor het kantoor claimnummer in CCS.", "company_number": "Bedrijfsnummer", "company_numberDescription": "Selecteer het metadata-type voor het bedrijfsnummer in CCS.", "intermediary_person_number": "Intermediair persoonnummer", "intermediary_person_numberDescription": "Selecteer het metadata-type voor het intermediair persoonnummer in CCS."}, "title": "CCS-claim mapping"}, "docTypeConfig": {"add": "Document type toevoegen", "addDescription": "Voeg een nieuw documenttype toe aan de CCS-configuratie", "addTitle": "Nieuw documenttype toevoegen", "agendaCreate": "Agenda a<PERSON>maken", "agendaReason": "Agenda Reden", "description": "Configureer de verwerking van verschillende documenttypes in BRIO.\n\n", "docType": "Documenttype", "title": "Documenttype Configuratie\n\n"}}, "connection": "Verbindingsinstellingen", "connector": "Connector", "connectorDescription": "Selecteer welke connector wordt gebruikt voor deze webhook.\nAlle webhook-calls zullen de instellingen/parameters gebruiken die zijn ingesteld in de connector.", "customPayload": "Custom payload", "customPayloadDescription": "Stel een custom payload samen", "dangerZone": "Gevarenzone", "dataConfig": "Data Configuratie", "dataExtend": "Connector Data instellingen uitbreiden", "dataExtendDescription": "<PERSON><PERSON><PERSON> deze optie in om de configuratie die is ingesteld in uw geselecteerde Connector uit te breiden.", "defaultPayload": "Standaard payload", "defaultPayloadDescription": "Gebruik de standaard Paperbox payload.", "delete": "Webhook verwijderen", "deleteDescription": "Het verwijderen van een actieve webhook zal alle bestaande verbindingen met het gekoppelde systeem verbreken.\nZorg ervoor dat u deze webhook niet meer nodig heeft voordat u deze verwijdert.\n\n", "dependsOn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van", "dependsOnDescription": "Selecteer de webhooks die moeten worden getriggerd voordat deze webhook kan worden getriggerd.", "enabled": "Actief", "enabledDescription": "<PERSON><PERSON><PERSON> de webhook in of uit, wanneer een webhook is uitgeschakeld zal deze niet meer gebruikt worden.", "generalInfo": "Algemene info", "headers": "Headers", "headersDescription": "Voeg headers toe, deze zullen toegevoegd worden aan de reeds bestaande headers van uw connector", "id": "Id", "idDescription": "Het Interne ID voor deze webhook", "name": "<PERSON><PERSON>", "nameDescription": "<PERSON><PERSON>", "queryParams": "Query parameters", "queryParamsDescription": "Voeg parameters toe aan de URL query, deze zullen toegevoegd worden aan de reeds bestaande query parameters van uw connector", "request": "Request", "save": "Opsla<PERSON>", "saving": "Opsla<PERSON>", "selectDocTypes": "Selecteer Document Types", "selectDocTypesDescription": "Kies bij welke document types (bundle) deze webhook van toepassing is.", "selectInboxes": "Selecteer Inboxen", "selectInboxesDescription": "<PERSON>es bij welke inboxen deze webhook van toepassing is.", "selectTables": "Selecteer <PERSON><PERSON><PERSON>", "selectTablesDescription": "<PERSON>es bij welke gelinkte tabellen de webhook van toepassing is.", "title": "Webhook configuratie", "triggerActionTypes": "Trigger <PERSON>ie Types", "triggerActionTypesDescription": "De webhook wordt alleen geactiveerd bij de geselecteerde actietypes wanneer het actie event wordt getriggerd. Standaard worden alle actietypes getriggerd.", "triggerActions": "Acties", "triggerDocTypes": "Document Types", "triggerDocTypesDescription": "Kies bij welke document types (bundle) deze webhook van toepassing is.", "triggerEvent": "Trigger events", "triggerEventDescription": "Specificeer de events die deze webhook triggeren. De webhook wordt alleen geactiveerd bij de geselecteerde events", "triggerInboxes": "Inboxen", "triggerInboxesDescription": "Definieer de inboxen waarop deze webhook moet worden getriggerd.", "triggerTables": "Tabellen", "triggers": "Triggers", "unflatten": "Unflatten", "unflattenDescription": "<PERSON><PERSON><PERSON> met dubbele underscores worden standaard nested dictionaries. \nZet deze optie uit om dit gedrag te wijzigen.", "url": "URL Pad", "urlDescription": "Het pad dat wordt gebruikt voor de webhook.\nDit pad wordt toegevoegd aan de base-URL die is ingesteld in de geselecteerde connector"}}