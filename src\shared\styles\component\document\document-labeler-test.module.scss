@import "../../vars/_vars";

.container {

  display: flex;
  flex-direction: column;
  height: 100vh;
}

.body {
  display: flex;
  flex: 1;
  width: auto;
  height: calc(100% - 50px);
}

.sidebar {
  width: 300px;
  height: 100%;
  background: $error;

  button {
    width: 20px;
    height: 20px;
  }
}

.main {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  height: 100%;
  background: green;

}

.document {
  flex: 1 1 auto;
  overflow: auto;
  width: calc(100% - 10px);
  height: 100%;
  margin-right: 10px;
  background: purple;

  &::-webkit-scrollbar {
    width: 10px;
    border-radius: 70px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 70px;
  }

  &::-webkit-scrollbar-thumb {
    background: $medium-gray;
    border-radius: 5px;
  }
}

.page {
  margin: 0 auto;
  padding: 20px;
  background: grey;

  img {
    max-width: 100%;
    max-height: 100%;

  }

}

.canvas {
}

.handle {
  position: absolute;
  z-index: 1000;
  left: 50%;
  width: 20px;
  height: 10px;
  background: orange;
}

.panel {
  flex-grow: 1;
  flex-shrink: 1;
  width: 100%;
  height: auto;
  min-height: 60px;
  background: yellow;

}

.header {
  flex-shrink: 0;
  width: 100%;
  height: 50px;
  background: black;
}
