import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import { demoDocTypesEN, demoDocTypesNL, demoUserNames, getUserToken } from '@shared/helpers/helpers';
import { getAllTypes, getUserActivity } from '@shared/store/dashboardSlice';
import { api } from '@shared/store/setup/firebase-setup';
import { useDispatch, useSelector } from '@shared/store/store';
import sc from '@shared/styles/component/inbox/inbox-content.module.scss';
import s from '@shared/styles/component/inbox/inbox-dashboard.module.scss';
import { ReactComponent as InfoIcon } from '@svg/info-icon.svg';
import clsx from 'clsx';
import { EChartsOption, SeriesOption } from 'echarts';
import ReactEChartsCore from 'echarts-for-react/lib/core';
import { BarChart } from 'echarts/charts';
import { GridComponent, TitleComponent, TooltipComponent } from 'echarts/components';

import * as echarts from 'echarts/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>enderer } from 'echarts/renderers';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useDeepCompareEffect } from 'use-deep-compare';
import { FlowComponentHandle } from './DashboardFlow.tsx';
import { AnalyticsGrouping, AnalyticsPeriod, ColoredInbox } from './InboxDashboard';

interface Props {
  inboxes: ColoredInbox[];
  activeDateRange: AnalyticsPeriod;
}
interface InboxAggs {
  total: number;
  segments: GroupSegment[];
  inboxId: string;
}

interface GroupSegment {
  id: string;
  value: number;
}

const fieldMap = {
  user: 'action.actor_id',
  docType: 'doc_type_id',
  tag: 'tag_type_id',
  date: 'action.timestamp',
  action: 'action.type',
};

export const createGradient = (color: string) => {
  return new echarts.graphic.LinearGradient(
    0,
    0,
    0,
    1,
    [
      { offset: 1, color: color },
      { offset: 0.7, color: color },
      { offset: 0, color: echarts.color.lift(color, 0.3) },
    ],
    false,
  );
};

echarts.use([TitleComponent, TooltipComponent, GridComponent, BarChart, CanvasRenderer, SVGRenderer]);

const DashboardOutflow = React.forwardRef<FlowComponentHandle, Props>(({ inboxes, activeDateRange }, ref) => {
  const [activeGrouping, setActiveGrouping] = useState<AnalyticsGrouping>('user');
  const mappingData = useSelector((state) => state.dashboard.mappingData);
  const userActivity = useSelector((state) => state.dashboard.userActivity);
  const userActivitySub = useSelector((state) => state.subs.userActivitySub);
  const demoMode = useSelector((state) => state.dashboard.demoMode);

  const [chartData, setChartData] = useState<InboxAggs[]>([]);
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(getUserActivity());
  }, [dispatch]);

  useEffect(() => {
    return () => {
      if (userActivitySub) userActivitySub();
    };
  }, [userActivitySub]);

  useDeepCompareEffect(() => {
    if (inboxes) {
      let type;
      if (activeGrouping === 'date') {
        return;
      }
      if (activeGrouping === 'tag') {
        type = 'tag_types';
      } else if (activeGrouping === 'docType') {
        type = 'doc_types';
      } else if (activeGrouping === 'user') {
        type = 'users';
      } else if (activeGrouping === 'action') {
        type = 'action';
      }
      if (type) {
        dispatch(getAllTypes(type));
      }
    }
  }, [dispatch, inboxes, activeGrouping]);

  const fetchFunc = useCallback(async () => {
    const b = await getUserToken();
    if (!b) return;
    return api
      .get(`${import.meta.env.VITE_PAPERBOX_ANALYTICS_URL}/dashboard/outflow`, {
        params: {
          searchField: fieldMap[activeGrouping],
          startDate: activeDateRange[0].toISOString(),
          endDate: activeDateRange[1].toISOString(),
          inboxes: inboxes.length > 0 ? inboxes.map((e) => e.id) : null,
          demoMode: demoMode,
        },
        headers: {
          Authorization: `Bearer ${b}`,
        },
      })
      .then((res) => {
        setChartData(res.data);
        return res;
      });
  }, [demoMode, activeGrouping, activeDateRange, inboxes]);

  React.useImperativeHandle(ref, () => ({
    fetchFunc,
  }));

  useEffect(() => {
    fetchFunc();
  }, [fetchFunc]);

  const transformInboxAggsToChartData = useCallback(
    (inboxAggsData: InboxAggs[]): any[] => {
      const uniqueSegmentIds = Array.from(
        new Set(inboxAggsData.flatMap((item) => item.segments.map((segment) => segment.id))),
      );

      const data = uniqueSegmentIds.map((segmentId) => {
        let total = 0;
        const value = inboxAggsData.map((inboxAgg) => {
          const matchingSegment = inboxAgg.segments.find((segment) => segment.id === segmentId);
          const val = matchingSegment ? matchingSegment.value : 0;
          total += val;
          return {
            segment: inboxAgg.inboxId,
            value: val,
            color: inboxes.find((e) => e.id === inboxAgg.inboxId)?.color ?? '#FFFFFF00',
          };
        });

        return {
          name: segmentId,
          value: value,
          total: total,
        };
      });
      if (activeGrouping === 'date') {
        data.sort((a, b) => {
          return Number.parseInt(a.name) - Number.parseInt(b.name);
        });
      }
      return data;
    },
    [activeGrouping, inboxes],
  );

  const transformedData = useMemo(
    () => transformInboxAggsToChartData(chartData),
    [chartData, transformInboxAggsToChartData],
  );

  const sortedData = useMemo(() => {
    let limit = 25;
    if (activeGrouping === 'date') {
      limit = 100;
    }
    if (!transformedData || transformedData.length === 0) return [];
    const tempData = transformedData;
    const moreThanLimit = tempData.length > limit ? tempData.length - limit : 0;

    const mappedList = tempData
      .map((category, i) => {
        let name = category.name;
        if (activeGrouping === 'docType' && demoMode) {
          const mapping = i18n.language === 'en' ? demoDocTypesEN : demoDocTypesNL;
          name = mapping[i % 20];
        }
        return {
          name: name,
          value: category.value.sort((a, b) => a.value - b.value),
        };
      })
      .sort((a, b) => {
        if (activeGrouping === 'date') {
          return a.name - b.name;
        }

        const aSum = a.value.reduce((acc, item) => acc + item.value, 0);
        const bSum = b.value.reduce((acc, item) => acc + item.value, 0);
        return bSum - aSum;
      });
    const list: any[] = mappedList.slice(0, limit);
    if (moreThanLimit > 0) {
      const segments: Record<string, any> = {};
      mappedList.slice(-moreThanLimit).forEach((e) => {
        e.value.forEach((item) => {
          segments[item.segment] = {
            value: item.value + (segments[item.segment]?.value ?? 0),
            color: item.color,
          };
        });
      });
      const mappedSegments = Object.entries(segments)
        .map(([k, v]) => {
          return { segment: k, value: v.value, color: v.color };
        })
        .sort((a, b) => {
          return a.value - b.value;
        });

      list.push({
        name: `${moreThanLimit} Others`,
        list: [],
        value: mappedSegments,
      });
    }

    return list;
  }, [activeGrouping, transformedData]);

  const seriesConfig: SeriesOption = useMemo(
    () =>
      sortedData[0]?.value.map((_, index) => {
        return {
          type: 'bar',
          stack: 'Total',
          realtimeSort: false,
          barMaxWidth: 100,
          data: sortedData.map((category) => {
            const currentSegment = category.value[index];
            if (!currentSegment) {
              return {
                value: 0,
              };
            }
            const isTopBar = currentSegment.value === Math.max(...category.value.map((s) => s.value));
            const color = currentSegment?.color ?? '#FFFFFF00';
            return {
              ...currentSegment,
              itemStyle: {
                color: isTopBar ? createGradient(color) : color,
                borderRadius: isTopBar ? [5, 5, 0, 0] : [0, 0, 0, 0],
                borderWidth: 0.5,
                borderType: 'solid',
                borderColor: '#ffffff',
              },
            };
          }),
        } as SeriesOption;
      }),
    [sortedData],
  );
  const chartInstance = useRef(null);

  // Calculate the rotation angle
  const calculateRotationAngle = () => {
    try {
      const xAxisWidth = chartInstance.current?.getWidth();
      if (xAxisWidth == null) return 0;
      const totalLabels = transformedData.length;
      const longestLabel = transformedData.reduce((maxLabel, category) => {
        let currentLabel;
        if (activeGrouping === 'date') {
          currentLabel = formatDateLabel(category.name);
        } else {
          currentLabel = mappingData[category.name]?.name ?? category.name;
        }
        if (demoMode) {
          if (activeGrouping === 'user') {
            currentLabel = 'Demo User';
          }
        }
        return currentLabel.length > maxLabel.length ? currentLabel : maxLabel;
      }, '');
      const labelWidth = 9 * longestLabel.length;
      const requiredWidth = totalLabels * labelWidth;

      return requiredWidth > xAxisWidth ? 30 : 0;
    } catch (e) {
      return 0;
    }
  };

  const formatDateLabel = (dateValue: number) => {
    if (typeof dateValue === 'number') {
      return new Date(dateValue).toLocaleString('nl-BE', {
        day: '2-digit',
        month: '2-digit',
      });
    }
    return '';
  };

  const chartOptions: EChartsOption = {
    grid: {
      borderWidth: 100,
      left: 100,
      right: 100,
      bottom: 100,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params) => {
        const sortedParams = params.sort((b, a) => a.data.value - b.data.value);
        const str = `<div style="padding-bottom: 10px;font-weight: bold">${params[0].axisValue}</div>`;
        const items = sortedParams
          .filter((p) => p.value !== 0)
          .map(
            (p) =>
              `<div><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                p.data.color
              };"></span>${inboxes.find((e) => e.id === p.data.segment)?.settings.name ?? p.data.segment}: ${
                p.data.value
              }</div>`,
          )
          .join('');
        return str + items;
      },
    },
    xAxis: {
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },

      type: 'category',
      data: sortedData.map((category, i) => {
        if (category.name === 'paperbox') return 'Paperbox';
        if (category.name === 'provider') return 'API';
        if (activeGrouping === 'date') {
          return formatDateLabel(category.name) ?? category.name;
        }
        let name = mappingData[category.name]?.name;
        if (!name) name = category.name || '';
        if (demoMode) {
          if (activeGrouping === 'user') {
            name = demoUserNames[i % 9];
          }
        }

        return name;
      }),
      axisLabel: {
        interval: 0,
        rotate: calculateRotationAngle(),
        fontWeight: 600,
        fontFamily: 'Yantramanav',
        margin: 15,
        fontSize: 12,
        rich: {
          active: {
            color: '#91C500',
          },
          inactive: {
            width: 0,
            color: 'transparent',
          },
        },
        formatter: (l, i) => {
          const obj = sortedData[i]?.name;
          const date = userActivity[obj];
          let state = 'inactive';
          if (date) {
            const delta = new Date().getTime() - 15 * 1000 * 60;
            if (date.getTime() > delta) {
              state = 'active';
            }
          }
          if (l.length <= 20) return `{${state}|●} {value|${l}}`;
          const preservedEndLength = 4;
          const truncationLength = 20 - preservedEndLength - 3;
          return `{${state}|●} {value|${l.slice(0, truncationLength)}...${l.slice(
            l.length - preservedEndLength,
          )}}`;
        },
      },
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      axisLabel: {
        padding: 10,
        fontWeight: 600,
        fontSize: 12,
        fontFamily: 'Yantramanav',
      },
    },
    series: seriesConfig,
  };

  return (
    <div className={sc.container}>
      <div className={sc.sub_header}>
        <div className={sc.sub_info}>
          <h2 className={sc.sub_title}> {t('home:dashboard.outflowTitle')}</h2>

          <Tooltip
            position={'right'}
            alignment="start"
            content={
              <>
                <b
                  style={{
                    fontWeight: 500,
                    fontSize: 15,
                    marginBottom: 10,
                    display: 'block',
                  }}
                >
                  {t('home:dashboard.outflowTitle')}
                </b>
                <p style={{ maxWidth: 350 }}>{t('home:dashboard.outflowDescription')}</p>
              </>
            }
          >
            <div>
              <InfoIcon />
            </div>
          </Tooltip>
        </div>
        <div className={clsx(sc.tags_wrapper, sc.tags_wrapper__right)}>
          <div className={sc.tags}>
            <>
              <button
                onClick={() => setActiveGrouping('user')}
                key={'user'}
                className={clsx(sc.tag, { [sc.tag__active]: activeGrouping === 'user' })}
              >
                {t('home:dashboard.groupUser')}
              </button>
              <button
                onClick={() => setActiveGrouping('docType')}
                key={'doctype'}
                className={clsx(sc.tag, { [sc.tag__active]: activeGrouping === 'docType' })}
              >
                {t('home:dashboard.groupDocType')}
              </button>
              <button
                onClick={() => setActiveGrouping('tag')}
                key={'tag'}
                className={clsx(sc.tag, { [sc.tag__active]: activeGrouping === 'tag' })}
              >
                {t('home:dashboard.groupTag')}
              </button>
              <button
                onClick={() => setActiveGrouping('date')}
                key={'date'}
                className={clsx(sc.tag, { [sc.tag__active]: activeGrouping === 'date' })}
              >
                {t('home:dashboard.groupDate')}
              </button>{' '}
              <button
                onClick={() => setActiveGrouping('action')}
                key={'type'}
                className={clsx(sc.tag, { [sc.tag__active]: activeGrouping === 'action' })}
              >
                {t('home:dashboard.groupAction')}
              </button>
            </>
          </div>
        </div>
      </div>
      <div className={s.chart_wrapper}>
        {transformedData?.length > 0 ? (
          <ReactEChartsCore
            echarts={echarts}
            lazyUpdate
            style={{ width: '100%', height: '100%' }}
            onChartReady={(instance) => {
              chartInstance.current = instance;
            }}
            notMerge
            option={chartOptions}
          />
        ) : (
          <div className={s.no_data}>{t('home:dashboard.noData')}</div>
        )}
      </div>
    </div>
  );
});

export default DashboardOutflow;
