import Tooltip from '@components/shared/tooltip/Tooltip';
import { getRawPDF } from '@shared/services/documentService.ts';
import s from '@shared/styles/component/inbox/inbox-overlay.module.scss';
import { ReactComponent as CrossIcon } from '@svg/cross-icon.svg';
import { ReactComponent as DownloadIcon } from '@svg/download.svg';
import clsx from 'clsx';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';
import { CSSTransition } from 'react-transition-group';

const ActionButton = memo(
  ({
    icon: Icon,
    onClick,
    tooltip,
    className,
  }: {
    icon: React.FC<React.SVGProps<SVGSVGElement>>;
    onClick: () => void;
    tooltip: string;
    className?: string;
  }) => (
    <Tooltip position="top" content={tooltip}>
      <button onClick={onClick} className={clsx(s.button, className)}>
        <Icon />
      </button>
    </Tooltip>
  ),
);

interface Props {
  checkedRows: Set<string>;
  clearRows: () => void;
}

const InboxTableToolbarHistorical: React.FC<Props> = memo(({ checkedRows, clearRows }) => {
  const { inboxId } = useParams();
  const { t } = useTranslation();

  const handleDownload = async (documentId: string) => {
    const res = await getRawPDF(inboxId, documentId);
    const filename = res.headers['content-disposition']?.match(/filename=(.+)/)[1];
    const url = window.URL.createObjectURL(res.data);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  const handleBatchAction = (action: (documentId: string) => void) => {
    checkedRows.forEach(action);
    clearRows();
  };

  return (
    <CSSTransition
      timeout={150}
      in={checkedRows.size > 0}
      classNames="inbox-overlay-anim"
      unmountOnExit
      mountOnEnter
    >
      <div className={s.container}>
        <CrossIcon data-testid="inbox-overlay-close" className={s.cross} onClick={clearRows} />
        <div className={s.counter} data-testid="inbox-overlay-count">
          {checkedRows.size}
        </div>
        <p className={s.text}>{checkedRows.size > 1 ? 'documents ' : 'document '}selected</p>

        <div className={s.actions}>
          <ActionButton
            icon={DownloadIcon}
            tooltip={t('document:typeSwitch.download')}
            onClick={() => handleBatchAction(handleDownload)}
          />
        </div>
      </div>
    </CSSTransition>
  );
});

export default InboxTableToolbarHistorical;
