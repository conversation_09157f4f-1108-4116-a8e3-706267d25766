import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { IRawDocument } from '@shared/helpers/converters/document.ts';
import { cloneDeep } from 'lodash';
import { DateRange } from 'rsuite/DateRangePicker';
import { subDays } from 'rsuite/esm/utils/dateUtils';

export interface InboxFilterState {
  docTypeId?: string;
  subTypeId?: string;
  searchTerm?: string;
  activeTagId?: string;
  action?: 'delete' | 'bounce' | 'approve';
  sortBy?: string;
  actorId?: string;
  actorIdFilterMode?: 'include' | 'exclude';
  isSortDescending?: boolean;
  dateRange?: DateRange;
  approvalChecks?: string[];
  currentPageIndex: number;
  pageSize: number;
  navDirection?: 'back' | 'forward';
}

export interface InboxPaginationCountsState {
  totalDocCount: number;
  filteredDocCount: number;
  pageCount: number;
}

interface DocumentState {
  filters: InboxFilterState;
  documentList: any[]; // Your document list structure
  listFirstDoc?: IRawDocument;
  listLastDoc?: IRawDocument;
  selectedMutationId: string;
  activeDocumentPage: number;
  paginationCounts: InboxPaginationCountsState;
  pageImagesMap?: Record<string, Record<string, { imageUrl?: string; thumbUrl?: string }>>;
  isThumbsLoading: boolean;
  isImageLoading: boolean;
  // Per-document and per-page loading states for better coordination
  thumbsLoadingByDoc: Record<string, boolean>;
  imageLoadingByPage: Record<string, boolean>; // key format: "docId-pageNo"
}

const initialPaginationFilters = {
  pageSize: 10,
  currentPageIndex: 0,
};

const initialState: DocumentState = {
  filters: {
    sortBy: null,
    isSortDescending: false,
    searchTerm: '',
    docTypeId: '',
    subTypeId: null,
    activeTagId: null,
    action: null,
    actorId: null,
    actorIdFilterMode: null,
    dateRange: [subDays(new Date(), 29), new Date()],
    ...initialPaginationFilters,
  },
  paginationCounts: {
    totalDocCount: 0,
    filteredDocCount: 0,
    pageCount: 0,
  },
  documentList: [],
  activeDocumentPage: 1,
  selectedMutationId: 'original',
  pageImagesMap: {},
  isThumbsLoading: false,
  isImageLoading: false,
  thumbsLoadingByDoc: {},
  imageLoadingByPage: {},
};

export const documentListSlice = createSlice({
  name: 'documentList',
  initialState,
  reducers: {
    setActiveDocumentPage: (state, action: PayloadAction<number>) => {
      state.activeDocumentPage = action.payload;
    },
    setSelectedMutationId: (state, action: PayloadAction<string>) => {
      state.selectedMutationId = action.payload;
    },
    setDocTypeId: (state, action: PayloadAction<string | undefined>) => {
      state.filters.docTypeId = action.payload;
    },
    setSubTypeId: (state, action: PayloadAction<string | undefined>) => {
      state.filters.subTypeId = action.payload;
    },
    setActiveTagId: (state, action: PayloadAction<string | undefined>) => {
      state.filters.activeTagId = action.payload;
    },
    setSortBy: (state, action: PayloadAction<string>) => {
      state.filters.sortBy = action.payload;
    },
    setSortDescending: (state, action: PayloadAction<boolean>) => {
      state.filters.isSortDescending = action.payload;
    },

    setListLastDoc: (state, action: PayloadAction<IRawDocument>) => {
      state.listLastDoc = action.payload;
    },
    setListFirstDoc: (state, action: PayloadAction<IRawDocument>) => {
      state.listFirstDoc = action.payload;
    },

    setDocumentList: (state, action: PayloadAction<any[]>) => {
      state.documentList = action.payload;
    },
    // --- Patch Reducers ---
    updateFilters: (state, action: PayloadAction<Partial<InboxFilterState>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    updatePaginationCounts: (state, action: PayloadAction<Partial<InboxPaginationCountsState>>) => {
      state.paginationCounts = { ...state.paginationCounts, ...action.payload };
    },
    setIsThumbsLoading: (state, action: PayloadAction<boolean>) => {
      state.isThumbsLoading = action.payload;
    },
    setIsImageLoading: (state, action: PayloadAction<boolean>) => {
      state.isImageLoading = action.payload;
    },
    setThumbsLoadingByDoc: (state, action: PayloadAction<{ docId: string; isLoading: boolean }>) => {
      const { docId, isLoading } = action.payload;
      if (isLoading) {
        state.thumbsLoadingByDoc[docId] = true;
      } else {
        delete state.thumbsLoadingByDoc[docId];
      }
    },
    setImageLoadingByPage: (
      state,
      action: PayloadAction<{ docId: string; pageNo: number; isLoading: boolean }>,
    ) => {
      const { docId, pageNo, isLoading } = action.payload;
      const pageKey = `${docId}-${pageNo}`;
      if (isLoading) {
        state.imageLoadingByPage[pageKey] = true;
      } else {
        delete state.imageLoadingByPage[pageKey];
      }
    },
    setPageImagesItem: (
      state,
      action: PayloadAction<{ docId: string; pageNo: number; imageUrl?: string; thumbUrl?: string }>,
    ) => {
      try {
        const { docId, pageNo, imageUrl, thumbUrl } = action.payload;
        const clone = cloneDeep(state.pageImagesMap);
        // Check if imageUrl or thumbUrl are null to remove the entry
        if (imageUrl == null && thumbUrl == null) {
          delete clone[docId][pageNo];
          if (Object.keys(clone[docId]).length === 0) {
            delete clone[docId];
          }
        } else {
          if (!clone[docId]) {
            clone[docId] = {}; // Initialize docId if it doesn't exist
          }
          if (!clone[docId][pageNo]) {
            clone[docId][pageNo] = {}; // Initialize pageNo if it doesn't exist
          }
          // Update imageUrl and/or thumbUrl
          if (imageUrl) {
            clone[docId][pageNo] = {
              ...clone[docId][pageNo],
              imageUrl,
            };
          }
          if (thumbUrl) {
            clone[docId][pageNo] = {
              ...clone[docId][pageNo],
              thumbUrl,
            };
          }
        }

        state.pageImagesMap = clone;
      } catch (e) {
        console.log(e);
      }
    },

    // --- Reset Reducers ---
    resetFilters: (state, skipList?: PayloadAction<(keyof InboxFilterState)[]>) => {
      const resetKeys = skipList?.payload || [];
      const initialFilters = { ...initialState.filters };
      for (const key of resetKeys) {
        delete initialFilters[key];
      }
      state.filters = { ...state.filters, ...initialFilters };
    },
    resetPaginationFilters: (state) => {
      state.filters = { ...state.filters, ...initialPaginationFilters };
    },
  },
});

export const {
  updateFilters,
  updatePaginationCounts,
  resetPaginationFilters,
  resetFilters,
  setListLastDoc,
  setListFirstDoc,
  setThumbsLoadingByDoc,
  setImageLoadingByPage,
} = documentListSlice.actions;

export default documentListSlice.reducer;
