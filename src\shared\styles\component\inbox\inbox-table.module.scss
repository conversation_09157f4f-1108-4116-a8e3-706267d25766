@import "../../vars/_vars";


.container {
}


.table {
  font-family: $base-font;
  display: table;
  width: 100%;
  padding: 10px;
  table-layout: fixed;
}


.row {
  height: 56px;
  cursor: default;
  border-radius: 5px;
  box-shadow: none;

  &:nth-child(even) {
    background-color: $light-gray;
  }

}


.row__clickable {
  cursor: pointer;
}


.row__pending {
  user-select: none;
  opacity: 0.5;
  background: #FFFFFF;
}


.loading {
  height: 12px;
  border-radius: 5px;
  background: #EEEEEE;
}


.head {
  height: 42px;
  user-select: none;
  color: $dark-gray;
  background: $light-gray;

  & .cell {
    font-size: rem(12);
    min-width: 20px;
  }
}


.cell {
  font-size: rem(14);
  align-items: center;
  justify-content: center;
  vertical-align: middle;
}


.cell:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;

}


.cell:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}


.cell_name {
  width: 50%;
}


.row:hover:not(.row__pending), .row__active:not(.row__pending) {
  font-weight: 500;
  z-index: 10;
  color: $paperbox-blue;

}


.row:focus:not(.row__pending) {
  font-weight: 500;
  color: $paperbox-blue;
  outline: none;
}


.row__pending:focus {
  outline: none;
}


.row__pending:hover, .row__pending {
  .cell {
    font-weight: 400 !important;
    cursor: default;
    background: #FFFFFF;
    box-shadow: none !important;

  }
}


.row__locked:hover .cell {
  cursor: default;

}


.lock {
  width: 20px;
  height: auto;
  opacity: 0.5;
}


.bold {
  font-weight: 800;
}


.table_wrapper {
  overflow-y: auto;
  max-height: calc(100vh - 330px);
  margin-top: 24px;
  border: 1px solid $medium-gray;
  border-radius: 7px;
  background: white;
}


.table_bottom {
  font-family: $base-font;
  display: flex;
  align-items: center;
  margin-top: 16px;
  padding-bottom: 32px;
}


.pagination {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 40px;
  border: 1px solid $medium-gray;
  border-radius: 7px;

  * + * {
    border-left: 1px solid $medium-gray;
  }

  & > * {
    height: 100%;
  }

}


.pagination_icon {
  width: 32px;
  margin-top: 2px;
  cursor: default;

  &:disabled {
    svg {
      color: $medium-gray;
    }
  }

  &:hover:not([disabled]), &:focus:not([disabled]) {
    cursor: pointer;

    svg {
      color: $paperbox-blue
    }

  }

  &:active, &:focus {
    outline: none;
  }

  & + & {
    margin-left: 10px;
  }

  svg {
    width: 20px;
  }
}


.pagination_text {
  display: flex;
  align-items: center;
  padding: 16px;
}


.pagination_select {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  //width: 100px;
  height: 40px;
  //margin-right: 50px;

  margin-left: auto;
  white-space: nowrap;
  gap: 8px;
}


.processed {
  width: 20px;
  height: auto;
  color: $success;
}


.error {
  width: 20px;
  height: auto;
}


.empty {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: calc(50vh - 260px);

  svg {
    width: 32px;
    height: auto;
  }
}


.empty_head {
  font-family: $base-font;
  font-size: rem(24);
  font-weight: 500;
  margin-top: 14px;
  color: $medium-dark-gray;
}


.empty_text {
  font-family: $base-font;
  font-size: rem(16);
  font-weight: 300;
  line-height: 1.25;
  margin-top: 8px;
  text-align: center;
  color: $medium-dark-gray;
}


.confidence__low {
  color: $error
}


.confidence__high {
  color: $success
}


.label_wrapper {
  display: flex;
  justify-content: center;
  gap: 8px;
}


.label {
  font-size: 13px;
  display: flex;
  align-items: center;
  flex-grow: 0;
  justify-content: space-between;
  width: auto;
  padding: 4px 9px;
  color: white;
  border: 1px solid $medium-gray;
  border-radius: 5px;
}


.sort_head {
  @include flex-center;
  height: 16px;
}


.sort_head__start {
  justify-content: flex-start;
}


.sort_icon {
  width: 16px;
  height: auto;
  margin-left: 6px;
  vertical-align: center;
  color: $medium-dark-gray;
}


.name {
  display: inline-block;
  overflow: hidden;
  max-width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
}


.name_wrapper {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: flex-start;
}


.loader {
  flex-shrink: 0;
  margin-left: 8px;
}


.checks {
  margin-left: 8px;
  color: black;
  @include flex-center;
}


.info_icon {
  height: 14px;
  margin-top: -1px;
  transform: rotate(180deg);
  color: rgba(0, 0, 0, 0.23);
}


.copy_counter {
  @include flex-center;
  font-size: 12px;
  display: flex;
  align-items: center;
  flex-grow: 0;
  justify-content: space-between;
  width: auto;
  height: 20px;
  margin-left: 10px;
  padding: 5px 9px 4px 9px;
  transition: all 0.3s ease-in-out;
  color: $paperbox-blue;
  border-radius: 5px;
  background: $paperbox-blue--fade-extra;
}


.overdue_icon {
  flex-shrink: 0;
  width: 16px;
  height: auto;
  margin-left: 8px;
  margin-top: 2px;
  color: $error;
}


.notes_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}


.notes_pip {
  width: 6px;
  height: 6px;
  border-radius: 5px;
  background: $paperbox-blue;
}
