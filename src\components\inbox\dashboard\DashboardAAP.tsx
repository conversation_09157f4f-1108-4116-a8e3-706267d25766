import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import { demoDocTypesEN, demoDocTypesNL, getUserToken } from '@shared/helpers/helpers.ts';
import { getAllApprovalChecks } from '@shared/store/dashboardSlice.ts';
import { api } from '@shared/store/setup/firebase-setup.ts';
import { useDispatch, useSelector } from '@shared/store/store.ts';

import sc from '@shared/styles/component/inbox/inbox-content.module.scss';
import s from '@shared/styles/component/inbox/inbox-dashboard.module.scss';
import { ReactComponent as CheckmarkIcon } from '@svg/checkmark-alt-icon.svg';
import { ReactComponent as ChevronLeft } from '@svg/chevron-left.svg';
import { ReactComponent as EyeIcon } from '@svg/eye-open.svg';
import { ReactComponent as InfoIcon } from '@svg/info-icon.svg';
import clsx from 'clsx';
import { BarSeriesOption } from 'echarts';
import ReactEChartsCore from 'echarts-for-react/lib/core';
import { BarChart } from 'echarts/charts';
import { GridComponent, TitleComponent, TooltipComponent } from 'echarts/components';
import * as echarts from 'echarts/core';
import { CanvasRenderer, SVGRenderer } from 'echarts/renderers';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useResizeDetector } from 'react-resize-detector';
import { FlowComponentHandle } from './DashboardFlow.tsx';
import { createGradient } from './DashboardOutflow.tsx';
import { ColoredInbox } from './InboxDashboard.tsx';

interface Props {
  inboxes: ColoredInbox[];
  activeDateRange: [Date, Date];
}
type AAPData = {
  checks: string[];
  data: {
    keys: ('failed' | 'succeeded')[];
    count: number;
  }[];
  inboxKeys: {
    [key: string]: [docTypeName: string];
  };
};

const chartColors = [
  '#0085FF', // Original Blue
  '#005BB5', // Darker Blue
  '#00BFFF', // Distinct Tone: Deep Sky Blue

  '#FF5555', // Original Red
  '#CC0000', // Darker Red
  '#FF0000', // Distinct Tone: Bright Red

  '#FF8000', // Original Orange
  '#CC6600', // Darker Orange
  '#FFA500', // Distinct Tone: Goldish Orange

  '#7F19FC', // Original Purple
  '#4D00B2', // Darker Purple
  '#9932CC', // Distinct Tone: Dark Orchid

  '#0BC9CD', // Original Turquoise
  '#008080', // Darker Turquoise
  '#20B2AA', // Distinct Tone: Light Sea Green

  '#554971', // Original Grayish Purple
  '#2D1D3B', // Darker Grayish Purple
  '#8B008B', // Distinct Tone: Dark Magenta

  '#FFA07A', // New Tone 1: Light Salmon
  '#FF4500', // New Tone 1: Darker Orange-Red
  '#E9967A', // Distinct Tone: Dark Salmon

  '#6A5ACD', // New Tone 2: Slate Blue
  '#483D8B', // New Tone 2: Darker Slate Blue
  '#7B68EE', // Distinct Tone: Medium Slate Blue
];
let controller;
echarts.use([TitleComponent, TooltipComponent, GridComponent, BarChart, CanvasRenderer, SVGRenderer]);

const DashboardAAP = React.forwardRef<FlowComponentHandle, Props>(({ inboxes, activeDateRange }, ref) => {
  const demoMode = useSelector((state) => state.dashboard.demoMode);

  const [combinedNameMap] = useState([]);
  const [checkNameMap, setCheckNameMap] = useState<{ name: string; active: boolean; color: string }[]>([]);
  const [AAPData, setAAPData] = useState<AAPData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const [approvalCheckTypes, setApprovalCheckTypes] = useState([]);

  const { width: scw, ref: tagsRef } = useResizeDetector({
    refreshRate: 1,
    refreshMode: 'throttle',
  });

  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const handleScroll = (isNext: boolean) => {
    const tags = tagsRef?.current;
    if (!tags) return;

    const delta = (isNext ? 1 : -1) * (tags.clientWidth / 2);
    const newScroll = tags.scrollLeft + delta;

    setCanScrollLeft(newScroll > 0);
    setCanScrollRight(tags.scrollWidth - newScroll > tags.clientWidth);

    tags.scrollTo({ left: newScroll, behavior: 'smooth' }); // Smooth scrolling
  };

  useEffect(() => {
    const divElement = tagsRef.current as HTMLDivElement;
    if (divElement && scw) {
      setCanScrollLeft(divElement.scrollLeft > 0);
      setCanScrollRight(divElement.scrollWidth - divElement.scrollLeft > divElement.clientWidth);
    }
  }, [scw, tagsRef]);

  const [biggestTotal, setBiggestTotal] = useState(0);

  const fetchFunc = useCallback(async () => {
    const b = await getUserToken();
    if (!b) return;
    setBiggestTotal(0);
    if (controller) controller.abort();
    controller = new AbortController();
    setIsLoading(true);
    return api
      .get(`${import.meta.env.VITE_PAPERBOX_ANALYTICS_URL}/dashboard/aap`, {
        params: {
          startDate: activeDateRange[0].toISOString(),
          endDate: activeDateRange[1].toISOString(),
          inboxes: inboxes.length > 0 ? inboxes.map((e) => e.id) : null,
          demoMode: demoMode,
        },
        headers: {
          Authorization: `Bearer ${b}`,
        },
        signal: controller.signal,
      })
      .then((res) => {
        if (!res?.data) return null;

        const data = res.data
          .map((item) => {
            const total = item.data.reduce((sum, curr) => sum + curr.count, 0);
            setBiggestTotal((currentTotal) => Math.max(currentTotal, total));
            const data = item.data;
            return {
              ...item,
              data,
            };
          })
          .reverse();
        setAAPData(data);
        controller = undefined;
        setIsLoading(false);
        return res;
      });
  }, [demoMode, activeDateRange, inboxes]);

  React.useImperativeHandle(ref, () => ({
    fetchFunc,
  }));

  useEffect(() => {
    const approvalChecksFunc = async () => {
      const checks = await dispatch(getAllApprovalChecks());
      console.log(checks);
      setApprovalCheckTypes(checks);
    };

    fetchFunc();
    approvalChecksFunc();
  }, [dispatch, fetchFunc]);

  const checkNamesMapper = (stringToMap: string) => {
    const translationString = t(`home:dashboard.aapCheckNames.${stringToMap}`, { defaultValue: null });
    if (translationString.includes('.')) {
      const checkMapItem = approvalCheckTypes.find((e) => e.type === stringToMap);
      if (checkMapItem) return checkMapItem.name;
      return stringToMap.replace('_', ' ');
    }

    return translationString;
  };

  const segmentDataArray = useMemo(() => {
    if (AAPData) {
      let checkNames: any[] = [];
      const mapped = AAPData.map((item) => {
        item.checks.forEach((check) => {
          if (!checkNames.find((item) => item.name === check)) {
            if (check === 'automated') {
              checkNames.push({ name: check, active: false });
            } else {
              checkNames.push({ name: check, active: true });
            }
          }
        });

        return item.data.map((dataItem) => {
          let failedChecks = '';
          if (dataItem.keys.every((key) => key === 'succeeded')) {
            failedChecks = 'automated';
            if (!checkNames.some((e) => e.name === 'automated')) {
              checkNames.push({ name: 'automated', active: false });
            }
          } else {
            failedChecks = dataItem.keys
              .map((key, index) => {
                return key === 'failed' ? item.checks[index] : null;
              })
              .filter(Boolean)
              .join(' + ');
          }

          if (!combinedNameMap.includes(failedChecks)) {
            combinedNameMap.push(failedChecks);
          }

          return {
            name: failedChecks,
            value: dataItem.count,
          };
        });
      });
      checkNames = checkNames.map((checkName, i) => ({
        ...checkName,
        color: chartColors[i % (chartColors.length - 1)],
      }));
      setCheckNameMap(checkNames);
      return mapped;
    }
    return [];
  }, [AAPData, combinedNameMap]);

  const series = useMemo(() => {
    if (segmentDataArray) {
      const test = segmentDataArray.map((data) => {
        return data
          .filter((item) => {
            const automatedCheck = checkNameMap.find((e) => e.name === 'automated');
            if (item.name === 'automated' && automatedCheck?.active !== true) {
              return false;
            }
            return true;
          })
          .reduce((sum, curr) => sum + curr.value, 0);
      });
      setBiggestTotal(Math.max(...test));
      const filteredGroups = segmentDataArray.map(() => new Array(combinedNameMap.length).fill({ value: 0 }));
      const groups = segmentDataArray.map(() => new Array(combinedNameMap.length).fill({ value: 0 }));
      const returnItem = combinedNameMap.map((checkName, checkIndex) => {
        const pass = checkNameMap.some((nameItem) => checkName.includes(nameItem.name) && nameItem.active);
        const data = segmentDataArray.map((segmentData) => {
          const item = segmentData.find((segment) => segment.name === checkName);
          if (item) {
            const isTopBar = segmentData[segmentData.length - 1].name === checkName;
            return {
              value: item.value,
              trueValue: item.name,
              itemStyle: {
                color:
                  item.name === 'automated'
                    ? createGradient('#76c865')
                    : createGradient(chartColors[checkIndex % (chartColors.length - 1)]),
                borderRadius: isTopBar ? [0, 5, 5, 0] : [0, 0, 0, 0],
                borderWidth: 0.5,
                borderType: 'solid',
                borderColor: '#ffffff',
              },
            };
          }
          return { value: 0 };
        });

        data.forEach((dataItem, i) => {
          groups[i][checkIndex] = {
            value: Number.parseFloat(dataItem.value.toString()) || 0,
            name: dataItem.trueValue,
          };
          if (pass) {
            filteredGroups[i][checkIndex] = {
              value: Number.parseFloat(dataItem.value.toString()) || 0,
              name: dataItem.trueValue,
            };
          }
        });

        return {
          data: pass ? data : [],
          name: checkName,
          type: 'bar',
          stack: 'x',
          barMaxWidth: 100,
          seriesLayoutBy: 'column',

          label: {
            show: true,
            position: 'right',
            fontWeight: 600,
            fontFamily: 'Yantramanav',
            margin: 5,
            fontSize: 12,
            opacity: 0.7,
            formatter: (params) => {
              const currentFilteredGroup = filteredGroups[params.dataIndex];
              if (!currentFilteredGroup) return '';
              const currentGroup = groups[params.dataIndex];
              const reversedIndex = currentFilteredGroup
                .slice()
                .reverse()
                .findIndex((item) => item?.value > 0);
              const currentLastIndex =
                reversedIndex === -1 ? -1 : currentFilteredGroup.length - 1 - reversedIndex;

              if (params.componentIndex === currentLastIndex) {
                const automatedCheck = checkNameMap.find((e) => e.name === 'automated');
                const filteredGroupTotal = currentFilteredGroup.reduce((sum, item) => sum + item.value, 0);
                const groupTotal = currentGroup.reduce((sum, item) => {
                  if (item.name === 'automated' && automatedCheck?.active !== true) {
                    return sum;
                  }

                  return sum + item.value;
                }, 0);
                if (filteredGroupTotal === 0) return '';
                return `${((filteredGroupTotal / groupTotal) * 100).toFixed(1)}%`;
              }
              return '';
            },
          },
        } as BarSeriesOption;
      });
      return returnItem;
    }
  }, [segmentDataArray, combinedNameMap, checkNameMap]);

  const tags = useMemo(() => {
    return checkNameMap.filter((t) => t.name !== 'automated');
  }, [checkNameMap]);

  const option = useMemo(() => {
    return {
      animation: true,
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        textStyle: {
          fontSize: 14,
          fontfamily: 'Yantramanav',
        },
        formatter: (params) => {
          const sortedParams = params.sort(
            (b, a) => Number.parseFloat(a.data?.value ?? 0) - Number.parseFloat(b.data?.value ?? 0),
          );
          const total = sortedParams.reduce((sum, p) => sum + Number.parseFloat(p.data?.value ?? 0), 0);

          let othersValue = 0;
          let othersPercentage = 0;

          const formatted = sortedParams
            .filter((p) => p.value !== 0)
            .map((p) => ({
              color: p.data.itemStyle.color.colorStops[0].color,
              seriesName: p.seriesName,
              value: p.value,
              percentage: (p.data.value / total) * 100,
            }))
            .filter((p) => {
              if (p.percentage < 1) {
                othersValue += p.value;
                othersPercentage += p.percentage;
                return false;
              }
              return true;
            });

          if (othersPercentage > 0) {
            formatted.push({
              color: 'gray',
              seriesName: 'Others',
              value: othersValue,
              percentage: othersPercentage,
            });
          }

          return formatted
            .map((p) => {
              const splitNames = p.seriesName.split(' + ');
              const formattedNames = splitNames
                .map((n) => {
                  const isActive = checkNameMap.find((item) => item.name === n.replace(' ', '_'))?.active;
                  if (isActive) {
                    return `<span style="font-weight: 500;margin-left:auto;">${checkNamesMapper(n)}</span>`;
                  }
                  return `<span style="font-weight: 400;margin-left:auto;opacity:0.4">${n}</span>`;
                })
                .join(' + ');
              return `
        <div style="display: flex; justify-content: stretch; align-items: center; gap: 10px">
          <span style="display: block; margin-right: 5px; border-radius: 10px; width: 10px; height: 10px; background-color: ${
            p.color
          };"></span>
          <span style="text-transform: capitalize; margin-left: 5px; margin-right: auto">${formattedNames}</span>
          <b style="font-weight: 700; margin-left: auto;">${p.value} (${p.percentage.toFixed(1)}%)</b>
        </div>`;
            })
            .join('');
        },
      },
      xAxis: {
        min: 0,
        max: biggestTotal,
      },
      yAxis: {
        axisTick: {
          show: false,
        },
        data: segmentDataArray.map(() => ''),
      },
      series: series,
    };
  }, [biggestTotal, segmentDataArray, checkNameMap]);

  return (
    <div className={sc.container}>
      <div className={sc.sub_header}>
        <div className={sc.sub_info}>
          <h2 className={sc.sub_title}> {t('home:dashboard.bottlenecksTitle')}</h2>
          <Tooltip
            position={'right'}
            alignment="start"
            content={
              <>
                <b
                  style={{
                    fontWeight: 500,
                    fontSize: 15,
                    marginBottom: 10,
                    display: 'block',
                  }}
                >
                  {t('home:dashboard.bottlenecksTitle')}
                </b>
                <p style={{ maxWidth: 350 }}>{t('home:dashboard.bottlenecksDescription')}</p>
              </>
            }
          >
            <div>
              <InfoIcon />
            </div>
          </Tooltip>
        </div>

        <div style={{ width: 'auto', minWidth: '8vw' }} />
        <div className={sc.tags} style={{ flexShrink: 0, marginRight: 0, marginLeft: 'auto' }}>
          {checkNameMap.find((e) => e.name === 'automated') && (
            <Tooltip content={t('home:dashboard.bottlenecksAutomated')} position={'bottom'}>
              <button
                style={{ padding: 0, width: 46 }}
                className={clsx(sc.tag, {
                  [sc.tag__active]: checkNameMap.find((mi) => mi.name === 'automated')?.active,
                })}
                onClick={() =>
                  setCheckNameMap(
                    checkNameMap.map((e) => {
                      return {
                        ...e,
                        active: e.name === 'automated' ? !e.active : e.active,
                      };
                    }),
                  )
                }
              >
                <CheckmarkIcon style={{ width: 16, height: 'auto' }} />
              </button>
            </Tooltip>
          )}
          <Tooltip content={t('home:dashboard.bottlenecksHide')} position={'bottom'}>
            <button
              className={clsx(sc.tag, {
                [sc.tag__active]: checkNameMap.filter((e) => e.name !== 'automated').every((e) => e.active),
              })}
              style={{ padding: 0, width: 46 }}
              onClick={() =>
                setCheckNameMap(
                  checkNameMap.map((e) => {
                    return {
                      ...e,
                      active: !checkNameMap.filter((e) => e.name !== 'automated').every((e) => e.active),
                    };
                  }),
                )
              }
            >
              <EyeIcon style={{ width: 16, height: 'auto' }} />
            </button>
          </Tooltip>
        </div>

        <div className={clsx(sc.tags_wrapper, sc.tags_wrapper__right)} style={{ marginLeft: 0 }}>
          <div ref={tagsRef} className={sc.tags}>
            <>
              {canScrollLeft && (
                <button className={sc.button_float} onClick={() => handleScroll(false)}>
                  <ChevronLeft />
                </button>
              )}
              {canScrollRight && (
                <button
                  className={clsx(sc.button_float, sc.button_float__right)}
                  onClick={() => handleScroll(true)}
                >
                  <ChevronLeft />
                </button>
              )}
              {tags.map((mi) => (
                <button
                  key={mi.name}
                  className={clsx(sc.tag, { [sc.tag__active]: mi.active })}
                  style={{ textTransform: 'capitalize' }}
                  onClick={() =>
                    setCheckNameMap(
                      checkNameMap.map((e) => {
                        return {
                          ...e,
                          active: e.name === mi.name ? !e.active : e.active,
                        };
                      }),
                    )
                  }
                >
                  {checkNamesMapper(mi.name)}
                </button>
              ))}
            </>
          </div>
        </div>
      </div>
      <div className={s.chart_wrapper}>
        {AAPData && AAPData.length > 0 ? (
          <>
            <div className={s.chart_axis}>
              {[...AAPData].reverse().map((e, i) => (
                <Tooltip
                  key={e.checks.join('-')}
                  content={
                    <div className={s.chart_inbox_tooltip}>
                      <b>{t('home:dashboard.bottlenecksTooltipTitle')}</b>
                      <div className={s.tooltip_inboxes}>
                        {e?.inboxKeys &&
                          Object.entries(e?.inboxKeys).map(([key, v], i) => {
                            const inboxName = inboxes.find((e) => e.id === key)?.settings.name;

                            return (
                              <div className={s.tooltip_inbox} key={key}>
                                <span>{inboxName ?? key}</span>
                                <div className={s.tooltip_doctypes}>
                                  {v.slice(0, 5).map((v, vi) => {
                                    let name = v;
                                    if (demoMode) {
                                      const mapping =
                                        i18n.language === 'en' ? demoDocTypesEN : demoDocTypesNL;
                                      name = mapping[((i + 1) * (vi + 1)) % 20];
                                    }
                                    return <span key={v}>{name}</span>;
                                  })}
                                  {v.length > 5 && (
                                    <span style={{ fontWeight: 400, fontSize: 12 }}>
                                      {' '}
                                      + {v.length - 5} {t('home:dashboard.bottlenecksMore')}
                                    </span>
                                  )}
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    </div>
                  }
                >
                  <span>Groep {i + 1}</span>
                </Tooltip>
              ))}
            </div>
            <ReactEChartsCore
              echarts={echarts}
              showLoading={isLoading}
              notMerge={true}
              shouldSetOption={(prevProps, props) => {
                return prevProps.option !== props.option;
              }}
              style={{ width: '100%', height: '100%' }}
              option={option}
            />
          </>
        ) : (
          <div className={s.no_data}>{t('home:dashboard.noData')}</div>
        )}
      </div>
    </div>
  );
});

export default DashboardAAP;
