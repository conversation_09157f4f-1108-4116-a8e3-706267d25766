import FormBodyHeader from '@components/admin/components/form/FormBodyHeader';
import FormInputField from '@components/admin/components/form/FormInputField';
import FormSection from '@components/admin/components/form/FormSection';
import { DropdownOption } from '@components/shared/dropdown/StyledSelect';
import apiClient from '@shared/helpers/apiClient';
import { getInboxes, postInbox } from '@shared/store/adminSlice';
import { clientConnectorsSelector } from '@shared/store/adminSlice';
import { useDispatch, useSelector } from '@shared/store/store';
import s from '@shared/styles/component/admin/admin-section.module.scss';
import { createTemplateType } from '@src/components/tenant/onboarding-types';
import { cloneDeep } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import AdminInboxSettingsForm from '../shared/AdminInboxSettingsForm';

import {
  IClientInboxCreation,
  defaultInboxCreation,
  inboxCreationToPayload,
} from '@shared/helpers/converters/inboxCreation';

const AdminInboxCreation: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const connectors = useSelector(clientConnectorsSelector);
  const [templates, setTemplates] = useState<any>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Initialize with the default inbox creation state
  const [state, setState] = useState<IClientInboxCreation>(cloneDeep(defaultInboxCreation));

  const [systemOptions, setSystemOptions] = useState<DropdownOption[]>([]);
  const [inboxTypeOptions, setInboxTypeOptions] = useState<DropdownOption[]>([]);
  const [languageOptions, setLanguageOptions] = useState<DropdownOption[]>([]);

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await apiClient.get(
          `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/templates/inboxes`,
        );
        setTemplates(response.data);
        console.log(response.data);

        const systems: DropdownOption[] = [
          {
            label: t('admin:inboxes.templates.custom'),
            value: 'custom',
            color: '#0085FF',
          },
        ];

        Object.keys(response.data).forEach((system) => {
          let connectorType = system;
          let color = '#0085FF';

          if (system === 'brio') {
            connectorType = 'brio';
            color = '#91C500';
          } else if (system === 'ccs') {
            color = '#7B61FF';
          }

          systems.push({
            label: system.charAt(0).toUpperCase() + system.slice(1),
            value: system,
            color,
            tag: {
              name: system.toUpperCase(),
              value: connectorType,
            },
          });
        });

        setSystemOptions(systems);
      } catch (error) {
        console.error('Failed to fetch templates:', error);
        setErrorMessage('Failed to fetch templates');
      }
    };

    fetchTemplates();
  }, []);

  // Update inbox type options when system changes
  useEffect(() => {
    if (state.template.system && state.template.system !== 'custom' && templates[state.template.system]) {
      const options: DropdownOption[] = Object.keys(templates[state.template.system]).map((type) => ({
        label: type.charAt(0).toUpperCase() + type.slice(1), // Capitalize first letter
        value: type,
        description: templates[state.template.system][type].description || `${type} documents`,
      }));

      setInboxTypeOptions(options);

      // Only set default inbox type if none is selected yet
      if (options.length > 0 && !state.template.inboxType) {
        setState((prev: IClientInboxCreation) => ({
          ...prev,
          template: {
            ...prev.template,
            inboxType: options[0].value,
          },
        }));
      }
    } else {
      setInboxTypeOptions([]);
    }
  }, [state.template.system, templates, state.template.inboxType]);

  useEffect(() => {
    if (
      state.template.system &&
      state.template.system !== 'custom' &&
      state.template.inboxType &&
      templates[state.template.system] &&
      templates[state.template.system][state.template.inboxType]
    ) {
      const languages = templates[state.template.system][state.template.inboxType].languages;

      const options: DropdownOption[] = languages.map((lang: string) => ({
        label: lang.toUpperCase(),
        value: lang,
        description: `${lang.toUpperCase()} language`,
      }));

      setLanguageOptions(options);

      if (options.length > 0 && !state.template.language) {
        setState((prev: IClientInboxCreation) => ({
          ...prev,
          template: {
            ...prev.template,
            language: options[0].value,
          },
        }));
      }
    } else {
      setLanguageOptions([]);
    }
  }, [state.template.system, state.template.inboxType, templates, state.template.language]);

  useEffect(() => {
    if (state.template.system && state.template.inboxType && state.template.language) {
      // Only update if templateType doesn't match current selections
      const expectedTemplateType = createTemplateType(
        state.template.system,
        state.template.inboxType,
        state.template.language,
      );

      if (state.template.templateType !== expectedTemplateType) {
        let connectorType = state.template.system;
        if (state.template.system === 'portimabrio') {
          connectorType = 'brio';
        }

        setState((prev: IClientInboxCreation) => ({
          ...prev,
          template: {
            ...prev.template,
            templateType: expectedTemplateType,
          },
          connector: {
            ...prev.connector,
            connectorType: state.template.system === 'custom' ? '' : connectorType,
          },
        }));
      }
    }
  }, [state.template.system, state.template.inboxType, state.template.language, state.template.templateType]);

  const handleInboxInput = useCallback((value: any, path: string) => {
    setState((prevState: IClientInboxCreation) => {
      const newState = cloneDeep(prevState);
      const pathParts = path.split('.');
      let current = newState;

      // Special handling for autoDelete
      if (pathParts[1] === 'autoDelete' && !current.settings.autoDelete) {
        current.settings.autoDelete = {
          active: false,
          actionType: 'none',
          timeField: 7,
          timeFormat: 'day',
        };
      }

      // Ensure all nested objects exist
      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i];
        if (!current[part]) current[part] = {};

        current = current[part];
      }
      current[pathParts[pathParts.length - 1]] = value;
      return newState;
    });
  }, []);

  const handleConnectorInput = useCallback((value: any, path: string) => {
    setState((prevState: IClientInboxCreation) => {
      const newState = cloneDeep(prevState);
      const pathParts = path.split('.');
      let current = newState.connector.connectorSettings;

      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i];
        if (!current[part]) current[part] = {};

        current = current[part];
      }

      current[pathParts[pathParts.length - 1]] = value;
      return newState;
    });
  }, []);
  const handleEndpointInput = useCallback((value: any, path: string) => {
    setState((prevState: IClientInboxCreation) => {
      const newState = cloneDeep(prevState);
      const pathParts = path.split('.');
      let current = newState.endpoint;

      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i];
        if (!current[part]) current[part] = {};

        current = current[part];
      }

      current[pathParts[pathParts.length - 1]] = value;
      return newState;
    });
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!state.settings.name) {
        setErrorMessage('Inbox name is required');
        return;
      }

      setIsSubmitting(true);
      setErrorMessage(null);

      try {
        // Convert the inbox creation state to the API payload
        const payload = inboxCreationToPayload(state);
        let newInboxId;
        // If a template was selected, use the import endpoint
        if (state.template.templateType && state.template.templateType !== 'custom') {
          const res = await apiClient.post(
            `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/import?inbox_template_type=${state.template.templateType}`,
            payload,
          );
          newInboxId = res.data.inbox_id;
        } else {
          // Otherwise use the regular inbox creation endpoint
          const res = await postInbox(payload.settings);
          newInboxId = res.data.id;
        }

        // Refresh the inboxes list and navigate to the newly created inbox
        await dispatch(getInboxes()).then(() => {
          navigate(`/admin/inboxes/${newInboxId}`);
        });
      } catch (error) {
        console.error('Failed to create inbox:', error);
        setErrorMessage(t('admin:errors.inboxCreate'));
      } finally {
        setIsSubmitting(false);
      }
    },
    [state, dispatch, navigate],
  );

  const filteredConnectors = useMemo(() => {
    return connectors.filter((c: any) => {
      if (state.connector.connectorType === 'brio') {
        return c.type === 'portimabrio';
      }
      if (state.connector.connectorType === 'ccs') {
        return c.type === 'ccs';
      }
      return true;
    });
  }, [connectors, state.connector.connectorType]);

  const filteredConnectorsOptions = useMemo(() => {
    return filteredConnectors.map((c: any) => ({
      value: c.id,
      label: c.name || c.id,
      description: c.type || '',
      tag: {
        name:
          c.type === 'portimabrio' ? 'Brio' : c.type === 'ccs' ? 'CCS' : c.type?.toUpperCase() || 'Connector',
        value: c.type || 'connector',
      },
      color:
        c.type === 'portimabrio' || c.type === 'brio' ? '#91C500' : c.type === 'ccs' ? '#7B61FF' : '#0085FF',
    }));
  }, [filteredConnectors]);

  return (
    <form onSubmit={handleSubmit} className={s.form_body}>
      <FormBodyHeader
        title={t('admin:inboxes.createInbox')}
        saving={isSubmitting}
        errorMessage={errorMessage}
        buttonText={t('admin:inboxes.createInbox')}
        hasChanges={true}
      />

      {/* Render the form sections */}
      <div className={s.sections}>
        {/* Template Selection Section */}
        <FormSection title={t('admin:inboxes.templateSelection')} disabled={systemOptions.length === 0}>
          <FormInputField
            label={t('admin:inboxes.system')}
            description={t('admin:inboxes.systemDescription')}
            type="dropdown"
            value={systemOptions.find((option) => option.value === state.template.system)}
            dropdownOptions={systemOptions}
            onChange={(option) =>
              setState((prev: IClientInboxCreation) => ({
                ...prev,
                template: {
                  ...prev.template,
                  system: option.value,
                  inboxType: '',
                  language: '',
                  templateType: option.value === 'custom' ? 'custom' : '',
                },
                connector: {
                  ...prev.connector,
                  connectorType: option.value === 'custom' ? '' : option.tag?.value || option.value,
                },
              }))
            }
          />

          {state.template.system && state.template.system !== 'custom' && (
            <FormInputField
              label={t('admin:inboxes.inboxType')}
              description={t('admin:inboxes.inboxTypeDescription')}
              type="dropdown"
              value={inboxTypeOptions.find((option) => option.value === state.template.inboxType)}
              dropdownOptions={inboxTypeOptions}
              onChange={(option) =>
                setState((prev: IClientInboxCreation) => ({
                  ...prev,
                  template: {
                    ...prev.template,
                    inboxType: option.value,
                    language: '',
                  },
                }))
              }
            />
          )}

          {state.template.system && state.template.system !== 'custom' && state.template.inboxType && (
            <FormInputField
              label={t('admin:inboxes.language')}
              description={t('admin:inboxes.languageDescription')}
              type="dropdown"
              value={languageOptions.find((option) => option.value === state.template.language)}
              dropdownOptions={languageOptions}
              onChange={(option) =>
                setState((prev: IClientInboxCreation) => ({
                  ...prev,
                  template: {
                    ...prev.template,
                    language: option.value,
                  },
                }))
              }
            />
          )}
        </FormSection>
        {state.template.system && state.template.system !== 'custom' && (
          <FormSection title={'Inbox Settings'}>
            <FormInputField
              testId="inbox-name-input"
              required
              value={state?.settings?.name}
              type="text"
              label={t('admin:inboxes.name')}
              description={t('admin:inboxes.nameDescription')}
              onChange={(val) => handleInboxInput(val, 'settings.name')}
            />
          </FormSection>
        )}
        {/* Connector Configuration Section - only shown for non-custom templates */}
        {state.template.templateType &&
          state.template.templateType !== 'custom' &&
          state.connector.connectorType && (
            <FormSection title="Connector Configuration">
              <FormInputField
                type="toggle"
                label={t('admin:inboxes.useExistingConnector')}
                description={t('admin:inboxes.useExistingConnectorDescription')}
                value={state.connector.useExistingConnector}
                onChange={(val) => {
                  setState((prev: IClientInboxCreation) => {
                    const newState = cloneDeep(prev);
                    newState.connector.useExistingConnector = val;

                    if (val && !newState.connector.connectorId) {
                      if (filteredConnectors.length > 0) {
                        newState.connector.connectorId = filteredConnectors[0].id;
                      }
                    }
                    return newState;
                  });
                }}
              />

              {state.connector.useExistingConnector ? (
                <>
                  <FormInputField
                    label={t('admin:inboxes.selectConnector')}
                    description={t('admin:inboxes.selectConnectorDescription')}
                    type="dropdown"
                    value={
                      state.connector.connectorId
                        ? filteredConnectorsOptions.find((c: any) => c.value === state.connector.connectorId)
                        : null
                    }
                    dropdownOptions={filteredConnectorsOptions}
                    onChange={(option) =>
                      setState((prev: IClientInboxCreation) => ({
                        ...prev,
                        connector: {
                          ...prev.connector,
                          connectorId: option.value,
                        },
                      }))
                    }
                  />
                </>
              ) : (
                <>
                  <FormInputField
                    required
                    type="text"
                    label={t('admin:connectors.name')}
                    description={t('admin:connectors.nameDescription')}
                    value={state.connector.connectorSettings.name}
                    onChange={(val) => handleConnectorInput(val, 'name')}
                  />

                  {state.connector.connectorType === 'brio' && (
                    <>
                      <FormInputField
                        required
                        type="dropdown"
                        label={t('admin:connectors.environment')}
                        description={t('admin:connectors.environmentDescription')}
                        value={{
                          value: state.connector.connectorSettings.environment,
                          label:
                            state.connector.connectorSettings.environment.charAt(0).toUpperCase() +
                            state.connector.connectorSettings.environment.slice(1),
                        }}
                        dropdownOptions={[
                          { value: 'production', label: 'Production' },
                          { value: 'acceptance', label: 'Staging' },
                        ]}
                        onChange={(option) => handleConnectorInput(option.value, 'environment')}
                      />
                      <FormInputField
                        required
                        type="text"
                        label={t('admin:connectors.officeId')}
                        description={t('admin:connectors.officeIdDescription')}
                        value={state.connector.connectorSettings.office_id}
                        onChange={(val) => handleConnectorInput(val, 'office_id')}
                      />
                      <FormInputField
                        required
                        type="text"
                        label={t('admin:connectors.subOfficeId')}
                        description={t('admin:connectors.subOfficeIdDescription')}
                        value={state.connector.connectorSettings.sub_office_id}
                        onChange={(val) => handleConnectorInput(val, 'sub_office_id')}
                      />
                      <FormInputField
                        required
                        type="text"
                        label={t('admin:connectors.pi2Key')}
                        description={t('admin:connectors.pi2KeyDescription')}
                        value={state.connector.connectorSettings.pi2_key}
                        onChange={(val) => handleConnectorInput(val, 'pi2_key')}
                      />
                    </>
                  )}

                  {/* CCS Connector Fields */}
                  {state.connector.connectorType === 'ccs' && (
                    <>
                      <FormInputField
                        required
                        type="url"
                        label={t('admin:connectors.ccs.url')}
                        description={t('admin:connectors.ccs.urlDescription')}
                        value={state.connector.connectorSettings.url}
                        onChange={(val) => handleConnectorInput(val, 'url')}
                      />
                      <FormInputField
                        required
                        type="text"
                        label={t('admin:connectors.ccs.account')}
                        description={t('admin:connectors.ccs.accountDescription')}
                        value={state.connector.connectorSettings.account}
                        onChange={(val) => handleConnectorInput(val, 'account')}
                      />
                      <FormInputField
                        required
                        type="text"
                        label={t('admin:connectors.ccs.username')}
                        description={t('admin:connectors.ccs.usernameDescription')}
                        value={state.connector.connectorSettings.username}
                        onChange={(val) => handleConnectorInput(val, 'username')}
                      />
                      <FormInputField
                        required
                        type="secret"
                        label={t('admin:connectors.ccs.password')}
                        description={t('admin:connectors.ccs.passwordDescription')}
                        value={state.connector.connectorSettings.password}
                        onChange={(val) => handleConnectorInput(val, 'password')}
                      />
                    </>
                  )}
                </>
              )}
              {state.template.system === 'brio' && (
                <FormInputField
                  required
                  type="text"
                  label={t('admin:webhooks.brio.activitySettings.rds')}
                  description={t('admin:webhooks.brio.activitySettings.rdsDescription')}
                  value={state.endpoint.rds}
                  onChange={(val) => handleEndpointInput(val, 'rds')}
                />
              )}
            </FormSection>
          )}

        {state.template.templateType && state.template.templateType === 'custom' && (
          <AdminInboxSettingsForm
            inboxState={state}
            handleInput={handleInboxInput}
            isCreation={true}
            setState={setState}
          />
        )}
      </div>
    </form>
  );
};

export default AdminInboxCreation;
