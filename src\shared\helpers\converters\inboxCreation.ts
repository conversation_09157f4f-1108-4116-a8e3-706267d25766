import { IClientInbox, inboxClientToRaw } from './inbox';

// Connector settings for different connector types
export interface IConnectorSettings {
  // Common fields
  name: string;

  // Brio fields
  office_id?: string;
  sub_office_id?: string;
  pi2_key?: string;
  environment?: 'production' | 'acceptance' | 'test';

  // CCS fields
  url?: string;
  username?: string;
  password?: string;
  account?: string;
}

// Template selection information
export interface ITemplateSelection {
  templateType: string;
  system: string;
  inboxType: string;
  language: string;
}

// Connector selection information
export interface IConnectorSelection {
  connectorType: string;
  connectorId?: string;
  useExistingConnector: boolean;
  connectorSettings: IConnectorSettings;
}
export interface IEndpointSelection {
  rds?: string;
}

// Extended IClientInbox for creation with template and connector information
export interface IClientInboxCreation extends IClientInbox {
  template: ITemplateSelection;
  connector?: IConnectorSelection;
  endpoint?: IEndpointSelection;
}

// Create a default inbox creation object
export const defaultInboxCreation: IClientInboxCreation = {
  id: null,
  config: {
    workflowVersion: '',
    anonymization: false,
  },
  settings: {
    name: '',
    tableColumns: {
      name: true,
      confidence: false,
      digitizedDate: true,
      docTypeId: true,
      lastUserUpdate: true,
      tagTypeId: false,
    },
    autoDelete: {
      actionType: 'none',
      active: false,
      timeField: 7,
      timeFormat: 'day',
    },
    autoAdvance: true,
    fileUpload: true,
    documentDownload: true,
    documentCopy: true,
    documentTransform: true,
    labelingMode: false,
    mailroom: true,
    inboxMoveWhitelist: [],
    inboxMoveWhitelistActive: false,
  },
  template: {
    templateType: '',
    system: '',
    inboxType: '',
    language: '',
  },
  endpoint: {
    rds: '',
  },
  connector: {
    connectorType: '',
    connectorId: '',
    useExistingConnector: false,
    connectorSettings: {
      name: '',
      office_id: '',
      sub_office_id: '',
      pi2_key: '',
      environment: 'production',
      url: '',
      username: '',
      password: '',
      account: '',
    },
  },
};

// Function to convert IClientInboxCreation to payload for API
export const inboxCreationToPayload = (inboxCreation: IClientInboxCreation): any => {
  // Convert inbox settings using the existing converter
  const inboxPayload = inboxClientToRaw(inboxCreation);
  delete inboxPayload.id;

  // If no template is selected, just return the inbox settings
  if (!inboxCreation.template.templateType || inboxCreation.template.templateType === 'custom') {
    return { settings: inboxPayload };
  }

  // Otherwise, prepare the connector payload
  let connectorPayload = {};

  // If using an existing connector
  if (inboxCreation.connector.useExistingConnector && inboxCreation.connector.connectorId) {
    connectorPayload = {
      [inboxCreation.connector.connectorId]: {},
    };
  }
  // Otherwise create a new connector
  else if (inboxCreation.connector.connectorType) {
    if (inboxCreation.connector.connectorType === 'brio') {
      connectorPayload = {
        _CONNECTOR_ID_: {
          type: 'portimabrio',
          environment: inboxCreation.connector.connectorSettings.environment,
          office_id: inboxCreation.connector.connectorSettings.office_id,
          sub_office_id: inboxCreation.connector.connectorSettings.sub_office_id,
          pi2_key: inboxCreation.connector.connectorSettings.pi2_key,
          name:
            inboxCreation.connector.connectorSettings.name || `${inboxCreation.settings.name} Brio Connector`,
        },
      };
    } else if (inboxCreation.connector.connectorType === 'ccs') {
      connectorPayload = {
        _CONNECTOR_ID_: {
          type: 'ccs',
          url: inboxCreation.connector.connectorSettings.url,
          account: inboxCreation.connector.connectorSettings.account,
          username: inboxCreation.connector.connectorSettings.username,
          password: inboxCreation.connector.connectorSettings.password
            ? { '.psv': '@PB_SECRET', secret: inboxCreation.connector.connectorSettings.password }
            : undefined,
          name:
            inboxCreation.connector.connectorSettings.name || `${inboxCreation.settings.name} CCS Connector`,
        },
      };
    }
  }
  let endpointPayload;
  if (inboxCreation.endpoint.rds)
    endpointPayload = {
      _ENDPOINT_ID_: {
        inbox_mapping: {
          _INBOX_ID_: {
            activity_settings: {
              recipient_default_service: inboxCreation.endpoint.rds,
            },
          },
        },
      },
    };

  // Return the combined payload
  return {
    settings: {
      settings: {
        name: inboxPayload.settings.name,
      },
    },
    connectors: connectorPayload,
    endpoints: endpointPayload,
  };
};
