import { updateFilters, updatePaginationCounts } from '@shared/store/documentListSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store.ts';
import { useEffect } from 'react';

const usePagination = () => {
  const dispatch = useDispatch();
  const { pageSize, currentPageIndex } = useSelector((state) => state.documentList.filters);
  const { filteredDocCount, pageCount } = useSelector((state) => state.documentList.paginationCounts);

  const handleNextPage = (e) => {
    e.preventDefault();
    dispatch(updateFilters({ currentPageIndex: currentPageIndex + 1, navDirection: 'forward' }));
  };

  // Sets page count based on docCount and pageSize
  useEffect(() => {
    if (filteredDocCount !== null && pageSize !== null) {
      let pageCount = Math.ceil(filteredDocCount / pageSize);
      if (pageCount === 0) pageCount = 1;
      dispatch(updatePaginationCounts({ pageCount }));
    }
  }, [filteredDocCount, pageSize]);

  const handlePrevPage = (e) => {
    e.preventDefault();
    dispatch(updateFilters({ currentPageIndex: currentPageIndex - 1, navDirection: 'back' }));
  };

  return {
    handleNextPage,
    handlePrevPage,
    currentPageIndex,
    pageCount,
    docCount: filteredDocCount,
    pageSize,
  };
};

export default usePagination;
