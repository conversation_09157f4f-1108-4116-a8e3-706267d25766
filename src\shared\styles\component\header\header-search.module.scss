@import "../../vars/_vars";


.results {
  font-family: $base-font;
  position: absolute;
  z-index: 9;
  top: 34px;
  left: 2px;
  overflow-y: auto;
  width: 396px;
  max-height: 160px;
  border-radius: 0 0 10px 10px;
  box-shadow: 0 1px 5px rgba(0, 13, 33, 0.20);
}


.result {
  display: flex;
  align-items: center;
  height: 25px;
  padding: 20px;
  background-color: white;


  &:hover {
    cursor: pointer;
    background: #F0F3F8;
  }
}


.icon {
  width: auto;
  min-width: 10px;
  height: 14px;
  min-height: 14px;
  margin-right: 8px;
}


.text {
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  color: $dark-gray;

}


.container {
  position: relative;
  z-index: 1000;
  align-self: flex-start;
  flex: 1;

}


.input {
  font-family: $base-font;
  font-size: 14px;
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  width: 400px;
  height: 35px;
  padding: 0 36px;
  border: 1px solid #CCD7E6;
  border-radius: 5px;
  outline: none;
  background: #F0F3F8;


  &::placeholder {
    color: $dark-gray;
  }
}


.input__icon {
  position: absolute;
  z-index: 11;
  top: 9px;
  left: 10px;
  width: auto;
  height: 20px;
  color: #5E7AA1;
}


.input_active {
  border-radius: 5px 5px 0 0;
  box-shadow: 0 1px 5px rgba(0, 13, 33, 0.10);

}
