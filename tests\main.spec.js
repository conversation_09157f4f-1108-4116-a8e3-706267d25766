import { expect } from 'playwright-test-coverage';
import { test } from './baseFixtures';

import { captureAuthFrom } from '../test-helpers/firebaseAuth';
import path from 'node:path';
import axios from 'axios';
require('dotenv').config();

if (process.env.CI == null) {
  require('dotenv').config({ path: path.resolve(process.cwd(), '.env.tst.local') });
}

let currentInboxId;
test.afterAll(async () => {
  const authUserResult = process.env.FIREBASE_AUTH_IDB;
  // return;
  const authUserResultParsed = JSON.parse(authUserResult);
  const b = authUserResultParsed.value.stsTokenManager.accessToken;
  console.log('currentInboxId', currentInboxId);
  if (currentInboxId)
    await axios.delete(`${process.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${currentInboxId}`, {
      headers: {
        authorization: 'Bearer ' + b,
      },
      params: { key: process.env.PAPERBOX_BACKEND_API_KEY },
    });
});

const subTypeName1 = 'Testing SubType 1';
const subTypeName2 = 'Testing SubType 2';
const subTypeId1 = 'Testing Id';
const subTypeId2 = 'Testing Id2';
const bundleTypeName = 'Testing BundleType';
const bundleTypeId = 'Testing Id';
const mailTypeName = 'Testing MailType';
const mailTypeId = 'Mail Id';
const docTypeName = 'Testing DocType';
const docTypeId = 'Doc Id';
// const inboxName = 'Test';
// const inboxName = '1712559712555';
const inboxName = new Date().getTime().toString();
const fieldTypeNameExtra = 'Testing FieldType 0';
const fieldTypeName1 = 'Testing FieldType 1';
const fieldTypeName2 = 'Testing FieldType 2';
const fieldTypeName3 = 'Testing FieldType 3';
const fieldTypeName4 = 'Testing FieldType 4';
const metadataTypeName = 'Testing MetadataType';
const categoryName = 'Testing Category';
const connectorHTTPName = 'Connector HTTP';
const connectorSFTPName = 'Connector SFTP';
const connectorMS365Name = 'Connector MS365';
const connectorHTTPUrl = 'https://mock-webhook.paperbox.ai';

test.beforeEach('auth', async ({ page, baseURL }) => {
  await page.goto(`${baseURL}`);

  await page.fill('#email', '<EMAIL>');
  await page.fill('#password', process.env.VITE_TST_PASS);
  await page.click('data-testid=login-button');
  await page.waitForSelector('data-testid=home-grid');
  await captureAuthFrom(page);
});
// test('Admin Flow', async ({ page, baseURL }) => {
//   await page.goto(`${baseURL}`);
// });

test('Admin Flow', async ({ page, baseURL }) => {
  await page.goto(`${baseURL}`);

  await page.waitForSelector('data-testid=header-dropdown-user');
  await page.locator('data-testid=header-dropdown-user').click();
  await page.locator('data-testid=dropdown-admin').click();

  await page.locator('data-testid=inbox-add').click();
  await page.locator('data-testid=inbox-name-input').fill(inboxName);
  await page.locator('data-testid=inbox-retention-input').fill('6');
  await page.locator('data-testid=mailroom-toggle').click();
  await page.locator('data-testid=file-upload-toggle').click();
  await page.locator('data-testid=header-save').click();
  // await page.waitForSelector(`data-testid=inbox-label >> text=test`, { timeout: 30000 });
  await page.waitForSelector(`data-testid=inbox-label >> text=${inboxName}`, { timeout: 30000 });
  currentInboxId = await page.locator('data-testid=inbox-id-input').inputValue();

  await page.locator('data-testid=inbox-nav-metadata').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/metadata`);
  await page.locator('data-testid=metadata-add').click();
  await page.locator('data-testid=metadata-name-input').fill(metadataTypeName);
  await page.locator('data-testid=header-save').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/metadata`);
  await page.locator('data-testid=inbox-nav-fields').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/fields`);
  await page.locator('data-testid=fieldtype-add').click();
  await page.locator('data-testid=fieldtype-name-input').fill(fieldTypeNameExtra);
  await page.locator('data-testid=header-save').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/fields`);

  await page.locator('data-testid=fieldtype-add').click();
  await page.locator('data-testid=fieldtype-name-input').fill(fieldTypeName1);
  await page.locator('data-testid=fieldtype-dropdown').click();
  await page.locator(`data-testid=dropdown-option  >> text="Text"`).click();
  await page.locator('data-testid=header-save').click();

  await page.locator('data-testid=fieldtype-add').click();
  await page.locator('data-testid=fieldtype-name-input').fill(fieldTypeName2);
  await page.locator('data-testid=fieldtype-dropdown').click();
  await page.locator(`data-testid=dropdown-option  >> text="Boolean"`).click();
  await page.locator('data-testid=header-save').click();

  await page.locator('data-testid=fieldtype-add').click();
  await page.locator('data-testid=fieldtype-name-input').fill(fieldTypeName3);
  await page.locator('data-testid=fieldtype-dropdown').click();
  await page.locator(`data-testid=dropdown-option  >> text="Image"`).click();
  await page.locator('data-testid=header-save').click();

  await page.locator('data-testid=fieldtype-add').click();
  await page.locator('data-testid=fieldtype-name-input').fill(fieldTypeName4);
  await page.locator('data-testid=fieldtype-dropdown').click();
  await page.locator(`data-testid=dropdown-option  >> text="Complex"`).click();
  await page.locator('data-testid=doctype-fieldtype-add').click();
  var multiSelectItems = await page.locator(`data-testid=multi-select-option`);
  await multiSelectItems.nth(1).click();
  await multiSelectItems.nth(2).click();
  await page.locator('data-testid=multi-select-confirm').click({ force: true });

  await page.locator('data-testid=header-save').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/fields`);

  // Don't really like this, but need to wait just a bit to prevent a race condition with firestore

  await page.locator('data-testid=inbox-nav-doctypes').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/doctypes`);

  await page.locator('data-testid=doctype-add-Bundle').click();
  await page.locator('data-testid=doctype-name-input').fill(bundleTypeName);
  await page.locator('data-testid=doctype-provider-id-input').fill(bundleTypeId);
  await page.locator('data-testid=doctype-approval-threshold-input').fill('100');
  await page.locator('data-testid=doctype-ocr-threshold-input').fill('100');
  await page.locator('data-testid=doctype-metadata-add').click();
  await page.locator(`data-testid=multi-select-option >> text="${metadataTypeName}"`).click();
  await page.locator('data-testid=multi-select-confirm').click();
  const metadataSection = await page.locator('data-testid=doctype-metadata-section');
  const sortableItems = await metadataSection.locator('data-testid=admin-meta-row');
  await expect(sortableItems).toHaveCount(1);

  // //ADD FIELD TYPES

  await page.locator('data-testid=doctype-fieldtype-add').click();
  await page.locator('data-testid=multi-select-all').click();
  multiSelectItems = await page.locator(`data-testid=multi-select-option`);
  await expect(multiSelectItems).toHaveCount(5);

  await page.locator('data-testid=multi-select-confirm').click({ force: true });
  const fieldsSection = await page.locator('data-testid=doctype-fieldtype-section');
  //Expect there to be 1 metadata item

  const sortableFieldTypeItems = await fieldsSection.locator('data-testid=sortable-field');
  await expect(sortableFieldTypeItems).toHaveCount(5);

  await page.locator('data-testid=doctype-category-add').click();
  const categoryInput = await page.locator('data-testid=doctype-category-input');
  await expect(categoryInput).toBeFocused();
  await categoryInput.fill(categoryName);
  await categoryInput.press('Enter');
  await expect(page.locator('data-testid=doctype-category-name')).toContainText(categoryName);
  await page.locator('data-testid=doctype-category-field-add').click();
  await page.locator('data-testid=multi-select-all').click();
  await page.locator('data-testid=multi-select-confirm').click({ force: true });

  const categorySection = await page.locator('data-testid=doctype-category-section');
  const sortableCategoryItems = await categorySection.locator('data-testid=sortable-item');
  await expect(sortableCategoryItems).toHaveCount(5);

  //Delete 1 field type
  await fieldsSection.locator('data-testid=admin-item-row-delete').first().click();
  await expect(sortableFieldTypeItems).toHaveCount(4);
  await expect(sortableCategoryItems).toHaveCount(4);
  var first = sortableCategoryItems.first();
  await first.hover();
  await page.waitForTimeout(100);
  await first.getByTestId('sortable-item-delete').click();
  await page.locator('data-testid=doctype-subtype-add').click();

  await page.locator('data-testid=subtype-name-input').fill(subTypeName1);
  await page.locator('data-testid=subtype-provider-id-input').fill(subTypeId1);
  await page.locator('data-testid=header-save').click();
  let subtypeSection = await page.locator('data-testid=doctype-subtype-section');
  await expect(subtypeSection.locator('data-testid=admin-item-row')).toHaveCount(1);
  await page.locator('data-testid=header-save').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/doctypes`);

  // Add Mail type

  await page.locator('data-testid=doctype-add-Mail').click();
  await page.locator('data-testid=doctype-name-input').fill(mailTypeName);
  await page.locator('data-testid=doctype-provider-id-input').fill(mailTypeId);
  await page.locator('data-testid=header-save').click();

  // Add Document type

  await page.locator('data-testid=doctype-add-Document').click();
  await page.locator('data-testid=doctype-name-input').fill(docTypeName);
  await page.locator('data-testid=doctype-provider-id-input').fill(docTypeId);
  await page.locator('data-testid=header-save').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/doctypes`);

  await page.locator('data-testid=inbox-nav-fields').click();
  await page
    .locator(`data-testid=admin-item-row`, { hasText: fieldTypeNameExtra })
    .locator('data-testid=admin-item-row-delete')
    .click();
  await page.locator('data-testid=confirm-dialog-confirm').click();
  await page.locator('data-testid=inbox-nav-doctypes').click();
  await page.locator(`data-testid=admin-item-row`, { hasText: bundleTypeName }).click();

  // Patch a doctype with a new subtype

  await page.locator('data-testid=doctype-subtype-add').click();
  await page.locator('data-testid=subtype-name-input').fill(subTypeName2);
  await page.locator('data-testid=subtype-provider-id-input').fill(subTypeId2);
  await page.locator('data-testid=header-save').click();
  subtypeSection = await page.locator('data-testid=doctype-subtype-section');
  await expect(subtypeSection.locator('data-testid=admin-item-row')).toHaveCount(2);
  await page.locator('data-testid=inbox-nav-doctypes').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/doctypes`);

  // Masterdata
  await page.getByTestId('inbox-nav-masterdata').click();
  await page.getByTestId('masterdata-add').click();
  await page.getByTestId('masterdata-name-input').fill('Masterdata Table');
  await page.getByTestId('header-save').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/masterdata`);
  await page.getByTestId('admin-item-row').locator('div').first().click();
  await page.getByTestId('upload-table-input').setInputFiles(['./tests/documents/test-masterdata.csv']);
  await page.getByTestId('upload-table').click();
  // line-count-input
  const tableLineInput = await page.getByTestId('line-count-input');
  await expect(tableLineInput).toHaveValue('4');
  let masterdataRow = await page.locator('data-testid=masterdata-mapping-row', { hasText: 'claim_number' });
  await masterdataRow.getByTestId('masterdata-switcher').nth(2).click();
  await masterdataRow.locator('data-testid=dropdown-container').first().click();
  await page.locator(`data-testid=dropdown-option`, { hasText: fieldTypeName1 }).click();
  await masterdataRow.getByTestId('masterdata-searchable').click();

  masterdataRow = await page.locator('data-testid=masterdata-mapping-row', { hasText: 'last_name' });
  await masterdataRow.getByTestId('masterdata-switcher').nth(2).click();
  await masterdataRow.locator('data-testid=dropdown-container').first().click();
  await page.locator(`data-testid=dropdown-option`, { hasText: metadataTypeName }).click();

  await page.getByTestId('header-save').click();
  await page.waitForURL(`${baseURL}/admin/inboxes/${currentInboxId}/masterdata`);

  await page.locator('data-testid=admin-sidebar-users').click();

  const testingRow = await page.locator('data-testid=admin-user-row >> text=<EMAIL> >> ..');
  await testingRow.locator('data-testid=admin-user-dropdown').click();
  await page.locator(`data-testid=multi-select-option`, { hasText: inboxName }).click();
  await page.locator(`data-testid=multi-select-confirm`).click();
  // Create new User and Remove
  await page.getByTestId('admin-user-input').fill('<EMAIL>');
  await page.getByTestId('admin-user-add').click();
  await page.getByTestId('admin-user-search').fill('<EMAIL>');

  await page.waitForSelector('data-testid=admin-user-row >> text=<EMAIL>');
  var row = page.locator('data-testid=admin-user-row', { hasText: '<EMAIL>' });
  await row.getByTestId('admin-user-row-delete').click();
  await page.getByTestId('confirm-dialog-confirm').click();
  await expect(row).not.toBeVisible({ timeout: 20000 });

  // Connectors
  await page.getByTestId('admin-sidebar-connectors').click();
  await page.getByTestId('connector-add').click();
  await page.getByTestId('connector-name-input').fill(connectorHTTPName);
  await page.getByTestId('connector-base-url-input').fill(connectorHTTPUrl);
  const paramsGroup = await page.getByTestId('admin-request-group-query');
  await paramsGroup.getByTestId('webhook-row-key').first().fill('key 1');
  await paramsGroup.getByTestId('admin-data-input').first().fill('value 1');
  await paramsGroup.getByTestId('webhook-row-add').click();
  const webhookRows = paramsGroup.getByTestId('webhook-row');
  await expect(webhookRows).toHaveCount(2);
  const secondRow = webhookRows.nth(1);
  await secondRow.getByTestId('webhook-row-key').fill('key 2');
  const secondRowInput = await secondRow.getByTestId('admin-data-input');
  await secondRowInput.click();
  await page.mouse.wheel(0, 1000);
  await page.waitForTimeout(100);
  await secondRow.getByTestId('admin-data-dropdown-item').first().click();
  const special = await paramsGroup.getByTestId('admin-data-special').first();
  await special.locator('svg').click();
  await secondRow.getByTestId('webhook-row-hide').click();
  await expect(secondRowInput).not.toBeEditable();
  await secondRow.getByTestId('webhook-row-remove').click();
  await expect(webhookRows).toHaveCount(1);
  await page.getByTestId('header-save').click();
  await page.waitForSelector('[data-testid="header-save"]:disabled');

  // DElete action

  await page.getByTestId('connector-delete').click();
  await page.getByTestId('confirm-dialog-confirm').click();
  await page.waitForURL(`${baseURL}/admin/connectors/new`);
  //
  // await page.getByTestId('connector-add').click();
  // await page.getByTestId('connector-name-input').fill(connectorSFTPName);
  // await page.getByTestId('connector-type-input').click();
  // await page.locator(`data-testid=dropdown-option`, { hasText: 'SFTP' }).click();
  // await page.getByTestId('connector-ip-input').fill('***********');
  // await page.getByTestId('connector-port-input').fill('22');
  // await page.getByTestId('connector-username-input').fill('SFTP User');
  // await page.getByTestId('connector-password-input').fill('SFTP Password');
  //
  // await page.getByTestId('header-save').click();
  // await page.waitForSelector('[data-testid="header-save"]:disabled');
  //
  // await page.locator('data-testid=connector-item', { hasText: connectorSFTPName });
  // await page.getByTestId('connector-add').click();
  // await page.getByTestId('connector-name-input').fill(connectorMS365Name);
  // await page.getByTestId('connector-type-input').click();
  // await page.locator(`data-testid=dropdown-option`, { hasText: 'Microsoft 365 AD' }).click();
  // await page.getByTestId('connector-ms365-button').click();
  // await page.getByTestId('header-save').click();
  // await page.waitForSelector('[data-testid="header-save"]:disabled');
  //
  // await page.getByTestId('connector-delete').click();
  // await page.getByTestId('confirm-dialog-confirm').click();
  // await page.getByTestId('connector-delete').click();
  // await page.getByTestId('confirm-dialog-confirm').click();

  await page.locator('data-testid=admin-close').click();

  // return;
  // ------------------------- //
  // End of Admin Panel testing
  // ------------------------- //
  // return;
  await page.waitForTimeout(1000);
  await page.reload();

  await page.locator(`data-testid=inbox-select-active`).click();
  await page.locator(`data-testid=dropdown-option`, { hasText: inboxName }).click();
  await page.locator('data-testid=sidebar-item').first().click();
  await page.waitForTimeout(1000);

  await page.locator('data-testid=inbox-add-document-btn').click();
  const fileModal = await page.locator('data-testid=add-document-modal');

  const fileUpload = await page.locator('data-testid=document-upload-input');
  await fileUpload.setInputFiles(['./tests/documents/test-doc.pdf', './tests/documents/test-mail.eml']);

  await fileModal.locator('data-testid=dropdown-container').first().click();
  await page.locator(`data-testid=dropdown-option`, { hasText: bundleTypeName }).click();

  await fileModal.locator('data-testid=dropdown-container').nth(1).click();
  await page.locator(`data-testid=dropdown-option`, { hasText: subTypeName1 }).click();

  await fileModal.locator('data-testid=document-upload-confirm').click();
  await page.waitForSelector('data-testid=document-upload-again', { timeout: 40000 });
  await fileModal.locator('data-testid=add-document-close').click();

  const element = page.locator('data-testid=inboxes-sidebar-segment');
  await element.locator('data-testid=sidebar-item').first().click();
  // });

  // test('Document Processing', async ({ page, baseURL }) => {
  await page.locator(`data-testid=inbox-select-active`).click();
  // await page.locator(`data-testid=dropdown-option`, { hasText: '1712059320405' }).click();
  await page.locator(`data-testid=dropdown-option`, { hasText: inboxName }).click();
  await page.locator('data-testid=sidebar-item').first().click();

  //Upload new documents

  await page.waitForSelector('id=table-row-1', { timeout: 3 * 60 * 1000 });
  const tableHeader = await page.locator('data-testid=inbox-table-th').nth(1);
  await tableHeader.click();
  await tableHeader.click();
  await page.waitForSelector('data-testid=table-row-name');
  const tableRows = await page.locator('data-testid=table-row');
  await tableRows.first().click();

  //Sidebar

  // Check low confidence entity
  const firstRow = page.locator('data-testid=sidebar-entity').first();
  await firstRow.locator('data-testid=low-confidence').isVisible();

  const sidebarCheckOcr = await page.locator('data-testid=sidebar-check-ocrConfidence');
  await sidebarCheckOcr.getByTestId('check-warning').isVisible();
  await sidebarCheckOcr.hover();
  await page.waitForSelector('data-testid=check-override').then((res) => res.click());

  await page.locator('data-testid=sidebar-entity').first().locator('data-testid=sidebar-row-delete').click();
  await page.locator('data-testid=sidebar-entity').first().locator('data-testid=sidebar-row-delete').click();
  await sidebarCheckOcr.getByTestId('check-success').isVisible();

  //Label entities of every type

  // Check is search mode is visible
  const searchMode = await page.locator('data-testid=labeler-tool');
  await expect(searchMode).toHaveCount(2);
  await searchMode.nth(1).click();

  // --- Boolean value
  await page.waitForSelector('data-testid=checkthis');
  await page.waitForTimeout(200);
  await page.locator('data-testid=fit-document').first().click();
  await page.waitForTimeout(500);

  const labelerElement = await page.locator('data-testid=labeler');
  const labelerBoundingBox = await labelerElement.boundingBox();
  const toolbarConfirmButton = await page.locator('data-testid=toolbar-confirm');

  await expect(toolbarConfirmButton).toBeDisabled();
  // Test labelling of Boolean
  await page.mouse.move(labelerBoundingBox.x + 80, labelerBoundingBox.y + 100);
  await page.mouse.down();
  await page.mouse.move(labelerBoundingBox.x + 350, labelerBoundingBox.y + 130);
  await page.mouse.up();
  await page.waitForTimeout(200);
  const toolInput = await page.locator('data-testid=tool-value-input');
  await expect(toolInput).toHaveValue('In bijlage vindt u een factuur.');
  await expect(toolbarConfirmButton).not.toBeDisabled();
  await toolbarConfirmButton.click();
  const toolbar = await page.getByTestId('document-toolbar');
  await toolbar.locator('data-testid=dropdown-container').click();
  const dropdownOptions = await page.locator('data-testid=dropdown-option');
  const dropdownOptionsLength = await dropdownOptions.evaluateAll((divs) => divs.length);
  expect(dropdownOptionsLength).toBe(5);

  // Test labelling of Image
  await page.locator(`data-testid=dropdown-option`, { hasText: fieldTypeName2 }).first().click();

  await page.mouse.move(labelerBoundingBox.x + 200, labelerBoundingBox.y + 200);
  await page.mouse.down();
  await page.mouse.move(labelerBoundingBox.x + 225, labelerBoundingBox.y + 225);
  await page.mouse.up();

  await page.locator('data-testid=toolbar-confirm').click();
  await toolbar.locator('data-testid=dropdown-container').click();

  await page.locator(`data-testid=dropdown-option`, { hasText: fieldTypeName3 }).click();
  await page.mouse.click(labelerBoundingBox.x + 150, labelerBoundingBox.y + 150);
  await page.mouse.move(labelerBoundingBox.x + 150, labelerBoundingBox.y + 150);
  await page.mouse.down();
  await page.mouse.move(labelerBoundingBox.x + 175, labelerBoundingBox.y + 175);
  await page.mouse.up();

  await page.locator('data-testid=toolbar-confirm').click();
  //Edit all possible entities values
  //Edit Text input
  const sidebarTextRow = await page.locator('data-testid=sidebar-entity', {
    hasText: 'In bijlage vindt u een factuur.',
  });
  const textValue = await sidebarTextRow.locator('data-testid=sidebar-entity-value');
  await textValue.click();

  const rowInput = await page.locator('data-testid=sidebar-row-string-input');
  await expect(rowInput).toHaveValue('In bijlage vindt u een factuur.');
  await page.waitForTimeout(5000);
  await rowInput.fill('Edited by Test');

  await page.locator('data-testid=sidebar-row-edit-confirm').click();

  const newRow = await page.locator('data-testid=sidebar-entity', { hasText: 'Edited by Test' });
  await expect(newRow).toBeVisible();

  const sidebarCheckboxRow = await page.locator('data-testid=sidebar-entity >> data-testid=checkbox-input');
  await expect(sidebarCheckboxRow).not.toBeChecked();
  await page.locator('data-testid=sidebar-entity >> data-testid=checkbox').click();
  await expect(sidebarCheckboxRow).toBeChecked();

  //Delete Single row.
  const rows = await page.locator('data-testid=sidebar-entity');

  await expect(newRow).toBeVisible();
  await page
    .locator('data-testid=sidebar-entity', { hasText: 'edited' })
    .locator('data-testid=sidebar-row-delete')
    .click();
  await expect.soft(newRow).not.toBeVisible();

  //Delete all Entities;
  await page.locator('data-testid=delete-all-fields').click();
  await page.locator('data-testid=confirm-dialog-confirm').click();
  await page.waitForSelector('data-testid=no-entities');

  await expect(rows).not.toBeVisible();

  const urlBeforeNav = page.url();
  //Go to Next document
  await page.locator('data-testid=doc-next').click();
  const urlAfterNav = page.url();

  await expect(urlAfterNav).not.toBe(urlBeforeNav);
  //Go to previous document
  await page.locator('data-testid=doc-prev').click();
  const urlAfterNavBack = page.url();

  await expect(urlAfterNavBack).toBe(urlBeforeNav);

  //Approve document
  await page.waitForSelector('[data-testid="doc-next"]:not([disabled])');
  // await waitForButtonStablyEnabled(page, '[data-testid="doc-approve"]', 5000);
  await page.waitForTimeout(10000);
  await page.locator('[data-testid="doc-approve"]:not([disabled])').click();
  await page.waitForURL(urlAfterNav);

  //Create mutation
  await page.locator('data-testid=header-badge-add').click();
  await page.waitForSelector('data-testid=header-badge-add-plus');

  //Change document type
  await page.locator('data-testid=header-badge-edit').nth(1).click();
  await page.waitForSelector('data-testid=type-selector-modal');
  await page.locator('data-testid=type-selector-change').click();
  await expect(page.locator('data-testid=header-badge-edit').nth(1)).toContainText('No Type');
  const mutationDelete = await page.locator('data-testid=header-badge-delete').nth(1);
  await mutationDelete.click();
  await page.waitForSelector('data-testid=confirm-dialog');
  await page.locator('data-testid=confirm-dialog-confirm').click();

  // await page.waitForTimeout(500);
  await page.locator('data-testid=header-badge-delete').nth(1).waitFor({ state: 'detached' });
  // await page.waitForTimeout(500);

  //Delete document
  // await page.locator('data-testid=header-badge-delete').nth(0).click();
  // await page.waitForSelector('data-testid=confirm-dialog');
  // await page.locator('data-testid=confirm-dialog-confirm').click();
  // await page.waitForSelector('data-testid=inbox-table-empty');
  // });
  //
  // test('Document Masterdata Processing', async ({ page }) => {
  //   await page.locator(`data-testid=inbox-select-active`).click();
  //   // await page.locator(`data-testid=dropdown-option`, { hasText: '1712059320405' }).click();
  //   await page.locator(`data-testid=dropdown-option`, { hasText: inboxName }).click();
  //   await page.locator('data-testid=sidebar-item').first().click();
  //
  //   //Upload new documents
  //   await page.waitForSelector('id=table-row-0');
  //   await page.waitForSelector('data-testid=table-row-name');
  //   const tableRows = await page.locator('data-testid=table-row');
  //   await tableRows.first().click();
  const masterdataInput = await page.getByTestId('masterdata-search-input');
  await masterdataInput.isEnabled();
  await masterdataInput.fill('123456789');
  await page.getByTestId('masterdata-search-button').click();
  await page.waitForSelector('data-testid=masterdata-result-card');
  await page.getByTestId('masterdata-result-card').hover();
  await page.getByTestId('masterdata-import').click();
  const entity = await page.locator('data-testid=sidebar-entity', { hasText: '1234567890' });
  await entity.isVisible();
  const metadataDocSection = await page.getByTestId('metadata-section');
  const metadataCount = await metadataDocSection.locator('data-testid=section-count');
  await expect(metadataCount).toHaveText('1');
  await metadataDocSection.click();
  await page.locator('data-testid=sidebar-entity', { hasText: 'Baumgartner' }).isVisible();
  await page.getByTestId('masterdata-clear').click();
  await expect(entity).not.toBeVisible();
  await expect(metadataDocSection).not.toBeVisible();

  await page.mouse.move(labelerBoundingBox.x + 400, labelerBoundingBox.y + 40);
  await page.mouse.down();
  await page.mouse.move(labelerBoundingBox.x + 410, labelerBoundingBox.y + 50);
  await page.mouse.up();
  await page.keyboard.press('Enter');
  await expect(masterdataInput).toHaveValue('123456789');
  // await expect(masterdataInput).toHaveValue('O2219550)');
  await page.getByTestId('header-back-button').click();
  await page.getByTestId('inbox-sidebar-historical').click();
  await page.reload();
  await page.locator('id=table-row-0 >> div[data-testid="table-row-name"]').click();
  const historicalSection = await page.getByTestId('historical-section');
  await expect(historicalSection).toBeVisible();
  const processedBy = await page.getByTestId('sidebar-metadata').first();
  await expect(processedBy).toHaveText('<NAME_EMAIL>');
});
