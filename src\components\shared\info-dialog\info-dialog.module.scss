@import "@shared/styles/vars/_vars";


.container {
  @include flex-center;
  flex-direction: column;
  width: 420px;
  padding: 25px;
  border-radius: 10px;
  background: $white;
  box-shadow: 0 2px 15px rgba(0, 13, 33, 0.30);

}


.icon {

  width: auto;
  height: 80px;
  stroke-width: 1.5px;

  &__info {
    color: $paperbox-blue;
  }

  &__warning {
    color: $warning;
  }

  &__error {
    color: $error;
  }
}


.title {
  font-family: $base-font;
  font-size: 20px;
  font-weight: bold;
  margin: 12px 0;
}


.text {
  font-size: 14px;
  line-height: 1.25;
}


.close {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;

  color: $font-color-black;

  &:hover {
    color: $error;

  }
}
