import { DropdownOption } from '@components/shared/dropdown/StyledSelect.tsx';

export interface AdminFlatDocTypeCategory {
  name: string;
  id: string;
  entityTypes: string[];
}

export interface DocTypeRule {
  property: string;
  operator: string;
  value: string;
}

export interface AdminEndpoint {
  id: string;
  connectorId?: string;
  path?: string;
  headers: Record<string, any>;
  method: string;
  params: Record<string, string>;
  payload: Record<string, any>;
  auth?: any;
}

export interface AdminWebhook {
  id: string;
  endpointId?: string;
  endpoint?: AdminEndpoint;
  name: string;
  events?: string[];
  inboxes?: string[];
  actionTypes?: string[];
  active?: boolean;
}

export interface WebhookServerSideValue {
  definition: {
    inputs: any[];
    output: any;
    outputs: Record<string, any>;
    description?: string;
  };
  id: string;
  name: string;
}
export interface AdminQuickEditField {
  name: string;
  label: string;
  type: 'text' | 'select' | 'boolean' | 'color';
  options?: DropdownOption[];
  valueKey: string;
  valueList?: string;
  isClearable?: boolean;
  onChange: (id: string, value: any) => void;
}
