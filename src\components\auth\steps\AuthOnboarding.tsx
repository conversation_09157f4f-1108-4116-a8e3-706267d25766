import AuthButton from '@components/auth/methods/AuthButton';
import GoogleButton from '@components/auth/methods/GoogleButton';
import SSOButton from '@components/auth/methods/SSOButton';
import Input from '@components/shared/input/Input';
import { generateSignInEmail } from '@shared/store/adminSlice';
import { useDispatch, useSelector } from '@shared/store/store';
import s from '@shared/styles/component/auth/auth.module.scss';
import { ReactComponent as PaperboxLogo } from '@svg/paperbox-logo.svg';
import { Pulsar } from '@uiball/loaders';
import clsx from 'clsx';
import queryString from 'query-string';
import React, { useEffect, useState } from 'react';
import { useResizeDetector } from 'react-resize-detector';
import { useLocation } from 'react-router';
import { CSSTransition } from 'react-transition-group';
import AuthOTPSent from './AuthOTPSent';

const AuthOnboarding: React.FC = () => {
  const providers = useSelector((state) => state.tenant.providers);
  const tenantConfig = useSelector((state) => state.tenant.details.config);
  const isPopupActive = useSelector((state) => state.user.isAuthPopupActive);

  const location = useLocation();
  const { ref } = useResizeDetector({
    refreshRate: 1,
    refreshMode: 'throttle',
  });

  const [email, setEmail] = useState('');
  const [emailSent, setEmailSent] = useState(false);
  const [innerHeight, setInnerHeight] = useState(375);
  const [isRunning, setIsRunning] = useState(false);
  const [errorCode, setErrorCode] = useState('');

  const calcHeight = (el) => {
    setInnerHeight(el.offsetHeight);
  };

  const handleError = (err) => {
    setErrorCode(err.code);
  };
  const dispatch = useDispatch();

  const handleSubmit = (e) => {
    e.preventDefault();
    setIsRunning(true);
    dispatch(generateSignInEmail(email))
      .then(() => {
        setEmailSent(true);
      })
      .catch(() => {
        setErrorCode('auth/user-not-found');
      })
      .finally(() => {
        setIsRunning(false);
      });
  };

  useEffect(() => {
    if (ref.current) {
      calcHeight(ref.current);
    }
  }, [ref]);

  useEffect(() => {
    const email = queryString.parse(location.search).email as string;
    setEmail(email);
  }, [location]);

  return (
    <div className={s.container}>
      <div className={s.card}>
        <PaperboxLogo className={s.logo} />
        <h2 className={s.title}>Hello, Welcome to Paperbox</h2>

        {isPopupActive && (
          <div className={s.loading}>
            <Pulsar size={50} color={'#0085FF'} /> Authenticating
          </div>
        )}
        {!isPopupActive && (
          <div className={s.card_inner} style={{ height: innerHeight }}>
            <CSSTransition
              unmountOnExit
              mountOnEnter
              classNames={'auth-slide'}
              timeout={500}
              onEnter={calcHeight}
              in={!emailSent}
            >
              <div className={clsx(s.buttons)} ref={ref}>
                {providers.map((provider) => {
                  if (provider.type === 'inboundSamlConfigs') {
                    return (
                      <SSOButton
                        text={`Log in with ${provider.displayName}`}
                        handleError={handleError}
                        type="inboundSamlConfigs"
                      />
                    );
                  }
                  if (provider.type === 'oauthIdpConfigs') {
                    return (
                      <SSOButton
                        text={`Log in with ${provider.displayName}`}
                        handleError={handleError}
                        type="oauthIdpConfigs"
                      />
                    );
                  }
                  if (
                    tenantConfig.googleLogin === true &&
                    provider?.name.includes('defaultSupportedIdpConfigs/google.com')
                  ) {
                    return (
                      <GoogleButton
                        text={'Continue with Google'}
                        handleError={handleError}
                      />
                    );
                  }
                  return null;
                })}

                {(() => {
                  // Check if there are any actual rendered login methods above the separator
                  const hasGoogleLogin = tenantConfig.googleLogin &&
                    providers.some(provider => provider?.name.includes('defaultSupportedIdpConfigs/google.com'));

                  const hasSSOLogin = providers.some(provider =>
                    provider.type === 'inboundSamlConfigs' || provider.type === 'oauthIdpConfigs'
                  );

                  const hasEmailPasswordLogin = providers.some(provider => provider.type === 'EmailPassword');

                  // Only show divider if there are actual login methods above AND email/password login below
                  const shouldShowDivider = (hasGoogleLogin || hasSSOLogin) && hasEmailPasswordLogin;

                  return shouldShowDivider && (
                    <div className={s.divider}>
                      <div className={s.divider_text}>OR</div>
                    </div>
                  );
                })()}

                {providers.filter((item) => item.type === 'EmailPassword').length >= 1 && (
                  <form onSubmit={handleSubmit} autoComplete="on">
                    <div className={s.input}>
                      <Input
                        hasError={errorCode === 'auth/user-not-found'}
                        errorText={
                          errorCode === 'auth/user-not-found' ? 'Please use a valid email address.' : ''
                        }
                        setValue={setEmail}
                        value={email}
                        id="email"
                        type="email"
                        placeholder={'Enter your email'}
                      />
                    </div>
                    <AuthButton isActive={!!email} isLoading={isRunning}>
                      Continue
                    </AuthButton>
                  </form>
                )}
              </div>
            </CSSTransition>
            <CSSTransition
              onEnter={calcHeight}
              unmountOnExit
              mountOnEnter
              classNames={'auth-slide'}
              timeout={500}
              in={emailSent}
            >
              <AuthOTPSent email={email} type={'onboard'} />
            </CSSTransition>
          </div>
        )}
      </div>
    </div>
  );
};

export default AuthOnboarding;
