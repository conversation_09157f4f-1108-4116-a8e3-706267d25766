import AuthOTPEntry from '@components/auth/steps/AuthOTPEntry';
import PasswordReset from '@components/reset/PasswordReset';
import queryString from 'query-string';
import React, { useEffect } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router';

const AuthActionContainer: React.FC = () => {
  const location = useLocation();
  const mode = queryString.parse(location.search).mode as string;
  const oobCode = queryString.parse(location.search).oobCode as string;
  const tenantId = queryString.parse(location.search).tenantId as string;

  const navigate = useNavigate();

  useEffect(() => {
    if (!mode) {
      navigate('/login', { replace: true });
    }
  }, [mode, navigate]);

  return (
    <div>
      {mode === 'resetPassword' ? (
        <PasswordReset oobCode={oobCode} tenantId={tenantId} />
      ) : mode === 'verifyEmail' ? (
        <Navigate to="/login" replace />
      ) : mode === 'signIn' ? (
        <AuthOTPEntry />
      ) : (
        <Navigate to="/login" replace />
      )}
    </div>
  );
};
export default AuthActionContainer;
