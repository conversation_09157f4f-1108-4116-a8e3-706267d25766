import { sleep } from '@shared/helpers/helpers';
import useRecaptchaVerifier from '@shared/hooks/useRecaptchaVerifier';
import { auth } from '@shared/store/setup/firebase-setup';
import s from '@shared/styles/component/two-factor/two-factor.module.scss';
import { ReactComponent as PaperboxLogoLight } from '@svg/paperbox-logo-light.svg';
import clsx from 'clsx';
import { PhoneAuthProvider, PhoneMultiFactorGenerator, multiFactor } from 'firebase/auth';
import React, { useCallback, useEffect, useState } from 'react';
import PhoneInput from 'react-phone-input-2';
import { useNavigate } from 'react-router';
import Recaptcha from '../auth/helpers/Recaptcha';
import Button from '../shared/button/Button';
import { TwoFactorCodeInput } from './TwoFactorCodeInput';

interface Props {}

const TwoFactor: React.FC<Props> = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [verificationId, setVerificationId] = useState<string | undefined>();
  const [verificationSuccess, setVerificationSuccess] = useState(false);
  const navigate = useNavigate();
  const { showVerifier, removeVerifier } = useRecaptchaVerifier();
  const [errorCode, setErrorCode] = useState(null);
  const [firebaseUser, setFirebaseUser] = useState(null);

  useEffect(() => {
    if (auth) {
      setFirebaseUser(auth.currentUser);
    }
  }, []);

  useEffect(() => {
    showVerifier();
    return () => {
      removeVerifier();
    };
  }, [removeVerifier, showVerifier]);

  useEffect(() => {
    if (!verificationId) {
      // @ts-ignore
      import(/* webpackChunkName: "phone-css" */ 'react-phone-input-2/lib/style.css');
    }
  }, [verificationId]);

  const confirmSMSCode = (e) => {
    e.preventDefault();
    const appVerifier = window['recaptchaVerifier'];
    if (!appVerifier) return;
    const multiFactorUser = multiFactor(firebaseUser);
    multiFactorUser
      .getSession()
      .then((res) => {
        // Specify the phone number and pass the MFA session.
        const phoneInfoOptions = {
          phoneNumber: `+${phoneNumber}`,
          session: res,
        };

        const phoneAuthProvider = new PhoneAuthProvider(auth);
        // Send SMS verification code.
        phoneAuthProvider
          .verifyPhoneNumber(phoneInfoOptions, appVerifier)
          .then((verificationId) => {
            setVerificationId(verificationId);
          })
          .catch((err) => {
            setErrorCode(err.message);
          });
      })
      .catch((err) => {
        setErrorCode(err.message);
      });
  };
  const enrollUser = useCallback(
    async (e) => {
      e.preventDefault();
      const cred = PhoneAuthProvider.credential(verificationId, smsCode);
      const multiFactorAssertion = PhoneMultiFactorGenerator.assertion(cred);
      const multiFactorUser = multiFactor(firebaseUser);
      multiFactorUser
        .enroll(multiFactorAssertion, 'PhoneNumber')
        .then(async () => {
          setVerificationSuccess(true);
          await sleep(3500).then(() => navigate('/login'));
        })
        .catch((err) => {
          setErrorCode(err.code);
        });
    },
    [firebaseUser, navigate, smsCode, verificationId],
  );

  const unEnrollUser = useCallback(
    async (e) => {
      e.preventDefault();

      const multiFactorUser = multiFactor(firebaseUser);
      const options = multiFactorUser.enrolledFactors;
      multiFactorUser
        .unenroll(options[0])
        .then(() => {
          navigate('/login');
        })
        .catch((err) => {
          setErrorCode(err.code);
        });
    },
    [firebaseUser, navigate],
  );
  return (
    <div className={s.container}>
      <Recaptcha />
      <PaperboxLogoLight onClick={() => navigate('/inbox')} className={s.logo} />
      <div className={s.card}>
        {firebaseUser && multiFactor(firebaseUser).enrolledFactors.length !== 0 ? (
          verificationSuccess ? (
            <>
              <h1 className={s.title}>Success</h1>
              <h2 className={s.sub}>
                Two Factor activated successfully, you will now be redirected to the login screen.
              </h2>
              <Button
                text="To Login"
                type="submit"
                className={clsx(s.button)}
                id="mfa__button"
                onClick={() => navigate('/login')}
              />
            </>
          ) : errorCode === 'auth/requires-recent-login' ? (
            <>
              <h1 className={s.title}>Log In again</h1>
              <h2 className={s.sub}>Please log in again before disabling 2FA</h2>
              <Button
                text="To Login"
                type="submit"
                className={clsx(s.button)}
                id="mfa__button"
                onClick={() => navigate('/login')}
              />
            </>
          ) : (
            <>
              <h1 className={s.title}>Disable 2FA</h1>
              <h2 className={s.sub}>
                <b>Warning</b> , disabling 2FA will prevent access to the website until re enabled.
              </h2>
              <form onSubmit={unEnrollUser}>
                <Button
                  text="Disable"
                  type="submit"
                  className={clsx(s.button, s.button_warning)}
                  id="mfa__button"
                />
              </form>
            </>
          )
        ) : (
          <>
            <h1 className={s.title}>Please enable 2FA. </h1>
            {!verificationId ? (
              <form onSubmit={confirmSMSCode}>
                <h2 className={s.sub}>Enter your phone number to activate two-factor authentication.</h2>

                {errorCode && (
                  <span className={s.error} style={{ fontSize: 14 }}>
                    Session expired, please{' '}
                    <button
                      style={{ color: 'black', textDecoration: 'underline' }}
                      onClick={() => navigate('/login')}
                    >
                      Login
                    </button>{' '}
                    again
                  </span>
                )}
                <PhoneInput
                  containerClass={s.input_container}
                  country={'be'}
                  value={phoneNumber}
                  onChange={(phoneNumber) => setPhoneNumber(phoneNumber)}
                />

                <Button text="Send SMS" type="submit" className={s.button} id="mfa__button" />
              </form>
            ) : (
              <form onSubmit={enrollUser}>
                <h2 className={s.sub}>SMS Sent, enter the verification code.</h2>
                <TwoFactorCodeInput
                  setValue={setSmsCode}
                  error={errorCode === 'auth/invalid-verification-code' ? errorCode : null}
                />
                <Button text="Confirm" type="submit" className={s.button} id="mfa__button" />
              </form>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default TwoFactor;
