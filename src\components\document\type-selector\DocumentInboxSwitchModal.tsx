import ContentModal from '@components/shared/content-modal/ContentModal.tsx';
import StyledSelect, { DropdownOption } from '@components/shared/dropdown/StyledSelect';
import { useModal } from '@shared/hooks/useModal';
import { useSelector } from '@shared/store/store';
import { userInboxesSelector } from '@shared/store/userSlice.ts';
import s from '@shared/styles/component/document/document-type-switch.module.scss';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
  handleConfirm: (newInboxId: string) => Promise<any>;
  currentInboxId: string;
}

const DocumentInboxSwitchModal: React.FC<Props> = ({ handleConfirm, currentInboxId }) => {
  const inboxes = useSelector(userInboxesSelector);
  const { closeModal } = useModal();
  const { t } = useTranslation();

  const [inboxOptions, setInboxOptions] = useState<DropdownOption[]>([]);
  const [inboxValue, setInboxValue] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleConfirmSelection = () => {
    if (!inboxValue) return;
    setIsLoading(true);
    return handleConfirm(inboxValue.value);
  };

  useEffect(() => {
    if (inboxes) {
      const map: DropdownOption[] = inboxes.map((inbox: any) => ({
        value: inbox.id,
        label: inbox.settings.name,
      }));
      const currentInbox = inboxes.find((inbox: any) => inbox.id === currentInboxId);
      let filtered;
      if (currentInbox?.settings.inboxMoveWhitelistActive) {
        const whitelist = currentInbox?.settings.inboxMoveWhitelist;
        filtered = map.filter((inbox: DropdownOption) => whitelist.includes(inbox.value));
      } else {
        filtered = map.filter((inbox: DropdownOption) => inbox.value !== currentInboxId);
      }
      setInboxOptions(filtered);
      if (filtered.length > 0) {
        setInboxValue(filtered[0]);
      }
    }
  }, [currentInboxId, inboxes]);

  const buttonText = useMemo(() => {
    if (!inboxValue) return 'No Valid Inboxes';
    return t('document:typeSwitch.inboxButton');
  }, [inboxValue, t]);

  return (
    <ContentModal
      title={t('document:typeSwitch.inboxTitle')}
      description={t('document:typeSwitch.inboxDescription')}
      onClose={closeModal}
      onAction={handleConfirmSelection}
      actionButtonText={buttonText}
      isActionDisabled={isLoading || !inboxValue}
      initialFocus
    >
      <div className={s.content}>
        <span className={s.label}>{t('document:typeSwitch.inbox')}</span>
        <StyledSelect
          value={inboxValue}
          defaultValue={inboxOptions[0]}
          onChange={setInboxValue}
          options={inboxOptions}
          isLoading={!inboxOptions}
        />
      </div>
    </ContentModal>
  );
};

export default DocumentInboxSwitchModal;
