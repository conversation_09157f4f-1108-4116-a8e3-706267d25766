/* Container for the 3D letter */
.letter_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto; /* Adjust the size */
  height: 80vh;
  transform: translateY(-50%);
  animation: moveLetter 2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
  aspect-ratio: 0.707;
  perspective: 3000px; /* Adds depth for 3D */
}


/* Letter container */
.letter {
  position: relative;
  width: 100%;
  height: 100%;
  transform-origin: top center;
  transform-style: preserve-3d;

  img {
    width: 100%;
    height: auto;
  }
}


/* Top Half of the Letter */
.letter::before {
  position: absolute;

  z-index: 10;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  content: '';
  transform: rotateX(-180deg); /* Starts folded down */
  transform-origin: bottom;
  animation: unfoldTop 1.2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
  animation-delay: 0.5s;
  border-bottom: none;
  background: white;
  background-size: cover;
}
.letter_nl::before {
  animation: unfoldTopNL 1.2s cubic-bezier(0.25, 1, 0.5, 1) forwards;
}


/* Bottom Half of the Letter */
.letter::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 50%;
  content: '';
  border-top: none;
  background: white;
  background: url('./letter_en.jpg');
  background-position: bottom;
  background-size: cover;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.letter_nl::after {
  background: url('./letter_nl.jpg');
  background-position: bottom;
  background-size: cover;
}


/* Keyframe for overall unfolding */
@keyframes unfoldLetter {
  0% {
    transform: rotateX(-0deg); /* Starts flat */
  }
  100% {
    transform: rotateX(0deg); /* Ends fully open */
  }
}


/* Keyframe for top half unfolding */
@keyframes unfoldTop {
  0% {
    transform: rotateX(-180deg); /* Fully folded */
    background: white;
  }
  95% {
    background-image: url('./letter_en.jpg');
  }

  100% {
    transform: rotateX(0deg); /* Fully unfolded */
    background-image: url('./letter_en.jpg');

  }
}

@keyframes unfoldTopNL {
  0% {
    transform: rotateX(-180deg); /* Fully folded */
    background: white;
  }
  95% {
    background-image: url('./letter_nl.jpg');
  }
  100% {
    background-position: top;
    transform: rotateX(0deg); /* Fully unfolded */
    background-image: url('./letter_nl.jpg');
  }
}

@keyframes moveLetter {
  0% {
    transform: translateY(50%) scale(0.2);
  }
  30% {
    transform: scale(1) translateY(-20%);
  }
  100% {
    transform: translateY(0);
  }
}
