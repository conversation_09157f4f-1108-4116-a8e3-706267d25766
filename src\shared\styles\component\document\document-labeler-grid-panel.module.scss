@import "../../vars/_vars";


.wrapper {
  position: relative;
  z-index: 1000;
  overflow: hidden;
  width: 100%;
  color: black;
  border-radius: 7px;
  background: white;
  box-shadow: 0 -2px 10px rgba(0, 13, 33, 0.1);
}


.bar {
  position: relative;
  width: calc(100%);
  padding: 10px;
  border-bottom: 1px solid $medium-gray;
}


.handle {

  position: absolute;
  z-index: 998;
  bottom: 0;
  width: 100%;
  height: 12px;
  cursor: row-resize;
  user-select: none;
  background: transparent;
}


.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 8px;

}


.toggle {
  @include flex-center;

  position: absolute;

  right: 20px;

  span {
    font-size: 14px;
    font-weight: 500;
    margin: 8px;
  }
}


.button {
  font-size: 14px;
  font-weight: 500;
  height: 32px;
  padding: 6px 16px;
  cursor: pointer;
  color: white;
  border-radius: 5px;
  background: $paperbox-blue;
  gap: 6px;

  svg {
    width: 18px;
    height: auto;
    margin-top: 0;
    color: white;
  }

  @include flex-center;

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

}


.table {
  font-size: 12px;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 100%;
  height: calc(100% - 80px);
  margin: 0 auto;
  padding: 0 80px;
  border-radius: 5px;
}


.headers {
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  height: auto;
  margin-top: 5px;
  border-bottom: 1px solid #F1F5FD;
}


.header {
  font-size: 14px;
  font-weight: 500;
  width: 100%;
  padding: 15px 8px;
  text-align: center;

}


.columns {
  display: flex;
  overflow: auto;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  border-radius: 4px;
}


.column {
  font-weight: 500;
  width: 100%;

  &:hover {
    cursor: pointer;
  }

  &_hide_wrapper {

  }

  &__hide {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    min-width: 24px;
    height: 27px;
    margin: 8px 1px;
    transform: rotate(90deg);
    border-radius: 50px;

    &_button {
      margin-top: 10px;
      color: white;
      background: $paperbox-blue;
    }
  }
}


.cell {
  @include flex-center;
  font-weight: 500;
  flex-grow: 0;
  width: calc(100% - 16px);
  height: 27px;
  margin: 8px;
  padding-right: 10px;
  padding-left: 10px;
  text-align: center;
  border: none;
  border-bottom: 1px solid $medium-light-gray;
  border-radius: 4px;
  background: #F1F5FD;

  &__error {
    border-collapse: collapse;
    border-bottom: 2px solid $error;
  }

  &:hover {
  }

  &:focus:not(.disabled) {
    transition: 0.2s box-shadow ease-in-out;
    outline: none;
    background: $medium-light-gray;
    box-shadow: 0 0 1px 2px $paperbox-blue--fade;
  }

  &:hover:not(.disabled) .arrow:not(.disabled) {
    svg path {
      fill: $paperbox-blue;
    }
  }
}


.selectors {
  position: absolute;
  top: -4px;
  display: none;
  overflow: hidden;
  flex-direction: column;
  width: 100%;
  padding: 4px;
  border-radius: 10px;
  background: $dark-blue;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.25);

  &__active {
    display: block;
  }
}


.selector {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 32px;
  padding: 8px 12px;
  color: white;
  border-radius: 5px;

  span {
    display: -webkit-box;
    overflow: hidden;
    white-space: normal;
    text-overflow: ellipsis;
    word-break: normal;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  svg {
    display: none;
  }

  & + & {
    margin-top: 4px;
  }

  &:hover:not(.selector__current) {
    color: $font-color-black;
    background: $medium-gray;
  }

  .icon_mandatory {
    display: none;
  }

  &__mandatory {
    .icon_mandatory {
      display: block;
    }
  }

  &__picked {
    &:hover:not(.selector__current) {
      color: white !important;
      background: $error !important;
      background: rgba($error, 0.7);
    }

    .icon {
      margin-right: -8px;
      margin-left: 8px;

      svg {
        display: block;
        flex-shrink: 0;
        width: 14px;
        height: auto;
        margin-top: 0;
      }

    }
  }

  &__current {
    background: $paperbox-blue;

  }

}


.selector_selected {
  width: 100%;
  height: 100%;
  min-height: 26px;
  padding: 3px;
  color: white;
  border-radius: 5px;
  background: $dark-blue;
}


.selector_wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 10px;
}


:global {
  .labeler-table-enter {
    max-height: 0;
    margin-top: 0;
    margin-bottom: 0;
    padding: 0;
    opacity: 0;
  }

  .labeler-table-enter-active {
    max-height: 27px;
    margin: 8px;
    transition: all 300ms ease-out;
    opacity: 1;
  }

  .labeler-table-exit {
    max-height: 27px;
  }

  .labeler-table-exit-active {
    max-height: 0;
    margin-top: 0;
    margin-bottom: 0;
    padding: 0;
    transition: all 300ms ease-out;
    opacity: 0;
    border-width: 0;
  }

  .labeler-table-exit-done {
    display: none;

  }

}

