@import '../../vars/_vars';


::-webkit-scrollbar {
  width: 3px;
}


::-webkit-scrollbar-track {
  background: $light-gray;
}


::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: $medium-light-gray;
}


.placeholder {
  @include flex-center;
  flex-direction: column;
  height: calc(100% - 65px);
}


.icon__right {
  width: 14px;
  min-width: 16px;
  height: auto;
  margin-right: 7px;
  margin-left: 6px;
  cursor: pointer;
  transition: transform 0.1s ease-in-out;
  opacity: 0;

  &.check {
    min-width: 16px;
    margin-left: 6px;
    color: $success;
  }

  &.delete {
    color: $medium-dark-gray;

    &:hover {
      color: $error;
    }
  }

  &:hover {
    transform: scale(1.3) !important;
  }
}


.icon__visible {
  opacity: 1;
}


@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}


.field__text {
  font-size: 14px;
  width: auto;
  min-width: unset !important;
  height: 100%;
  transition: border 0.2s;
  color: $font-color-black;
  border-bottom: 1px solid transparent;

  &:hover {
    border-bottom: 1px solid $paperbox-blue--fade;
  }

  &_master {
    &:hover {
      border-bottom: none !important;
    }
  }
}


.divider {
  width: 100%;
  height: 0px;
  border-bottom: 1px dashed #eeeeee;
}


.item {
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 45px;
  max-height: 45px;
  padding: 12px 6px 12px 12px;
  transition: padding-left, opacity, border-left, 0.15s ease-in-out;
  opacity: 1;
  border-left: 2px solid transparent;
  border-radius: 5px;
  outline: none;
  background: $white;
  will-change: border, padding;

  :global(.rs-picker-default .rs-picker-toggle) {
    height:35px !important;
    border: 1px solid $medium-gray;
    border-radius: 8px;
  }

  &:hover:not(.item__active) {
    cursor: pointer;
    background: rgba($paperbox-blue, 0.15);

    .icon__right {
      opacity: 1;
    }

    .title {
      color: black;
    }

  }

  &__active {
    cursor: pointer;
    transition: padding-left, border-left, 0.15s ease-in-out;
    border-left: 2px solid $paperbox-blue;
    background: $paperbox-blue--fade-extra;
    will-change: border, padding;

    .icon__right {
      opacity: 1;
    }

    .title {
      transition: margin-right 0.15s ease-in-out;
      color: $font-color-black;
    }

    .field__text {
      width: auto;
      min-width: 15px;
      min-height: 10px;
      color: $font-color-black;

      &:hover:not(.item__child) {
        border-bottom: 1px solid $medium-dark-gray;
      }

      &_master {
        &:hover {
          border-bottom: none !important;
        }
      }
    }

    .icon {
      width: 16px;
      min-width: 16px;
      height: auto;
      margin-right: 7px;
      margin-left: 7px;
      opacity: 1;
    }

    .delete {
      color: $error !important;
    }
  }

  &__editing {
    height: 60px;
    margin: unset;
    padding: unset;
    border: none !important;
  }

  &__drag {
    font-size: 14px;
    border: 1px solid #EEEEEE;
    background: white !important;
    box-shadow: rgba(99, 99, 99, 0.2) 0 1px 4px 0;

  }
}


.item_wrapper {
  display: flex;
  flex-direction: column;
  transition: max-height 0.25s ease-in-out;
  will-change: max-height;
}


.complex_wrapper {
  display: flex;
  overflow: hidden;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-start;
  max-width: 100%;
  max-height: 47px;
  border: 1px solid transparent;
  border-radius: 5px;

  .title {
    transform: translateX(-2px);
  }

  &__open {
    max-height: unset;
    border: 1px solid #EEEEEE;

    .item__complex .chevron {
      transform: rotate(180deg);
    }
  }

  .complex_fields {
    display: flex;
    flex-direction: column;
    width: 100%;

    .item {
      border-left: none;
      border-radius: unset;

      .add {
        font-size: 14px;
        display: flex;
        align-items: center;
        height: 21px;

        margin-right: 10px;
        color: $paperbox-blue;
        gap: 8px;

        &:hover {
          font-weight: 500;
        }

        svg {
          color: $paperbox-blue;
        }

      }
    }

  }

}


.multi_wrapper {
  display: flex;
  overflow: hidden;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-start;
  max-width: 100%;
  max-height: 0;
  padding: 0 18px;
  transition: max-height 0.2s, height 0.2s, padding 0.2s, opacity 0.2s;
  opacity: 0;
  background: rgba($paperbox-blue, 0.05);
  gap: 8px;

  &__visible {
    height: auto;
    max-height: 60px;

    padding: 12px 12px;
    opacity: 1;
  }

  &__source {
    &_user {
      background: rgba(#00ff59, 0.04);

      .multi_item {
        color: rgba(#00ff59, 0.5);
        border: 1px solid rgba(#00ff59, 0.5);

        &_cross {
          color: rgba(#00ff59, 0.5);
        }

        &__active,
        &:hover {
          color: #00ff59;
          border: 1px solid #00ff59;
        }
      }
    }

    &_model {
      background: rgba(#ff00ea, 0.04);

      .multi_item {
        color: rgba(#ff00ea, 0.5);
        border: 1px solid rgba(#ff00ea, 0.5);

        &_cross {
          color: rgba(#ff00ea, 0.5);
        }

        &__active,
        &:hover {
          color: #ff00ea;
          border: 1px solid #ff00ea;
        }
      }
    }
  }

  &__suggestion {
    background: rgba(#ff6a00, 0.04);

    .multi_item {
      color: rgba(#ff6a00, 0.5);
      border: 1px solid rgba(#ff6a00, 0.5);

      &_cross {
        color: rgba(#ff6a00, 0.5);
      }

      &__active,
      &:hover {
        color: #ff6a00;
        border: 1px solid #ff6a00;
      }
    }

  }

  &__historical {
    .multi_item {
      &:hover {
        padding-right: 14px;
      }
    }
  }
}


.multi_item {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(100% / 5.6);

  height: 26px;
  padding: 0 14px;
  transition: color 0.1s, border 0.1s, gap 0.2s;
  color: rgba($paperbox-blue, 0.5);
  border: 1px solid rgba($paperbox-blue, 0.5);
  border-radius: 5px;
  background: white;
  gap: 0;

  &_cross {
    width: 0;
    margin-right: -3px;
    margin-left: 3px;
    transition: width 0.2s;
    color: rgba($paperbox-blue, 0.5);
  }

  &:hover {
    padding-right: 0px;
    gap: 4px;

    & .multi_item_cross {
      min-width: 16px;

      &:hover {
        color: $error;
      }
    }
  }

  &__active,
  &:hover {
    color: $paperbox-blue;
    border: 1px solid $paperbox-blue;
  }

  &:focus {
    animation: buttonClick 0.3s;
    outline: none;
  }
}


@keyframes buttonClick {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.9);
  }

}


.counter {
  @include flex-center;
  font-size: 12px;
  font-weight: 500;
  flex-shrink: 0;
  width: 20px;
  height: 16px;
  margin-top: -2px;
  margin-right: 8px;
  padding-top: 2px;
  text-align: center;
  color: white;
  border-radius: 5px;
  background: $paperbox-blue;
}


.item__complex {
  display: flex;

  .item_right {
    font-size: 8px;
    font-weight: 500;
    display: flex;
    margin-right: 6px;
    gap: 6px;

    .field__badge {
      font-size: 12px;
      font-weight: 700;

      &__error {
        background: $error;
      }
    }
  }

  &.item__active {
    border-left: none;
  }
}


@keyframes growIn {
  0% {
    transform: scale(1.2);
    opacity: 0.5;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }

}


.complex_check_wrapper {
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  gap: 12px;

  .checked {
    font-size: 12px;
    font-weight: 800;
    display: flex;
    align-items: center;
    margin-top: 2px;
    margin-right: 10px;
    letter-spacing: 1px;
    text-transform: uppercase;
    gap: 6px;
  }

  .image {
    width: 40px;
    height: 30px;
    max-height: 30px;

    &_wrapper {
      height: 28px;
    }
  }

  .placeholder {
    font-family: $base-font;
    font-size: 14px;

    width: auto;
    height: 100%;
    padding: 6px 5px 5px 5px;
    color: $dark-gray;
  }

  .button {
    display: flex;
    height: 100%;
    padding: 8px;
    color: $paperbox-blue;
    border: 1px solid #EEEEEE;
    border-radius: 5px;

    svg {
      width: 14px;
      height: 16px;
    }

  }
}


.complex_input_wrapper {
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  animation: growIn 0.5s;
  border: none;

  border-radius: 5px;
  box-shadow: 0 0 1px 2px $paperbox-blue--fade;

  button {
    display: flex;
    align-items: center;
    height: 30px;
    color: $paperbox-blue;
    padding-inline: 10px;

    svg {
      width: 14px;
      height: 16px;
    }

    &:hover {
      color: white;
      background-color: $paperbox-blue;
    }
  }

  .image {
    max-height: 30px;
  }

  .placeholder {
    font-family: $base-font;
    font-size: 14px;

    width: 100%;
    height: 100%;
    padding: 6px 5px 5px 5px;
    color: $dark-gray;
  }

  .complex_input {
    font-family: $base-font;
    font-size: 14px;
    width: 100%;
    height: 100%;
    padding: 6px 5px 5px 5px;
    transition: padding 0.4s ease-in-out;
    text-align: center;
    border: none;

    outline: none;

    &::placeholder {
      color: $dark-gray;

    }

    &__invalid {
      border: 1px solid rgba($error, 0.8);

    }

    &__valid {
      border: 1px solid rgba($success, 1);

    }

    //&:focus:not(.input__invalid):not(.input__valid) {
    //  box-shadow: 0 0 1px 2px $paperbox-blue--fade;
    //}
  }
}


.item__complex_empty {
  background-color: white !important;
}


.item__lowConfidence {
  background: rgba($warning, 0.15) !important;

  &:hover:not(.item__active) {
    cursor: pointer;

    background: rgba($warning, 0.25) !important;

    .title {
      color: $font-color-black;
    }

    .field {
      color: $font-color-black;
    }
  }

  &.item__active {
    cursor: pointer;
    border-left: 2px solid $warning;
    background: rgba($warning, 0.35);
  }

  .title {
    color: #373739;
  }

  .field {
    color: #373739;
  }
}


.item__invalid {
  background: rgba($error, 0.15) !important;

  &.item__active {
    cursor: pointer;
    border-left: 2px solid $error;
  }
}


.item__pending {
  pointer-events: none;
  opacity: 0.5;
}


.item__source {
  &_user {
    background: rgba(#00ff59, 0.05);

    &.item__multi {
      .counter {
        background: #00ff59;
      }
    }

    &.item__active {
      border-left: 2px solid #00ff59;
    }

  }

  &_user_historical {
    background: rgba(#0085FF, 0.05);

    &.item__multi {
      .counter {
        background: #0085FF;
      }
    }

    &.item__active {
      border-left: 2px solid #0085FF;
    }
  }

  &_model {
    background: rgba(#ff00ea, 0.05);

    &.item__multi {
      .counter {
        background: #ff00ea;
      }
    }

    &.item__active {
      border-left: 2px solid #ff00ea;

    }
  }

}


.item__suggestion {
  background: rgba(#ff6a00, 0.1);

  &.item__multi {
    .counter {
      background: #ff6a00;
    }
  }

  &.item__active {
    border-left: 2px solid #ff6a00;

  }
}


.category_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 12px 16px 12px 12px;
  cursor: pointer;
  user-select: none;
  border-radius: 5px;

  &_title {
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: black;
    gap: 8px;
  }

  &_dot {
    width: 8px;
    height: 2px;
    border-radius: 5px;
  }

  &:hover &_arrow {
    transition: transform 0.1s ease-in-out, color 0.1s ease-in-out;
    color: $paperbox-blue;
  }

  &_arrow {
    width: 10px;
    height: auto;
    transition: transform 0.2s ease-in-out;
    color: $dark-gray;

    &__active {
      transition: transform 0.2s ease-in-out;
      transform: rotate(180deg);
    }
  }
}


.field_info {
  display: flex;
  align-items: center;

  svg {
    flex-shrink: 0;

    width: 16px;
    height: 16px;
    margin-right: 2px;
    margin-bottom: 1px;
    color: $paperbox-blue;
  }
}


.icon__labeling {
  opacity: 0.8 !important;

  &:hover {
    opacity: 1 !important;
  }

  &.delete {
    color: $error;
  }
}


.category {
  animation: fadeIn 0.3s;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
}


.category_rows {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  max-height: 50px;
  transition: max-height 0.2s ease-in-out, opacity 0.1s ease-in-out;
  opacity: 0.2;
  gap: 4px;

  &__open {
    transition: max-height 0.2s ease-in-out, opacity 0.1s ease-in-out;
    opacity: 1;
  }
}


.title {
  font-family: $base-font;
  font-size: 14px;
  display: flex;
  overflow: hidden;
  align-items: center;
  flex-basis: min-content;
  flex-grow: 1;
  min-width: 30%;
  min-height: 20px;
  max-height: 45px;
  margin-right: 8px;
  transition: margin-right 0.15s ease-in-out;
  text-align: left;
  text-transform: capitalize;
  text-overflow: ellipsis;
  color: $font-color-black;
  gap: 8px;
}


.field {
  font-size: 14px;
  display: -webkit-box;
  overflow: hidden;
  text-align: end;
  text-overflow: ellipsis;
  word-wrap: normal;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;

  &__meta {
    padding-right: 30px;
  }
}


.field__badge {
  display: flex;
  align-items: center;
  height: 22px !important;
  padding: 8px;
  color: white;
  border-radius: 5px;
  background: $paperbox-blue;
  gap: 8px;

  span {
    font-size: 14px;
  }

  svg {
    width: 16px;
    height: auto;
  }
}

.field__list {
  display: flex;
  align-items: center;
  text-align: end;
}

.value_list {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;

  .item {
    font-size: 13px;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    border-radius: 4px;
    padding: 2px 4px;
    font-weight: 500;
    background-color: white;
    color: $paperbox-blue;
    border: 1px solid $paperbox-blue--fade;
    width:unset;
    min-height: unset;
    &:hover{
      background: white;
    }
  }
}

.value_text {
  color: black;
}

.tooltip_content {
  color: white;

  b {
    font-weight: 500;
    color: $paperbox-blue;
  }
}

.tooltip_item {
  line-height: 1.4;
}

.tooltip_text {
  color: black;
}


.editor {
  font-family: $base-font;
  font-size: 14px;
  width: 100%;
  padding: 5px 10px;
  border: 1px solid #EEEEEE;

  border-radius: 5px 0 0 5px;

  &:focus {
    outline: none;
  }
}


.drag_handle {
  min-width: 14px;
  max-width: 14px;
  min-height: 16px;
  max-height: 16px;
  margin-top: 1px;
  margin-right: 2px;
  margin-left: -8px;
  cursor: grab;
  color: rgba($paperbox-blue, 0.5);
}


.image {
  max-width: 150px;
  height: 40px;
  max-height: 40px;

  &_wrapper {
    @include flex-center;
    overflow: hidden;
    flex-grow: 0;
    flex-shrink: 1;
    width: 150px;
    max-width: 100%;
    height: 40px;
    border-radius: 5px;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 13, 33, 0.1);
    object-position: center;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &__loading {
      width: 150px;
      height: 45px !important;
      background: $light-gray;

      svg {
        height: 100%;
        color: $medium-gray;
      }
    }
  }
}


.tooltip_wrapper {
  font-family: $base-font;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  flex-direction: column;
}


.tooltip_bottom {
  margin-top: 6px;
  padding-top: 6px;
  border-top: 1px solid $medium-gray;
}


.tooltip {
  display: flex;
  justify-content: space-between;
  padding: 2px;

  b {
    font-weight: 700;
    margin-left: 8px;
  }
}


.item_right {
  min-width: 0;
  max-width: 50%;
  text-align: end;
}


.item_warning {
  flex-shrink: 0;
  width: 12px;
  height: 12px;
  margin-right: 8px;
  transform: translateY(-1px);
  color: $warning;
}


.item_info {
  flex-shrink: 0;
  width: 12px;
  height: 12px;
  margin-right: 8px;
  transform: rotate(180deg) translateY(-1px);
  color: $icon-gray;
}


.item_imported {
  flex-shrink: 0;
  width: 16px;
  min-width: 16px;
  height: 16px;
  color: $paperbox-blue;
}


.icon_button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 6px 14px;
  transition: background 0.15s ease-in-out;
  color: $paperbox-blue;
  border: 1px solid #EEEEEE;
  border-left: none;
  border-radius: 0 5px 5px 0;
  background: white;

  &:hover {
    background: #EEEEEE;

    svg {
      transform: scale(1.1);
      color: black;
    }
  }

  svg {
    height: 20px;
    transition: all 0.15s ease-in-out;
  }
}


.icon {
  position: relative;
  width: 0;
  min-width: 0;
  height: auto;
  margin-right: 15px;
  margin-bottom: 3px;
  margin-left: 15px;
  cursor: pointer;

  color: $medium-dark-gray;

  &.menu {
    transform: rotate(90deg);
  }

  &:hover {
    color: $paperbox-blue-light;
  }
}


.search {
  font-family: $base-font;
  font-size: 14px;
  width: 100%;
  height: 33px;
  min-height: 33px;
  margin-bottom: 16px;
  padding-right: 12px;
  padding-left: 12px;
  border: 1px solid rgba($dark-gray, 0.2);
  border-radius: 5px;
  outline: none;

  background: $light-gray;

  &::placeholder {
    color: #bdbdbd;
  }
}


.fields {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 8px;
}


.placeholder_text {
  font-size: 14px;
  font-weight: 500;
  width: 100%;
  padding: 10px;
  text-align: center;
  color: $font-color-black;
}


.wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  will-change: width, minWidth;
}


.metadata {
  overflow-y: auto;
  flex: 1;
  height: auto;
  min-height: 0;
  transition: all 0.2s ease-in-out;

}


.metadata_slider {
  position: absolute;
  z-index: 9;
  top: -10;
  display: flex;
  justify-content: center;
  width: 100%;
  min-height: 15px;
  cursor: row-resize;
  background: transparent;

  &_inner {
    display: flex;
    justify-content: center;
    width: 30px;
    height: 12px;
    transition: opacity 0.2s;
    transform: translateY(-6px);
    opacity: 0;
    color: white;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    background: #ffffff;

    svg {
      height: 12px;
      transform: rotate(90deg);
      color: #e0e0e0;
    }
  }

  &:hover {
    .metadata_slider_inner {
      opacity: 1;
    }
  }
}


.header {
  font-size: 14px;
  font-weight: 400;
  position: relative;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  justify-content: center;
  height: 50px;
  cursor: pointer;
  transition: border-bottom-color 0.2s ease-in-out;
  text-align: center;
  border-top: 1px solid $medium-gray;
  border-bottom: 1px solid transparent;

  &__open {
    border-bottom: 1px solid rgba($medium-gray, 0.5);
  }

  .header_icon {
    position: absolute;
    top: calc(50% - 9px);
    left: 28px;
    width: 18px;
    height: 18px;
    cursor: pointer;
    transition: transform 0.2s ease-in-out;

    &__open {
      transition: transform 0.2s ease-in-out;
      transform: rotateX(180deg);
    }

    &__delete {
      right: 28px;
      left: unset;
      width: 12px;

      color: rgba(94, 122, 161, 0.3);

      &:hover {
        color: $error;
      }
    }
  }
}


.option {
  font-size: 14px;
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 10px;
  padding: 10px;

  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  text-align: left;
  border: 1px solid $medium-gray;
  border-radius: 5px;
  background: white;

  will-change: max-height, opacity, padding, margin, border;

  &:last-of-type {
    margin-bottom: 20px;
  }

  &:hover:not(.active) {
    transition: background-color 0.2s ease-in-out;
    background: $light-gray;
  }

  span {
    margin-left: 16px;
  }
}


.sub_list {
  overflow-y: auto;
  margin-bottom: 10px;
  padding: 10px;
  transition: max-height 0.2s ease-in-out;
  border-radius: 0 0 10px 10px;
  background: $light-gray;
}


.delete_section {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px 20px 0 20px;

  .description {
    padding-bottom: 8px;
  }

  .options {
    overflow-y: scroll;
    height: 100%;
    margin-right: -18px;
    padding-right: 18px;

    &:after {
      position: absolute;
      z-index: 9998;
      top: 157px;
      width: 100%;
      height: 30px;
      content: ' ';
      background: linear-gradient(180deg, white, white 30%, transparent 100%);
    }

    &:before {
      position: absolute;
      z-index: 9998;
      bottom: 0;
      width: 100%;
      height: 30px;
      content: ' ';
      background: linear-gradient(0deg, white, white 30%, transparent 100%);
    }
  }

  .title {
    font-weight: 500;
    align-self: center;
    width: 100%;
    margin-bottom: 24px;
    text-align: center;
    color: black;
  }

  p {
    font-size: 14px;
    margin-bottom: 16px;
  }
}


.item__meta {

  &:hover {
    cursor: default !important;
    color: black !important;
    background: $white !important;
  }
}


:global {
  .option-enter {
    transform: scaleY(0.9) scaleX(0.95);
    opacity: 0;
  }

  .option-enter-active {
    transition: opacity 200ms, transform 200ms;
    transform: translateX(0);
    opacity: 1;
  }

  .option-exit {
    overflow: hidden;
    max-height: 35px;
    opacity: 1;
  }

  .option-exit-active {
    max-height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    transition: all 400ms ease-in-out;
    opacity: 0 !important;
    border: none;
  }
}
