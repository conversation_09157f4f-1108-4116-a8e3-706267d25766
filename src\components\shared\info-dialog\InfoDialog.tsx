import { useModal } from '@shared/hooks/useModal';
import { ReactComponent as AlertCircleIcon } from '@svg/alert-circle.svg';
import { ReactComponent as AlertTriangleIcon } from '@svg/alert-triangle.svg';
import { ReactComponent as CrossIcon } from '@svg/cross-icon.svg';
import clsx from 'clsx';
import React from 'react';
import Modal from '../modal/Modal';
import s from './info-dialog.module.scss';

interface Props {
  type: 'info' | 'warning' | 'error';
  title: string;
  text?: string;
}

const InfoDialog: React.FC<Props> = ({ text, type, title }) => {
  const { closeDialog } = useModal();
  return (
    <Modal isDialog>
      <div className={s.container}>
        <CrossIcon onClick={closeDialog} className={s.close} />
        {type === 'info' && <AlertCircleIcon className={clsx(s.icon, s.icon__info)} />}
        {type === 'warning' && <AlertTriangleIcon className={clsx(s.icon, s.icon__warning)} />}
        {type === 'error' && <AlertCircleIcon className={clsx(s.icon, s.icon__error)} />}
        <span className={s.title}>{title}</span>
        <span className={s.text}>{text}</span>
      </div>
    </Modal>
  );
};

export default InfoDialog;
