module.exports = {
    root: true,
    env: {browser: true, es2020: true,node:true},
    extends: [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
        'plugin:react-hooks/recommended',
        'prettier'
    ],
    ignorePatterns: ['dist', '.eslintrc.cjs'],
    parser: '@typescript-eslint/parser',
    plugins: ['react-refresh','prettier'],
    rules: {
        "prettier/prettier": [
            "warn",
            {
                "endOfLine": "auto"
            }
        ],

        // "import/order": [
        //     "error",
        //     {
        //         "groups": ["builtin", "external", "internal", ["parent", "sibling"]],
        //         "pathGroups": [
        //             {
        //                 "pattern": "react",
        //                 "group": "external",
        //                 "position": "before"
        //             }
        //         ],
        //         "pathGroupsExcludedImportTypes": ["react"],
        //         "newlines-between": "never",
        //         "alphabetize": {
        //             "order": "asc",
        //             "caseInsensitive": true
        //         }
        //     }
        // ],

        'react-refresh/only-export-components': [
            'warn',
            {allowConstantExport: true},
        ],
        "@typescript-eslint/no-explicit-any": "off",
        '@typescript-eslint/no-var-requires': 0,

    },
}
