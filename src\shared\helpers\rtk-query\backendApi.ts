import { createApi } from '@reduxjs/toolkit/dist/query/react';
import { axiosBaseQuery } from '@shared/helpers/apiClient.ts';
import { PageTokens } from '@shared/models/document.ts';

export const backendApi = createApi({
  reducerPath: 'backendApi',
  baseQuery: axiosBaseQuery({
    baseUrl: import.meta.env.VITE_PAPERBOX_BACKEND_URL,
  }),
  endpoints: (builder) => ({
    getDocumentJSON: builder.query<PageTokens[], { inboxId: string; docId: string }>({
      query: ({ inboxId, docId }) => {
        return { url: `/inboxes/${inboxId}/documents/${docId}?type_=json`, method: 'GET' };
      },
    }),
  }),
});

export const { useGetDocumentJSONQuery } = backendApi;
