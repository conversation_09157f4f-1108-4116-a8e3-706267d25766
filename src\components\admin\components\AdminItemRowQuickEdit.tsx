import Checkbox from '@components/shared/checkbox/Checkbox.tsx';
import ColorPicker from '@components/shared/color-picker/ColorPicker.tsx';
import StyledSelect from '@components/shared/dropdown/StyledSelect.tsx';
import { usePrevious } from '@shared/helpers/helpers.ts';
import { useSelector } from '@shared/store/store.ts';
import s from '@shared/styles/component/admin/admin-item-row.module.scss';
import { ReactComponent as CheckmarkIcon } from '@svg/checkmark-alt-icon.svg';
import { Ring } from '@uiball/loaders';
import clsx from 'clsx';
import React, { useEffect, useMemo, useState } from 'react';

interface Props {
  item: any;
  isLoading?: boolean;
}

const AdminItemRowQuickEdit: React.FC<Props> = ({ item, isLoading }) => {
  const activeQuickEditField = useSelector((state) => state.admin.activeQuickEditField);
  const [initialValue, setInitialValue] = useState(null);
  const [textValue, setTextValue] = useState(null);
  const prevActiveQuickEditField = usePrevious(activeQuickEditField) ?? ({} as any);

  useEffect(() => {
    if (prevActiveQuickEditField?.name !== activeQuickEditField?.name) {
      setInitialValue(null);
      setTextValue(null);
    }
  }, [prevActiveQuickEditField, activeQuickEditField]);

  const field = useMemo(() => {
    if (!activeQuickEditField) return null;
    let activeItem;
    if (activeQuickEditField.valueList) {
      activeItem = item[activeQuickEditField.valueList].find(
        (e) => e.id === activeQuickEditField.valueKey,
      )?.value;
    } else {
      activeItem = item[activeQuickEditField.valueKey];
    }
    if (!initialValue) setInitialValue(activeItem);
    if (activeQuickEditField.type === 'text' && textValue === null) {
      setTextValue(activeItem);
    }
    switch (activeQuickEditField.type) {
      case 'text':
        return (
          <>
            <input
              placeholder={'......'}
              type="text"
              value={textValue}
              onChange={(e) => {
                setTextValue(e.target.value);
              }}
            />
            {initialValue !== textValue && (
              <button
                className={s.button}
                onClick={() => {
                  setInitialValue(textValue);
                  activeQuickEditField.onChange(item.id, textValue);
                }}
              >
                <CheckmarkIcon />
              </button>
            )}
          </>
        );
      case 'select': {
        const val = activeQuickEditField.options.find((o) => o.value === activeItem);
        return (
          <StyledSelect
            autoNext={false}
            isClearable={activeQuickEditField.isClearable ?? true}
            style={{ height: '100%', border: 'none' }}
            options={activeQuickEditField.options}
            value={val}
            onChange={(newVal) => {
              activeQuickEditField.onChange(item.id, newVal?.value);
            }}
          />
        );
      }
      case 'boolean':
        if (typeof activeItem !== 'boolean') activeItem = activeItem != null;
        return (
          <Checkbox
            type="checkbox"
            checked={activeItem}
            onClick={() => activeQuickEditField.onChange(item.id, !activeItem)}
          />
        );
      case 'color':
        return (
          <ColorPicker
            activeColor={activeItem}
            onChange={(color) => activeQuickEditField.onChange(item.id, color)}
          />
        );
      default:
        return null;
    }
  }, [activeQuickEditField, item, initialValue, textValue]);
  if (!activeQuickEditField) return null;

  return (
    <div className={clsx(s.action, s.action__long)} style={{ width: '20vw', maxWidth: 300 }}>
      {isLoading ? (
        <div className={s.loading_container}>
          <Ring size={14} color="#0085FF" lineWeight={5} />
        </div>
      ) : (
        field
      )}
    </div>
  );
};

export default AdminItemRowQuickEdit;
