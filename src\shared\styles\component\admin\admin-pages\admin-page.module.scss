@import "../../../vars/_vars";


.inboxes {
  display: flex;
  overflow: auto;
  align-items: center;
  flex-shrink: 0;
  flex-wrap: wrap;
  width: calc(100% - 170px);
  max-height: 72px;
  white-space: nowrap;
  gap: 10px;

}


.inboxes_wrapper {
  display: flex;
  flex-shrink: 0;
  justify-content: space-between;
  height: auto;
  margin-top: 13px;
  margin-bottom: 30px;
}


.inbox {
  font-size: 16px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 30px;
  padding: 6px 22px;
  cursor: pointer;
  user-select: none;
  transition: border-color 0.2s ease-in-out;
  transition-property: color, border-color;
  color: #9EA3B4;
  border: 1px solid transparent;
  border-radius: 5px;
  background: white;
  will-change: border-color, color;
  gap: 8px;

  &:hover, &__active {
    color: black;
    border: 1px solid #EEEEEE;
  }

  &__active {
    animation: bubble 0.2s ease-in-out;
  }

  &_indicator {
    width: 6px;
    height: 6px;
    margin-left: -6px;
    border-radius: 5px;
    background-color: $success;

    &__disabled {
      background-color: $error;
    }
  }

  svg {
    color: #969FAD;
  }

}


@keyframes bubble {
  0% {
    transform: scale(0.9);
    border: 1px solid $paperbox-blue;
  }

  100% {
    transform: scale(1);
    border: 1px solid #EEEEEE;

  }
}


.body {
  position: relative;
  display: flex;
  align-items: stretch;
  flex-direction: column;
  width: 100%;
  max-width: 1440px;
  height: 100%;
  margin: 0 auto;
  padding: 25px;

}


.button {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  transition: 0.3s ease-in-out;
  transition-property: background-color, color;
  white-space: nowrap;
  color: white;
  border: 1px solid transparent;
  border-radius: 5px;
  background: $paperbox-blue;
  gap: 10px;

  &:disabled {
    cursor: default;
    color: #BCBEBF;
    border: 1px solid #EEEEEE;
    background: white;
  }

  &__alt {
    color: #BCBEBF;
    border: 1px solid #EEEEEE;
    background: white;
  }
}


.body_header {
  display: flex;
  justify-content: space-between;
  margin: 15px 0 10px 0;

  h2 {
    font-family: $headings-font;
    font-size: 28px;
    font-weight: 700;

  }

}


.body_search {
  display: flex;
  flex-direction: row;
  width: 100%;
  margin-top: 20px;
  margin-bottom: 0;
  gap: 8px;
}


.body_description {
  font-size: 16px;
  line-height: 1.5;
  max-width: 75%;
  margin-top: 16px;
}


.body_header_action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;

  &_input {
    min-width: 180px;
  }
}


.body_header_action_error {
  font-size: 14px;
  color: $error;
}


.search_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 60px;
}


.overlay {
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 25px;
  animation: shoveIn 0.3s ease-in-out;
  background: #FCFBFC;

}


@keyframes shoveIn {
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }

}


.nav_grid {
  display: grid;
  width: 100%;
  margin-top: 100px;
  grid-template-columns: repeat(auto-fill, minmax(325px, 1fr));
  grid-gap: 30px;
}


.nav_grid_card {
  position: relative;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  justify-content: space-between;
  height: 150px;
  padding: 20px;
  cursor: pointer;
  border: 1px solid #EEEEEE;
  border-radius: 10px;

  background: white;

  &:hover {
    svg {
      color: $paperbox-blue;
    }

    .nav_grid_card_icon {
      background: $paperbox-blue--fade;
    }
  }

  &__add {
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #EEEEEE;

    svg {
      width: 14px;
      height: 14px;
    }

    &:hover {
      color: black;
    }
  }
}


.nav_grid_card_background {
  position: absolute;
  top: 40%;
  right: -10%;
  width: 50%;
  height: auto;
  opacity: 0.07;
  color: #898B99;
}


.nav_grid_card_header {
  font-size: 20px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}


.nav_grid_card_icon {
  z-index: 7;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  justify-content: center;
  width: 40px;
  height: 40px;
  transition: all 0.15s ease;
  color: #898B99;
  border-radius: 100%;
  background: #EEEEEE;

  svg {
    width: 20px;
    height: auto;
  }
}


.nav_grid_card_body {
  display: flex;
  flex-direction: column;
  color: #898B99;
  gap: 10px;

  b {
    font-weight: 600;
    letter-spacing: 1px;
  }

  .tag {
    width: 12px;
    height: auto;
    margin-top: -3px;
  }
}


.nav_grid_card_body_title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
}


.nav {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  height: 40px;
  border-bottom: 1px solid #EEEEEE;
}


.nav_items {
  display: flex;
  align-items: flex-end;
  width: 100%;
  height: 40px;
  gap: 20px;

}


.nav_indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100px;
  height: 2px;
  transition: 0.2s ease-in-out;
  transition-property: left, width;
  border-radius: 5px 5px 0 0;
  background: $paperbox-blue;
}


.nav_item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 10px 30px 10px 10px;
  cursor: pointer;
  user-select: none;
  color: #6F758A;
  border-bottom: 2px solid transparent;

  &:focus {
    outline: unset;
  }

  &__active {
    color: $paperbox-blue;
  }
}


.section_wrapper {
  position: relative;
  display: flex;
  overflow: auto;
  flex-direction: column;
  flex-grow: 1;
  margin-top: 20px;
  margin-right: -10px;
  margin-left: -10px;
  padding-right: 10px;
  padding-left: 10px;
  will-change: opacity;
}


.header {
  display: flex;
  justify-content: space-between;
  width: 100%;
}


.header_nav {
  font-size: 14px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  justify-content: space-between;
  width: 100%;
  height: 50px;
  cursor: pointer;
  transition: color 0.2s ease-in-out;
  gap: 10px;

  .header_nav_group {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .header_nav_item {
    display: flex;
    align-items: center;
    gap: 10px;

    &:hover {
      color: $paperbox-blue;
    }

    &:disabled {
      color: #6F758A;
    }
  }

  svg {
    width: 16px;
    height: auto;
    margin-top: 1px;
  }
}


.header_nav__sub {
  font-size: 14px;
  font-weight: 400;
  margin-left: 16px;
  opacity: 0.5;
}


.user_search {
  width: 300px;
  padding: 6px 12px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  outline: none;
  background: white;

}


.user_wrapper {
  overflow: auto;
  margin-top: 20px;
}


.user_loader {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  max-height: 200px;

}


.user_row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 10px 25px;

  &:not(:last-of-type) {
    border-bottom: 1px solid #EEEEEE;
  }

}


.user_row_actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;

  .user_row_admin {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-left: 50px;
    gap: 8px;

    span {
      font-size: 14px;
      color: #898B99;
    }

    &_icon {
      width: 20px;
      cursor: pointer;
      color: #969FAD;

      &__disabled {
        cursor: default;
        opacity: 0.3;
      }

      &__sent {
        width: 20px;
        height: 18px;
        color: $success;
      }
    }
  }
}


.name {
  display: flex;
  align-items: center;
  gap: 10px;

  &_icon {
    width: 14px;
    height: 14px;
    color: $paperbox-blue;
  }
}

