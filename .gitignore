# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules/
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

.nyc_output
.now
.idea
.firebase
.firebaserc
.env*
.eslintcache
/.eslintcache
test-results

netlify.toml
# Local Netlify folder
.netlify

# Sentry Auth Token
.env.sentry-build-plugin
.history
