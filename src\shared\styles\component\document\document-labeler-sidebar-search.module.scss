@import "../../vars/_vars";


.container {
  position: relative;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  margin-top: 10px;
  //padding: 8px;
  //border-bottom: 1px solid #EEEEEE;
}


.dropzone_wrapper {
  position: absolute;
  z-index: -1;
  top: -4px;
  right: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 40px;
  pointer-events: none;
  background: white;
  //padding: 10px 20px;

  &__active {
    z-index: 99;
    pointer-events: all;
  }

}


.dropzone {
  z-index: 100;
  display: flex;
  align-items: center;
  //overflow: hidden;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: grabbing;
  //max-height: 75px;
  transition: all 0.2s ease;
  border: 1px dashed $paperbox-blue--fade;
  //min-height: 40px;
  border-radius: 5px;

}


.wrapper {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 0;

  &__disabled {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.7;
  }

  & + & {
    margin-top: 8px;
  }
}


.pills {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  height: auto;
  overflow: hidden;
  margin-top: 10px;
  padding: 10px 0 8px;
  border-top: 1px solid #EEEEEE;

  .pill {
    border-radius: 50px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    color: $font-color-black;
    background: #F5F5F5;
    border: 1px solid #E0E0E0;
    transition: all 0.2s ease;
    user-select: none;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &__clickable {
      cursor: pointer;

      &:hover {
        background: #E8F4FD;
        border-color: $paperbox-blue--fade;
        color: $paperbox-blue;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }
    }

    &__type {
      font-weight: 600;
      opacity: 0.8;
    }

    &__separator {
      margin: 0 4px;
      opacity: 0.6;
    }

    &__value {
      font-weight: 500;
    }
  }
}



.button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 34px;
  padding: 8px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
  color: $error;
  border: 1px solid $error;

  border-radius: 5px;

  &:hover {
    color: white;
    background: $error;
  }

  span {
    font-size: 14px;
  }

  svg {
    width: auto;
    height: 16px;
    margin-left: 10px;
    transform: translateY(1px);
  }
}


.bottom {
  position: relative;
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-height: 0;
  margin-top: 0;
  margin-bottom: 0;
  transition: margin 0.2s, max-height 0.2s ease-in-out;

  &__expanded {
    overflow: visible;
    height: 32px;
    max-height: 32px;
    margin-top: 11px;
    margin-bottom: 3px;
  }
}


.bottom_buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  gap: 8px;
}


.hidden_measure {
  font-family: $base-font;
  font-size: 14px;
  position: fixed;
  top: -150px;
  left: -100px;
}


.tool {
  font-size: 14px;
  position: absolute;
  z-index: 100;
  display: flex;
  justify-content: center;
  transition: opacity 0.2s, transform 0.2s;
  transform-origin: top center;

  div {
    z-index: 2;
    left: 0;
    display: flex;
    width: fit-content;
    padding: 8px 16px;
    cursor: pointer;
    border: 1px solid #EEEEEE;
    border-radius: 5px;
    background: white;
    box-shadow: $shadow-light;
    margin-inline: auto;
  }
}


.search_button {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 30px;
  padding: 3px 10px;
  transition: 0.3s ease-in-out;
  transition-property: background-color, color;
  color: #969FAD;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: white;
  gap: 5px;

  svg {
    width: 16px;
    height: 16px;
    color: #969FAD;
  }

  &:hover:not(&:disabled) {
    background-color: rgba(#EEEEEE, 0.4);;

  }

  &:disabled {
    cursor: default;

  }
}


.add_query {
  font-size: 14px;
  width: auto;
  margin-top: 6px;
  margin-left: 8px;
  cursor: pointer;
  color: $medium-dark-gray;
}


.no_results {
  font-size: 14px;
  margin-top: 8px;
  color: $medium-dark-gray;
  margin-inline: auto;
}


.dropdown_wrapper {
  flex-shrink: 0;
  width: 150px;
  height: 100%;

}


.dropdown_wrapper_right {
  position: relative;
  width: 100%;
  height: 35px;
}


.dropdown {
  border-radius: 5px 0 0 5px !important;

  &_right {
    height: 35px;
    margin-top: 0;
    margin-bottom: 0;
    border-left: none !important;
    border-radius: 0 5px 5px 0 !important;
  }
}


.remove {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  max-width: 32px;
  height: 34px;
  margin-left: 4px;
  cursor: pointer;
  transition: max-width 0.3s, margin-left 0.3s;
  transition-delay: 0.1s;
  opacity: 0.5;
  will-change: max-width, margin;

  &:hover {
    opacity: 1;
  }

  svg {
    width: 16px;
  }
}


.search_wrapper {
  position: relative;
  width: 100%;

  .search_icons {
    position: absolute;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 100%;
      padding: 5px;
      cursor: pointer;
      transition: background-color 0.2s;
      color: #969FAD;

      &__exact {
        width: 29px;
        padding: 7px;

        &__active {
          color: $paperbox-blue
        }
      }

      &:hover {
        background-color: rgba(#EEEEEE, 0.4);
      }
    }
  }

  .search_icon {
    position: absolute;
    right: -2px;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 100%;
    padding: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    color: #969FAD;
    border-radius: 5px;

    &:hover {
      background-color: rgba(#EEEEEE, 0.4);
    }
  }

  .search {
    font-family: $base-font;
    font-size: 14px;
    width: 100%;
    height: 35px;
    padding-right: 26px;
    padding-left: 12px;
    border: 1px solid rgba(94, 122, 161, 0.2);
    border-left: none;
    border-radius: 0 5px 5px 0;
    outline: none;
    background: white;

    &::placeholder {
      color: #969FAD;
    }
  }
}
