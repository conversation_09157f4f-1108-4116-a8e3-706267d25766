@import "@shared/styles/vars/_vars";


.notification {
  $self: &;

  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  transition: all 300ms ease-in-out;
  transform: translateY(-100%);
  opacity: 0;
  will-change: transform, opacity;
  pointer-events: none;

  &__visible {
    animation: notificationBarIn 0.3s forwards;
  }

  &__hidden {
    animation: notificationBarOut 0.3s forwards;
  }

  &__container {
    max-width: 48rem;
    margin: 1rem auto;
    padding: 0 1rem;
  }

  &__content {
    padding: 0.75rem 1rem;
    text-align: center;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  // Variants
  &__success {
    #{$self}__content {
      color: #166534;
      border-color: #86efac;
      background-color: #f0fdf4;
    }
  }

  &__error {
    #{$self}__content {
      color: #991b1b;
      border-color: #fecaca;
      background-color: #fef2f2;
    }
  }

  &__warning {
    #{$self}__content {
      color: #92400e;
      border-color: #fef3c7;
      background-color: #fffbeb;
    }
  }
}


.button {
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 150ms ease-in-out;
  color: white;
  border: none;
  border-radius: 0.5rem;
  background-color: $paperbox-blue;

  &:hover {
    background-color: $paperbox-blue;
  }

  &:focus {
    outline: 2px solid #60a5fa;
    outline-offset: 2px;
  }
}


@keyframes notificationBarIn {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }

  100% {
    transform: translateY(0);
    opacity: 1;
  }
}


@keyframes notificationBarOut {
  0% {
    transform: translateY(0);
    opacity: 1;
  }

  100% {
    transform: translateY(-100%);
    opacity: 0;
  }
}