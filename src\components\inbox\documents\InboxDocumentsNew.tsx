import InboxTableNew from '@components/inbox/table/InboxTableNew.tsx';
import InboxTags from '@components/inbox/table/components/InboxTags.tsx';
import InboxUpload from '@components/inbox/table/components/InboxUpload.tsx';
import { useGetActiveInboxQuery, useGetDoctypesQuery } from '@shared/helpers/rtk-query/firestoreApi.ts';
import useDocumentList from '@shared/hooks/useDocumentList.ts';
import useFilterSync from '@shared/hooks/useFilterSync.ts';
import { resetFilters, updateFilters } from '@shared/store/documentListSlice.ts';
import s from '@shared/styles/component/inbox/inbox-content.module.scss';
import { useDispatch, useSelector } from '@src/shared/store/store.ts';
import React, { useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useParams, useSearchParams } from 'react-router';
import HeaderProfile from '../../header/profile/HeaderProfile.tsx';
import InboxTableFooter from '../table/InboxTableFooter.tsx';
import usePagination from '../table/hooks/usePagination.tsx';

interface Props {}

const InboxDocuments: React.FC<Props> = () => {
  const { inboxId } = useParams();
  const { pageCount } = usePagination();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();
  const location = useLocation();

  const filters = useSelector((state) => state.documentList.filters);
  const { documents, isLoading } = useDocumentList(inboxId);
  const { all: docTypes } = useGetDoctypesQuery({ inboxId }).data ?? {};
  const { data: activeInbox } = useGetActiveInboxQuery({ inboxId }, { skip: !inboxId });

  // Enable individual filter sync for this component
  useFilterSync({ autoSync: true });

  // Track previous location to detect view switching
  const previousLocation = useRef<string | null>(null);
  const isFirstMount = useRef(true);

  // Reset filters when switching from historical to normal view, but preserve direct navigation
  useEffect(() => {
    const currentPath = location.pathname;
    const isHistoricalView = currentPath.includes('/historical');
    const isNormalView = !isHistoricalView && currentPath.includes(`/inbox/${inboxId}`) && !currentPath.includes('/dashboard');

    if (isFirstMount.current) {
      isFirstMount.current = false;
      previousLocation.current = currentPath;

      // Check if there are any filter-related URL parameters
      const hasFilterParams = Array.from(searchParams.keys()).some(key =>
        ['docTypeId', 'subTypeId', 'searchTerm', 'activeTagId', 'sortBy', 'actorId',
         'isSortDescending', 'action', 'actorIdFilterMode', 'approvalChecks'].includes(key)
      );

      // Only reset if there are no URL parameters (clean navigation) and we're on normal view
      if (!hasFilterParams && isNormalView) {
        dispatch(resetFilters());
        setSearchParams(new URLSearchParams(), { replace: true });
      }
    } else {
      // Detect view switching: coming from historical to normal
      const wasHistorical = previousLocation.current?.includes('/historical');
      const isNowNormal = isNormalView;

      if (wasHistorical && isNowNormal) {
        // Reset filters when switching from historical to normal view
        dispatch(resetFilters());
        setSearchParams(new URLSearchParams(), { replace: true });
      }

      previousLocation.current = currentPath;
    }
  }, [location.pathname, inboxId, dispatch, searchParams, setSearchParams]);

  const title = useMemo(() => {
    const { docTypeId, subTypeId } = filters;
    const selectedDocType = docTypes?.find((docType) => docType.id === docTypeId);
    if (!selectedDocType) return t('home:allDocuments');
    let documentTitle = selectedDocType.name;

    if (subTypeId) {
      const selectedSubType = selectedDocType.subtypes?.find((subType) => subType.id === subTypeId);
      documentTitle += ` / ${selectedSubType?.name}`;
    }

    return documentTitle;
  }, [filters, docTypes, t]);

  const enabledColumns = {
    ...activeInbox?.settings.tableColumns,
    locker: true,
    actor: false,
    actionDate: false,
    approvalChecks: true,
    actionType: false,
    initialApprovalChecks: false,
  };

  return (
    <div className={s.container} style={{ marginBottom: 0 }}>
      <div className={s.header}>
        <h3 className={s.title} data-testid="inbox-title">
          {title}
        </h3>
        <HeaderProfile />
      </div>
      <div className={s.sub_header}>
        <InboxTags
          activeTagId={filters?.activeTagId}
          setActiveTagId={(tagId) => {
            dispatch(updateFilters({ activeTagId: tagId }));
          }}
        />

        <div className={s.info}>
          <InboxUpload />
        </div>
      </div>
      <InboxTableNew
        enabledColumns={enabledColumns}
        documents={documents}
        loading={isLoading}
        pageCount={pageCount}
        goToDocument={(docId) => {
          navigate(`/inbox/${inboxId}/documents/${docId}`);
        }}
      />
      <InboxTableFooter />
    </div>
  );
};

export default InboxDocuments;
