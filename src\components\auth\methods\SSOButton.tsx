import { loginWithPopup } from '@components/auth/helpers/helpers';
import { useDispatch, useSelector } from '@shared/store/store';
import s from '@shared/styles/component/auth/auth.module.scss';
import { ReactComponent as KeyIcon } from '@svg/key-alt-icon.svg';
import clsx from 'clsx';
import { SAMLAuthProvider, OAuthProvider } from 'firebase/auth';
import React from 'react';
import { useNavigate } from 'react-router';

interface Props {
  handleError: (err) => void;
  text: string;
  type: string;
}

const SSOButton: React.FC<Props> = ({ text, handleError, type }) => {
  const allowedDomains = useSelector((state) => state.tenant.details.settings?.allowedDomains);
  const providers = useSelector((state) => state.tenant.providers);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const handleSSO = () => {
    const providerId = providers
      .filter((item) => item.type === type)[0]
      .name.split(`${type}/`)[1];
    let provider;
    if (type === 'inboundSamlConfigs') {
      provider = new SAMLAuthProvider(providerId);
    }
    if (type === 'oauthIdpConfigs') {
      provider = new OAuthProvider(providerId);
    }
    loginWithPopup(provider, dispatch, allowedDomains, navigate, handleError);
  };

  return (
    <button onClick={handleSSO} className={clsx(s.button, s.button_main, s.button_sso)}>
      <div className={s.button_main__left}>
        <KeyIcon className={s.icon} />
      </div>
      <div className={s.button_main__right}>{text}</div>
    </button>
  );
};

export default SSOButton;
