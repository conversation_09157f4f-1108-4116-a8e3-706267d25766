import { sleep } from '@shared/helpers/helpers';
import useOutsideClick from '@shared/hooks/useOutsideClick';
import { UrlParams } from '@shared/models/generic';
import { UserAccount } from '@shared/models/user';
import { useDispatch, useSelector } from '@shared/store/store';
import { resetPassword } from '@shared/store/userSlice';
import s from '@shared/styles/component/header/header-profile.module.scss';
import avatar from '@svg/avatar.svg';
import { ReactComponent as CheckmarkIcon } from '@svg/checkmark-icon.svg';
import { ReactComponent as DropdownArrowDown } from '@svg/chevron-down.svg';
import { ReactComponent as KeyIcon } from '@svg/key.svg';
import { ReactComponent as LanguageIcon } from '@svg/language-icon.svg';
import { ReactComponent as SettingsIcon } from '@svg/settings.svg';
import { ReactComponent as SignOutIcon } from '@svg/sign-out-icon.svg';
import clsx from 'clsx';
import { User } from 'firebase/auth';
import React, { useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { NavLink, useParams } from 'react-router';
import { CSSTransition } from 'react-transition-group';
import HeaderLanguageSwitch from '../language/HeaderLanguageSwitch.tsx';

interface Props {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  user: UserAccount;
  isLight?: boolean;
  handleSignOut?: () => void;
  firebaseUser: User;
  hasName?: boolean;
  position?: 'top' | 'bottom';
}

const HeaderProfileDropdown: React.FC<Props> = ({
  isOpen,
  setIsOpen,
  user,
  isLight = false,
  position = 'bottom',
  handleSignOut,
  firebaseUser,
  hasName = true,
}) => {
  const providers = useSelector((state) => state.tenant.providers);

  const { inboxId }: UrlParams = useParams();
  const ref = useRef(null);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  useOutsideClick(ref, () => setIsOpen(false));

  const passwordEnabled = useMemo(() => !!providers.find((e) => e.type === 'EmailPassword'), [providers]);
  const [passwordResetSent, setPasswordResetSent] = useState(false);

  return (
    <>
      <div data-testid="header-dropdown" ref={ref} style={{ width: '100%' }}>
        <div
          className={s.dropdown__select}
          onClick={() => {
            setIsOpen(!isOpen);
          }}
        >
          {hasName && user.email && (
            <>
              <div data-testid="header-dropdown-user" className={clsx(s.name, { [s.name_light]: isLight })}>
                {user.email}
              </div>
              <DropdownArrowDown
                className={clsx(s.dropdown__arrow, {
                  [s.dropdown__arrow_active]: isOpen,
                })}
              />
            </>
          )}
        </div>
        <CSSTransition
          unmountOnExit
          mountOnEnter
          classNames={'profile-dropdown-anim'}
          timeout={250}
          in={isOpen}
        >
          <div
            className={clsx(s.dropdown, { [s.dropdown__top]: position === 'top' })}
            data-testid={'dropdown-item-list'}
          >
            <div className={s.header}>
              <img
                width={32}
                height={32}
                className={s.profile}
                src={firebaseUser?.photoURL || avatar}
                alt="avatar"
                onError={(e) => {
                  const img = e.target as HTMLImageElement;
                  img.src = avatar;
                }}
              />
              <div className={s.dropdown_name}>{user.email}</div>
            </div>

            <div className={s.nav}>
              {user.isAdmin && (
                <NavLink
                  data-testid="dropdown-admin"
                  to={`/admin/inboxes/${inboxId}/settings`}
                  className={s.nav_item}
                >
                  <SettingsIcon className={s.nav_icon} />
                  <div className={s.nav_text}>{t('home:userDropdown.adminPanel')}</div>
                </NavLink>
              )}
              <div className={clsx(s.nav_item, s.nav_item__no_hover)}>
                <LanguageIcon className={s.nav_icon} />
                <div data-testid={'sign-out'} className={s.nav_text}>
                  {t('home:userDropdown.language')}
                </div>
                <HeaderLanguageSwitch />
              </div>
              <div className={s.divider} />
              {passwordEnabled && (
                <div
                  data-testid="dropdown-pw-reset"
                  className={clsx(s.nav_item, { [s.nav_item__success]: passwordResetSent })}
                  onClick={() => {
                    setPasswordResetSent(true);
                    dispatch(resetPassword(user.email)).then(() => {
                      sleep(5000).then(() => {
                        setPasswordResetSent(false);
                      });
                    });
                  }}
                >
                  {passwordResetSent ? (
                    <>
                      <CheckmarkIcon className={clsx(s.nav_icon)} />
                      <div className={s.nav_text}>{t('home:userDropdown.passwordSent')}</div>
                    </>
                  ) : (
                    <>
                      <KeyIcon className={s.nav_icon} />
                      <div className={s.nav_text}>{t('home:userDropdown.password')}</div>
                    </>
                  )}
                </div>
              )}

              <div
                className={s.nav_item}
                onClick={(e) => {
                  e.preventDefault();
                  handleSignOut();
                }}
              >
                <SignOutIcon className={s.nav_icon} />
                <div data-testid={'sign-out'} className={s.nav_text}>
                  {t('home:userDropdown.signOut')}
                </div>
              </div>
            </div>
            <div className={s.version}>{import.meta.env.VITE_PAPERBOX_RELEASE}</div>
          </div>
        </CSSTransition>
      </div>
    </>
  );
};

export default HeaderProfileDropdown;
