name-template: 'v$RESOLVED_VERSION'
tag-template: 'v$RESOLVED_VERSION'
template: |
  # What's Changed
  $CHANGES

categories:
  - title: '🚀 Features'
    labels:
      - 'Feature'
  - title: '🐛 Bug Fixes'
    labels:
      - 'Bugfix'
  - title: '🔥 Hot Fixes'
    labels:
      - 'Hotfix'
  - title: '⚙️ Chores'
    labels:
      - 'Chores'


change-template: |
  - $TITLE @$AUTHOR (#$NUMBER)  
  <dl>  
  <dd>  
  <blockquote>  
  <details>  
  <summary>Show description</summary>
  $BODY  
  </details>  
  </blockquote>  
  </dd>  
  </dl>
change-title-escapes: '\<*_&' # You can add # and @ to disable mentions, and add ` to disable code blocks.
version-resolver:
  major:
    labels:
      - 'Breaking'
  minor:
    labels:
      - 'Feature'
  patch:
    labels:
      - 'Bugfix'
      - 'Hotfix'
      - 'Patch'
  default: patch

autolabeler:
  - label: 'Feature'
    branch:
      - '/feature\/.+/'
    title:
      - '/feature/i'
  - label: 'Chore'
    branch:
      - '/chore\/.+/'
    title:
      - '/chore/i'      
  - label: 'Bugfix'
    branch:
      - '/bugfix\/.+/'
    title:
      - '/bugfix/i'
  - label: 'Hotfix'
    branch:
      - '/hotfix\/.+/'
    title:
      - '/hotfix/i'