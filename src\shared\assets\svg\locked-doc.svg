<svg width="538" height="702" viewBox="0 0 538 702" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_d_1769_30)">
        <rect x="502.875" y="651.319" width="466.875" height="601.319" transform="rotate(-180 502.875 651.319)" fill="white"/>
        <g opacity="0.4">
            <rect x="457.667" y="592.034" width="238.369" height="41.2878" transform="rotate(-180 457.667 592.034)" fill="#98B3D9"/>
            <rect x="457.667" y="366.54" width="304.948" height="41.2878" transform="rotate(-180 457.667 366.54)" fill="#98B3D9"/>
            <rect x="457.668" y="291.373" width="238.369" height="41.2878" transform="rotate(-180 457.668 291.373)" fill="#98B3D9"/>
            <rect x="457.667" y="224.679" width="143.844" height="41.2878" transform="rotate(-180 457.667 224.679)" fill="#98B3D9"/>
            <rect x="457.667" y="516.87" width="143.844" height="41.2878" transform="rotate(-180 457.667 516.87)" fill="#98B3D9"/>
            <rect x="457.668" y="441.704" width="51.7837" height="41.2878" transform="rotate(-180 457.668 441.704)" fill="#98B3D9"/>
            <rect x="457.667" y="149.515" width="51.7837" height="41.2878" transform="rotate(-180 457.667 149.515)" fill="#98B3D9"/>
            <rect x="380.403" y="441.703" width="135.624" height="41.2878" transform="rotate(-180 380.403 441.703)" fill="#98B3D9"/>
            <rect x="380.403" y="149.515" width="135.624" height="41.2878" transform="rotate(-180 380.403 149.515)" fill="#98B3D9"/>
            <rect x="219.299" y="441.703" width="138.912" height="41.2878" transform="rotate(-180 219.299 441.703)" fill="#98B3D9"/>
            <rect x="219.298" y="149.514" width="138.912" height="41.2878" transform="rotate(-180 219.298 149.514)" fill="#98B3D9"/>
            <rect x="193.817" y="592.035" width="113.431" height="41.2878" transform="rotate(-180 193.817 592.035)" fill="#98B3D9"/>
            <rect x="128.06" y="366.54" width="47.6739" height="41.2878" transform="rotate(-180 128.06 366.54)" fill="#98B3D9"/>
            <rect x="193.817" y="291.374" width="113.431" height="41.2878" transform="rotate(-180 193.817 291.374)" fill="#98B3D9"/>
            <rect x="288.343" y="224.679" width="207.957" height="41.2878" transform="rotate(-180 288.343 224.679)" fill="#98B3D9"/>
            <rect x="288.344" y="516.868" width="207.957" height="41.2878" transform="rotate(-180 288.344 516.868)" fill="#98B3D9"/>
        </g>
    </g>
    <path opacity="0.5" fill-rule="evenodd" clip-rule="evenodd" d="M218.286 340.312V319.938C218.286 306.428 223.629 293.472 233.14 283.919C242.65 274.367 255.55 269 269 269C282.45 269 295.35 274.367 304.86 283.919C314.371 293.472 319.714 306.428 319.714 319.938V340.312C325.094 340.312 330.254 342.459 334.058 346.28C337.863 350.101 340 355.284 340 360.688V411.625C340 417.029 337.863 422.211 334.058 426.032C330.254 429.853 325.094 432 319.714 432H218.286C212.906 432 207.746 429.853 203.942 426.032C200.137 422.211 198 417.029 198 411.625V360.688C198 355.284 200.137 350.101 203.942 346.28C207.746 342.459 212.906 340.312 218.286 340.312V340.312ZM299.429 319.938V340.312H238.571V319.938C238.571 311.832 241.777 304.058 247.484 298.327C253.19 292.595 260.93 289.375 269 289.375C277.07 289.375 284.81 292.595 290.516 298.327C296.223 304.058 299.429 311.832 299.429 319.938V319.938Z" fill="#0085FF"/>
    <defs>
        <filter id="filter0_d_1769_30" x="8.05325" y="25.341" width="522.769" height="657.213" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="3.28785"/>
            <feGaussianBlur stdDeviation="13.9734"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0.054902 0 0 0 0 0.0901961 0 0 0 0 0.141176 0 0 0 0.2 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1769_30"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1769_30" result="shape"/>
        </filter>
    </defs>
</svg>
