// export type ToolType = 'default' | 'search';
import { EntityTypeType } from '@shared/models/inbox.ts';

export type ToolType = 'default' | 'key-value' | 'rectangle' | 'table';

// Map that maps ToolType to EntityTypeType
export const entityTypeToToolTypeMap: Record<EntityTypeType, ToolType> = {
  complex: 'default',
  boolean: 'rectangle',
  options: 'default',
  text: 'default',
  image: 'rectangle',
  table: 'table',
  date: 'default',
};
