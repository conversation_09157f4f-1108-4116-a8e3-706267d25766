import { IDocumentEnriched } from '@shared/helpers/converters/document.ts';
import { useHistoricalDocuments } from '@shared/helpers/firestore/firestoreRoutes.ts';
import { enrichDocumentWithTypes } from '@shared/helpers/newHelpers.ts';
import { useGetHistoricalDocumentIdsQuery } from '@shared/helpers/rtk-query/analyticsApi.ts';
import {
  useGetDoctypesQuery,
  useGetFieldtypesQuery,
  useGetTagtypesQuery,
} from '@shared/helpers/rtk-query/firestoreApi.ts';
import useConsole from '@shared/hooks/useConsole.tsx';
import { RootState } from '@shared/store/store.ts';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useDeepCompareEffect } from 'use-deep-compare';

const useHistoricalDocumentList = (inboxId: string) => {
  const filters = useSelector((state: RootState) => state.documentList.filters);
  const [idsLoading, setIdsLoading] = useState(false);
  const [stableLoading, setStableLoading] = useState(false);
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null);

  useDeepCompareEffect(() => {
    setIdsLoading(true);
  }, [filters]);

  const { data: idData, error } = useGetHistoricalDocumentIdsQuery(
    { inboxId, filters },
    { skip: !inboxId, pollingInterval: 2000 },
  );
  const { ids } = idData || {};

  useEffect(() => {
    if (ids) setIdsLoading(false);
  }, [ids]);

  const { documents: docs, isLoading } = useHistoricalDocuments(
    ids,
    filters,
    ids == null || ids.length === 0,
  );

  const { all: docTypes } = useGetDoctypesQuery({ inboxId }).data ?? {};
  const { data: fieldTypes } = useGetFieldtypesQuery({ inboxId }, { skip: !inboxId });
  const { data: tagTypes } = useGetTagtypesQuery({ inboxId }, { skip: !inboxId });

  const [cachedDocuments, setCachedDocuments] = useState<IDocumentEnriched[]>([]);

  useEffect(() => {
    if (docs) {
      console.log('DOCS', docs);
      const enriched = docs.map((d) =>
        enrichDocumentWithTypes(d, docTypes ?? [], fieldTypes ?? [], tagTypes ?? []),
      );
      setCachedDocuments(enriched);
    }
  }, [docs, docTypes, fieldTypes, tagTypes]);

  // Calculate the raw loading state
  const rawLoading = idsLoading || isLoading;

  // Update stable loading with debounce when raw loading turns off
  useEffect(() => {
    if (rawLoading) {
      // Clear timer if already set
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
        loadingTimerRef.current = null;
      }
      // Set stable loading immediately to true
      setStableLoading(true);
    } else {
      // Delay turning off stable loading by 200ms
      loadingTimerRef.current = setTimeout(() => {
        setStableLoading(false);
        loadingTimerRef.current = null;
      }, 200);
    }

    return () => {
      if (loadingTimerRef.current) {
        clearTimeout(loadingTimerRef.current);
        loadingTimerRef.current = null;
      }
    };
  }, [rawLoading]);

  useConsole(stableLoading, 'stableLoading');

  return {
    documents: cachedDocuments ?? [],
    isLoading: stableLoading,
    error,
  };
};

export default useHistoricalDocumentList;
