import s from '@components/document/labeler/sidebar/sidebar.module.scss';
import { parseDateString } from '@components/document/labeler/viewer/helpers/helpers.ts';
import Checkbox from '@components/shared/checkbox/Checkbox.tsx';
import StyledSelect from '@components/shared/dropdown/StyledSelect.tsx';
import {
  IDocumentEnrichedEntity,
  IDocumentEntity,
  IDocumentEntityComplexValue,
} from '@shared/helpers/converters/document.ts';
import { useKeyPress, uuid4hex } from '@shared/helpers/helpers.ts';
import { getEntityCropThumb, listToDropdownOptions } from '@shared/helpers/newHelpers.ts';
import useConsole from '@shared/hooks/useConsole.tsx';
import { useDocumentOperations } from '@shared/hooks/useDocumentOperations';
import { ActiveEntityPair } from '@shared/models/document.ts';
import { UrlParams } from '@shared/models/generic.ts';
import { entityTypeToToolTypeMap } from '@shared/models/labeler.ts';
import { selectFailedCheckTargets, selectIsInteractive } from '@shared/store/documentSlice';
import labelerSlice from '@shared/store/labelerSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store.ts';
import Tooltip from '@src/components/shared/tooltip/Tooltip';
import { ReactComponent as CancelIcon } from '@svg/cancel.svg';
import { ReactComponent as CheckMarkIcon } from '@svg/checkmark-icon.svg';
import { ReactComponent as ComplexIcon } from '@svg/complex-icon.svg';
import { ReactComponent as CrossIcon } from '@svg/cross-icon.svg';
import { ReactComponent as LinkIcon } from '@svg/link-icon.svg';
import { ReactComponent as PaperboxIcon } from '@svg/paperbox-logo-small.svg';
import { RaceBy } from '@uiball/loaders';
import clsx from 'clsx';
import React, { useEffect, useMemo, useState, FormEvent } from 'react';
import { useParams } from 'react-router';
import { DatePicker } from 'rsuite';

/* ---------- Helper Functions ---------- */

/**
 * Handles both placeholder and regular entity edits.
 */
const handleFieldEdit = ({
  entity,
  parent,
  editEntity,
  addEntity,
  isPlaceholder,
  creatingEntity,
  value,
}: {
  entity: IDocumentEnrichedEntity;
  parent?: IDocumentEnrichedEntity;
  editEntity: (details: ActiveEntityPair, data: Partial<IDocumentEnrichedEntity>) => void;
  addEntity: (entity: IDocumentEntity) => void;
  isPlaceholder?: boolean;
  creatingEntity?: IDocumentEnrichedEntity;
  value: any;
}) => {
  if (isPlaceholder) {
    const newEntityId = uuid4hex(); // Generate a unique ID for the new entity
    // Create a new entity from placeholder data.
    const dataToAdd: IDocumentEntity = {
      id: newEntityId, // Use the new unique ID
      type: entity.type,
      value,
      rawValue: creatingEntity?.value ?? value,
      valueLocations: creatingEntity?.valueLocations || [],
      pageNo: creatingEntity?.pageNo,
      source: 'user',
      confidence: 1,
    };

    if (parent) {
      // If part of a complex field, update parent's complex value.
      const parentValue = parent.value as IDocumentEntityComplexValue;
      const childFieldName = entity.id.replace('placeholder-', ''); // This is the key for the complex object property
      const data = {
        ...parent,
        id: uuid4hex(), // Parent also gets a new unique ID for this change instance
        value: {
          ...parentValue,
          complex: {
            ...parentValue.complex,
            [childFieldName]: dataToAdd, // dataToAdd now has a unique .id
          },
        },
      };
      addEntity(data);
    } else {
      addEntity(dataToAdd);
    }
    return;
  }

  // Regular editing.
  const { entityId, childId } = parent
    ? { entityId: parent.id, childId: entity.id }
    : { entityId: entity.id, childId: undefined };
  if (creatingEntity) {
    editEntity(
      { entityId, childId, locationIndex: 0 },
      { value, valueLocations: creatingEntity.valueLocations, pageNo: creatingEntity.pageNo },
    );
  } else {
    editEntity({ entityId, childId, locationIndex: 0 }, { value });
  }
};

/* ---------- Sub-Components ---------- */

// Delete button component.
const DeleteButton: React.FC<{ onClick: (e: React.MouseEvent) => void }> = ({ onClick }) => (
  <button onClick={onClick}>
    <CrossIcon style={{ color: '#FF5555' }} />
  </button>
);

// Empty field for null/empty values.
const EmptyField: React.FC<{ handleEnableEditing: () => void }> = ({ handleEnableEditing }) => (
  <div className={s.row_item_right}>
    <span onClick={handleEnableEditing} className={s.value} style={{ width: '100%', fontWeight: 'bold' }}>
      + Click to add
    </span>
    <div />
  </div>
);

// Complex field display (badge only).
const ComplexField: React.FC<{
  entity: IDocumentEnrichedEntity;
  deleteEntity: () => void;
  isPlaceholder?: boolean;
  isEditingAllowed?: boolean;
}> = ({ entity, deleteEntity, isPlaceholder, isEditingAllowed }) => {
  const entityValue = useMemo(
    () => entity.value as { complex: Record<string, IDocumentEnrichedEntity> },
    [entity],
  );
  const completedFields = useMemo(
    () => Object.values(entityValue.complex).filter((e) => e.value !== null).length,
    [entityValue],
  );
  const entityTypes = useMemo(() => entity.typeDetails.complexDefinition?.entityTypes || [], [entity]);
  const totalFields = entityTypes.length;
  const failedFields = useMemo(() => {
    return entityTypes
      .filter((det) => det.mandatory)
      .reduce((acc, det) => {
        const match = Object.values(entityValue.complex).find((child) => child.typeDetails.id === det.id);
        return acc + (match?.value !== null ? 0 : 1);
      }, 0);
  }, [entityTypes, entityValue]);
  const badgeStatus = failedFields > 0 ? 'failed' : 'default';

  return (
    <div className={s.row_item_right}>
      <div className={s.row_badge} data-status={badgeStatus}>
        <ComplexIcon style={{ width: 14, height: 14 }} />
        <span>
          {completedFields} / {totalFields}
        </span>
      </div>
      <div className={s.actions}>
        {!isPlaceholder && isEditingAllowed && (
          <DeleteButton
            onClick={(e) => {
              e.stopPropagation();
              deleteEntity();
            }}
          />
        )}
      </div>
    </div>
  );
};

// Boolean field display.
const BooleanField: React.FC<{
  entity: IDocumentEnrichedEntity;
  parent?: IDocumentEnrichedEntity;
  editEntity: (details: ActiveEntityPair, data: Partial<IDocumentEnrichedEntity>) => void;
  deleteEntity: () => void;
  isEditingAllowed?: boolean;
}> = ({ entity, parent, editEntity, deleteEntity, isEditingAllowed }) => (
  <div className={s.row_item_right}>
    <Checkbox
      disabled={!isEditingAllowed}
      checked={entity.value as boolean}
      onClick={() => {
        const newValue = !entity.value;
        const { entityId, childId } = parent
          ? { entityId: parent.id, childId: entity.id }
          : { entityId: entity.id, childId: undefined };
        editEntity({ entityId, childId, locationIndex: 0 }, { value: newValue });
      }}
    />
    <div className={s.actions}>
      {isEditingAllowed && (
        <DeleteButton
          onClick={(e) => {
            e.stopPropagation();
            deleteEntity();
          }}
        />
      )}
    </div>
  </div>
);

// Text field display.
const TextField: React.FC<{
  entity: IDocumentEnrichedEntity;
  truncatedStringValue: string;
  handleEnableEditing: () => void;
  deleteEntity: () => void;
  isEditingAllowed?: boolean;
}> = ({ entity, truncatedStringValue, handleEnableEditing, deleteEntity, isEditingAllowed }) => (
  <div className={s.row_item_right}>
    {Array.isArray(entity.value) ? (
      <div className={s.value_list}>
        {entity.value.length > 2 ? (
          <>
            <div className={s.item}>
              <span className={s.value_text}>{entity.value[0]}</span>
            </div>

            <Tooltip
              content={(entity.value.slice(1) as string[]).map((v) => <div key={v}>{v}</div>)}
              position="right"
              lightTheme={true}
            >
              <div className={s.item}>
                <span className={s.value_text}>+{entity.value.length - 1} more</span>
              </div>
            </Tooltip>
          </>
        ) : (
          entity.value.map((v, index) => (
            <div className={s.item} key={v + index}>
              <span className={s.value_text}>{v}</span>
            </div>
          ))
        )}
      </div>
    ) : (
      <span
        onClick={handleEnableEditing}
        className={clsx(s.value, { [s.value__no_edit]: !isEditingAllowed })}
      >
        {truncatedStringValue}
      </span>
    )}

    <div className={s.actions}>
      {isEditingAllowed && (
        <DeleteButton
          onClick={(e) => {
            e.stopPropagation();
            deleteEntity();
          }}
        />
      )}
    </div>
  </div>
);

// Image field display.
const ImageField: React.FC<{
  entity: IDocumentEnrichedEntity;
  deleteEntity: () => void;
  isEditingAllowed?: boolean;
}> = ({ entity, deleteEntity, isEditingAllowed }) => {
  const [thumb, setThumb] = useState<string | null>(null);
  const { docId, inboxId }: UrlParams = useParams();
  const mainDocument = useSelector((state) => state.document.mainDocument);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!thumb && !isLoading) {
      (async () => {
        setIsLoading(true);
        const res = await getEntityCropThumb(docId, inboxId, entity, mainDocument);
        setThumb(res as string | null);
        setIsLoading(false);
      })();
    }
  }, [docId, inboxId, entity, mainDocument, thumb, isLoading]);

  return (
    <div className={s.row_item_right}>
      {thumb ? (
        <img className={s.image} src={thumb} alt="" />
      ) : (
        <div className={s.image}>
          <RaceBy size={100} color={'#0085FF'} lineWeight={5} />
        </div>
      )}
      <div className={s.actions}>
        {isEditingAllowed && (
          <DeleteButton
            onClick={(e) => {
              e.stopPropagation();
              deleteEntity();
            }}
          />
        )}
      </div>
    </div>
  );
};

// Image editing field.
const ImageEditField: React.FC<{
  entity: IDocumentEnrichedEntity;
  parent?: IDocumentEnrichedEntity;
  editEntity: (details: ActiveEntityPair, data: Partial<IDocumentEnrichedEntity>) => void;
  handleClearEditing: () => void;
  isPlaceholder?: boolean;
  addEntity: (entity: IDocumentEntity) => void;
  creatingEntity: IDocumentEnrichedEntity;
}> = ({ entity, parent, editEntity, handleClearEditing, isPlaceholder, addEntity, creatingEntity }) => {
  const [thumb, setThumb] = useState<string | null>(null);
  const { docId, inboxId }: UrlParams = useParams();
  const mainDocument = useSelector((state) => state.document.mainDocument);

  const submit = () => {
    handleFieldEdit({
      entity,
      parent,
      editEntity,
      addEntity,
      isPlaceholder,
      creatingEntity,
      value: creatingEntity.value,
    });
    handleClearEditing();
  };
  useKeyPress('Enter', submit);

  useEffect(() => {
    if (!thumb) {
      (async () => {
        const res = await getEntityCropThumb(docId, inboxId, creatingEntity, mainDocument);
        setThumb(res as string | null);
      })();
    }
  }, [docId, inboxId, creatingEntity, mainDocument, thumb]);

  return (
    <div className={s.row_item_right}>
      {thumb && <img className={clsx(s.image, s.image__small)} src={thumb} alt="" />}

      <div className={s.actions} style={{ width: 40, maxWidth: 40, minWidth: 40 }} />
      <div className={s.input_actions}>
        <button type="button" className={s.input_action} onClick={submit}>
          <CheckMarkIcon className={s.input_confirm} />
        </button>

        <button type="button" className={s.input_action} onClick={handleClearEditing}>
          <CancelIcon className={s.input_cancel} />
        </button>
      </div>
    </div>
  );
};

// Field editing fallback when no selection is available.
const LabelingEditField: React.FC<{ handleClearEditing: () => void }> = ({ handleClearEditing }) => {
  useKeyPress('Escape', handleClearEditing);
  return (
    <div className={s.row_item_right}>
      <span className={clsx(s.value, s.value__pulsing)} style={{ marginRight: 30, fontWeight: 'bold' }}>
        Awaiting document selection
      </span>
      <div className={s.input_actions}>
        <button type="button" className={s.input_action} onClick={handleClearEditing}>
          <CancelIcon className={s.input_cancel} />
        </button>
      </div>
    </div>
  );
};

// Boolean field editing.
// Updated so that it no longer auto-creates a new entity.
// Instead, it displays a checkbox with local state and a confirmation message,
// and uses handleFieldEdit() upon pressing "Enter".
const BooleanEditField: React.FC<{
  entity: IDocumentEnrichedEntity;
  parent?: IDocumentEnrichedEntity;
  editEntity: (details: ActiveEntityPair, data: Partial<IDocumentEnrichedEntity>) => void;
  handleClearEditing: () => void;
  isPlaceholder?: boolean;
  addEntity: (entity: IDocumentEntity) => void;
  creatingEntity: IDocumentEnrichedEntity;
}> = ({ entity, parent, editEntity, handleClearEditing, isPlaceholder, addEntity, creatingEntity }) => {
  const [boolValue, setBoolValue] = useState<boolean>(
    entity.value !== null ? (entity.value as boolean) : false,
  );
  const submit = () => {
    handleFieldEdit({
      entity,
      parent,
      editEntity,
      addEntity,
      isPlaceholder,
      creatingEntity,
      value: boolValue,
    });
    handleClearEditing();
  };
  useKeyPress('Enter', submit);
  return (
    <div className={s.row_item_right}>
      <Checkbox checked={boolValue} onClick={() => setBoolValue((prev) => !prev)} />

      <div className={s.actions} style={{ width: 40, maxWidth: 40, minWidth: 40 }} />
      <div className={s.input_actions}>
        <button type="button" className={s.input_action} onClick={submit}>
          <CheckMarkIcon className={s.input_confirm} />
        </button>
        <button type="button" className={s.input_action} onClick={handleClearEditing}>
          <CancelIcon className={s.input_cancel} />
        </button>
      </div>
    </div>
  );
};

// Text editing field.
const TextEditField: React.FC<{
  entity: IDocumentEnrichedEntity;
  editInputValue: string;
  setEditInputValue: (value: string) => void;
  handleClearEditing: () => void;
  parent?: IDocumentEnrichedEntity;
  editEntity: (details: ActiveEntityPair, data: Partial<IDocumentEnrichedEntity>) => void;
  addEntity: (entity: IDocumentEntity) => void;
  isPlaceholder?: boolean;
  creatingEntity?: IDocumentEnrichedEntity;
}> = ({
  entity,
  editInputValue,
  setEditInputValue,
  handleClearEditing,
  parent,
  editEntity,
  addEntity,
  isPlaceholder,
  creatingEntity,
}) => {
  const submit = () => {
    handleFieldEdit({
      entity,
      parent,
      editEntity,
      addEntity,
      isPlaceholder,
      creatingEntity,
      value: editInputValue,
    });
    handleClearEditing();
  };
  useKeyPress('Enter', submit);
  useKeyPress('Escape', handleClearEditing);

  return (
    <form
      onSubmit={(e: FormEvent) => {
        e.preventDefault();
        submit();
      }}
    >
      <input
        onChange={(e) => setEditInputValue(e.target.value)}
        autoFocus
        type="text"
        value={editInputValue}
        className={s.input}
      />
      <div className={s.input_actions}>
        <button type="button" className={s.input_action} onClick={handleClearEditing}>
          <CancelIcon className={s.input_cancel} />
        </button>
      </div>
    </form>
  );
};

// Options editing field.
const OptionsEditField: React.FC<{
  entity: IDocumentEnrichedEntity;
  parent?: IDocumentEnrichedEntity;
  editEntity: (details: ActiveEntityPair, data: Partial<IDocumentEnrichedEntity>) => void;
  handleClearEditing: () => void;
  isPlaceholder?: boolean;
  addEntity: (entity: IDocumentEntity) => void;
  creatingEntity: IDocumentEnrichedEntity;
}> = ({ entity, parent, editEntity, handleClearEditing, isPlaceholder, addEntity, creatingEntity }) => {
  const options = entity.typeDetails.options || [];
  const mapped = listToDropdownOptions(options, 'value', 'value');
  const selected = mapped.find((o) => o.value === entity.value) ?? mapped[0];
  useKeyPress('Escape', handleClearEditing);

  return (
    <>
      <StyledSelect
        openMenuOnFocus
        onChange={(value) => {
          handleFieldEdit({
            entity,
            parent,
            editEntity,
            addEntity,
            isPlaceholder,
            creatingEntity,
            value: value.label,
          });
          handleClearEditing();
        }}
        autoFocus
        onBlur={handleClearEditing}
        iconStyles={{ marginRight: 22 }}
        style={{
          minHeight: '40px',
          marginTop: '-10px',
          marginBottom: '-10px',
          transform: 'translateX(2px)',
          borderRadius: 0,
          outline: 'none',
          border: 'none',
          borderLeft: '1px solid #0085FF4C',
          boxShadow: 'none',
        }}
        options={mapped}
        value={selected}
      />
      <div className={s.input_actions}>
        <button className={s.input_action} onClick={handleClearEditing}>
          <CancelIcon className={s.input_cancel} />
        </button>
      </div>
    </>
  );
};

// Date editing field.
const DateEditField: React.FC<{
  entity: IDocumentEnrichedEntity;
  parent?: IDocumentEnrichedEntity;
  editEntity: (details: ActiveEntityPair, data: Partial<IDocumentEnrichedEntity>) => void;
  handleClearEditing: () => void;
  isPlaceholder?: boolean;
  addEntity: (entity: IDocumentEntity) => void;
  creatingEntity: IDocumentEnrichedEntity;
}> = ({ entity, parent, editEntity, handleClearEditing, isPlaceholder, addEntity, creatingEntity }) => {
  // Initialize with current date value or current date
  const initialDate = useMemo(() => {
    if (entity.value) {
      return typeof entity.value === 'string' ? parseDateString(entity.value) : (entity.value as any);
    }
    return creatingEntity?.value ? parseDateString(creatingEntity.value as string) : new Date();
  }, [entity.value, creatingEntity]);
  useConsole(initialDate, 'initialDate');

  const [selectedDate, setSelectedDate] = useState<Date>(initialDate);

  // Update selectedDate whenever entity.value or creatingEntity.value changes
  useEffect(() => {
    let newDate: Date;
    if (entity.value) {
      newDate = typeof entity.value === 'string' ? parseDateString(entity.value) : (entity.value as any);
    } else if (creatingEntity?.value) {
      newDate = parseDateString(creatingEntity.value as string);
    } else {
      newDate = new Date();
    }
    setSelectedDate(newDate);
  }, [entity.value, creatingEntity?.value]);

  const handleConfirm = () => {
    handleFieldEdit({
      entity,
      parent,
      editEntity,
      addEntity,
      isPlaceholder,
      creatingEntity,
      value: selectedDate.toISOString(),
    });
    handleClearEditing();
  };

  useKeyPress('Escape', handleClearEditing);
  useKeyPress('Enter', handleConfirm);

  return (
    <>
      <DatePicker
        placeholder="Select or enter a Date"
        placement={'right'}
        value={selectedDate}
        onChange={(date) => {
          if (date) {
            setSelectedDate(date);
          }
        }}
        className={s.input_date}
        oneTap
        autoFocus
        cleanable={false}
      />
      <div className={s.input_actions}>
        <button className={s.input_action} onClick={handleConfirm} title="Confirm">
          <CheckMarkIcon className={s.input_confirm} />
        </button>
        <button className={s.input_action} onClick={handleClearEditing} title="Cancel">
          <CancelIcon className={s.input_cancel} />
        </button>
      </div>
    </>
  );
};

/* ---------- Main Component ---------- */

interface Props {
  entity: IDocumentEnrichedEntity;
  parent?: IDocumentEnrichedEntity;
  isFailedCheckTarget?: boolean;
}

const DocumentLabelerSidebarField: React.FC<Props> = ({ entity, parent, isFailedCheckTarget = false }) => {
  // Get failed check targets from the store
  const failedCheckTargets = useSelector(selectFailedCheckTargets) || {};

  // Log when a field is highlighted due to being a target of a failed check
  useEffect(() => {
    if (isFailedCheckTarget) {
      console.log(
        `Field ${entity.typeDetails.name} (ID: ${entity.id}) is highlighted as a failed check target`,
      );
    }
  }, [isFailedCheckTarget, entity.id, entity.typeDetails.name]);
  const dispatch = useDispatch();
  // Get state from Redux
  const isInteractive = useSelector(selectIsInteractive);

  // Get operations from the hook
  const { editEntity, deleteEntity, addEntity } = useDocumentOperations();
  const activeEntityPair = useSelector((state) => state.labeler.activeEntityPair);
  const editingEntityPair = useSelector((state) => state.labeler.editingEntityPair);
  const creatingEntity = useSelector((state) => state.labeler.creatingEntity);
  const [editInputValue, setEditInputValue] = useState(typeof entity?.value === 'string' ? entity.value : '');

  // Helper to get proper entity identifiers.
  const getIdentifiers = () => {
    return parent ? { entityId: parent.id, childId: entity.id } : { entityId: entity.id, childId: undefined };
  };
  const isEditingAllowed = useMemo(() => {
    if (!isInteractive) return false;
    if (entity.source === 'masterdata') return false;
    return true;
  }, [isInteractive, entity.source]);

  const isPlaceholder = useMemo(() => entity.id?.startsWith('placeholder-'), [entity]);

  const isEditingCurrent = useMemo(() => {
    if (!editingEntityPair) return false;
    if (parent) {
      return editingEntityPair.entityId === parent.id && editingEntityPair.childId === entity.id;
    }
    return editingEntityPair.entityId === entity.id;
  }, [editingEntityPair, entity, parent]);

  // --- Modified isActive computation ---
  const isActive = useMemo(() => {
    if (isEditingCurrent) return true;
    if (!activeEntityPair) return false;
    if (!parent) {
      // Top-level entity: active if activeEntityPair.entityId matches.
      return activeEntityPair.entityId === entity.id;
    }
    // Child entity: active if activeEntityPair matches both parent's id and child's id.
    return activeEntityPair.entityId === parent.id && activeEntityPair.childId === entity.id;
  }, [isEditingCurrent, activeEntityPair, entity, parent]);
  // --- End modified isActive ---

  const isPending = useSelector((state) => {
    const { entityId, childId } = getIdentifiers();
    const key = childId ? `${entityId}:${childId}` : entityId;
    const key2 = key.replaceAll('pending-', '');
    return !!state.pendingOperations.operations[key2];
  });

  const entityType = entity.typeDetails.type;
  const isValueEmpty = useMemo(() => {
    if (entityType === 'complex') return false;
    if (isEditingCurrent && creatingEntity) return false;
    return entity.value === null || entity.value === undefined;
  }, [entity, entityType, isEditingCurrent, creatingEntity]);

  // For complex fields, compute errors by checking mandatory child fields.
  const hasComplexErrors = useMemo(() => {
    if (entityType !== 'complex' || isValueEmpty) return false;
    const complexDefinition = entity.typeDetails.complexDefinition;
    if (!complexDefinition || !complexDefinition.entityTypes) return false;
    const mandatoryTypes = complexDefinition.entityTypes.filter((det) => det.mandatory);
    const complexEntities = (entity.value as { complex: Record<string, IDocumentEnrichedEntity> }).complex;
    return mandatoryTypes.some((det) => {
      const match = Object.values(complexEntities).find((child) => child.typeDetails.id === det.id);
      return match?.value == null;
    });
  }, [entity, entityType, isValueEmpty]);

  const isFailed = useMemo(() => {
    if (entityType === 'complex') {
      return isValueEmpty || hasComplexErrors;
    }
    if (parent) {
      const parentComplexDef = parent.typeDetails?.complexDefinition;
      if (parentComplexDef?.entityTypes) {
        const entityTypeDef = parentComplexDef.entityTypes.find((et) => et.id === entity.typeDetails.id);
        return entityTypeDef?.mandatory === true && isValueEmpty;
      }
      return false;
    }
    if (isPlaceholder) {
      return entity.isMandatory ? isValueEmpty : false;
    }
    return isValueEmpty;
  }, [entityType, isValueEmpty, hasComplexErrors, parent, entity.typeDetails.id, isPlaceholder]);

  // Handlers
  const handleEnableEditing = () => {
    if (!isEditingAllowed) return;
    if (creatingEntity && entity.value === null) setEditInputValue(creatingEntity.value as string);
    const { entityId, childId } = getIdentifiers();
    dispatch(labelerSlice.actions.setEditingEntityPair({ entityId, childId, locationIndex: 0 }));
    if (entity.typeDetails.type === 'boolean' && entity.value === null) {
      // For boolean fields, we now wait for confirmation via "Enter".
      // Hence, we do not auto-create the entity here.
    }
  };

  const handleSetActive = () => {
    const { entityId, childId } = getIdentifiers();
    dispatch(labelerSlice.actions.setActiveEntityPair({ entityId, childId, locationIndex: 0 }));
  };

  const handleClearEditing = () => {
    dispatch(labelerSlice.actions.setActiveEntityPair(editingEntityPair));
    dispatch(labelerSlice.actions.setEditingEntityPair(undefined));
    dispatch(labelerSlice.actions.setCreatingEntity(null));
  };

  const handleDelete = () => {
    const { entityId, childId } = getIdentifiers();
    if (parent) {
      deleteEntity({ entityId, childId, locationIndex: 0 });
    } else {
      deleteEntity({ entityId, locationIndex: 0 });
    }
  };

  useEffect(() => {
    if (isEditingCurrent && creatingEntity) {
      setEditInputValue(creatingEntity.value as string);
    }
  }, [isEditingCurrent, creatingEntity]);

  useEffect(() => {
    if (isEditingCurrent && entityType !== 'complex') {
      const tool = entityTypeToToolTypeMap[entityType];
      dispatch(labelerSlice.actions.setActiveTool(tool));
    }
  }, [entityType, isEditingCurrent, dispatch]);

  const stringValue = useMemo(() => {
    const type = entity.typeDetails;
    if (!type || isValueEmpty) return '';
    const ent = entity.value !== null ? entity : creatingEntity;
    switch (type.type) {
      case 'boolean':
        return '';
      case 'text':
      case 'options':
        return ent.value as string;
      case 'date':
        return typeof ent.value === 'string'
          ? new Date(ent.value).toLocaleDateString()
          : (ent.value as any)?.toLocaleDateString();
      case 'complex':
        return 'FALLBACK COMPLEX';
      case 'table':
        return 'FALLBACK TABLE';
      default:
        return ent.value as string;
    }
  }, [entity, isValueEmpty, creatingEntity]);

  const truncatedStringValue = useMemo(
    () => (stringValue.length > 55 ? `${stringValue.slice(0, 55)}...` : stringValue),
    [stringValue],
  );

  const renderSourceIcon = useMemo(() => {
    if (entity.source === 'user') return null;
    if (entity.source === 'masterdata') return <LinkIcon style={{ height: 11, width: 14 }} />;
    if (entity.source === 'paperbox')
      return <PaperboxIcon style={{ height: 12, marginBottom: 1, width: 14 }} />;
    return null;
  }, [entity.source]);

  // Render display field based on type.
  const renderValueField = () => {
    if (isValueEmpty && entityType !== 'complex') {
      return <EmptyField handleEnableEditing={handleEnableEditing} />;
    }
    switch (entityType) {
      case 'complex':
        return (
          <ComplexField
            entity={entity}
            deleteEntity={handleDelete}
            isPlaceholder={isPlaceholder}
            isEditingAllowed={isEditingAllowed}
          />
        );
      case 'boolean':
        return (
          <BooleanField
            entity={entity}
            parent={parent}
            editEntity={editEntity}
            deleteEntity={handleDelete}
            isEditingAllowed={isEditingAllowed}
          />
        );
      case 'text':
      case 'options':
      case 'date':
        return (
          <TextField
            isEditingAllowed={isEditingAllowed}
            entity={entity}
            truncatedStringValue={truncatedStringValue}
            handleEnableEditing={handleEnableEditing}
            deleteEntity={handleDelete}
          />
        );
      case 'image':
        return <ImageField entity={entity} deleteEntity={handleDelete} isEditingAllowed={isEditingAllowed} />;
      default:
        return null;
    }
  };

  // Render editing field based on type.
  const renderEditingField = () => {
    const emptyFallback = <LabelingEditField handleClearEditing={handleClearEditing} />;
    switch (entityType) {
      case 'text':
        return isValueEmpty ? (
          emptyFallback
        ) : (
          <TextEditField
            entity={entity}
            editInputValue={editInputValue}
            setEditInputValue={setEditInputValue}
            handleClearEditing={handleClearEditing}
            parent={parent}
            editEntity={editEntity}
            addEntity={addEntity}
            isPlaceholder={isPlaceholder}
            creatingEntity={creatingEntity}
          />
        );
      case 'options':
        return (
          <OptionsEditField
            entity={entity}
            parent={parent}
            editEntity={editEntity}
            handleClearEditing={handleClearEditing}
            isPlaceholder={isPlaceholder}
            addEntity={addEntity}
            creatingEntity={creatingEntity}
          />
        );
      case 'date':
        return (
          <DateEditField
            entity={entity}
            parent={parent}
            editEntity={editEntity}
            handleClearEditing={handleClearEditing}
            isPlaceholder={isPlaceholder}
            addEntity={addEntity}
            creatingEntity={creatingEntity}
          />
        );
      case 'boolean':
        return (
          <BooleanEditField
            entity={entity}
            parent={parent}
            editEntity={editEntity}
            handleClearEditing={handleClearEditing}
            isPlaceholder={isPlaceholder}
            addEntity={addEntity}
            creatingEntity={creatingEntity}
          />
        );
      case 'image':
        return isValueEmpty ? (
          emptyFallback
        ) : (
          <ImageEditField
            entity={entity}
            parent={parent}
            editEntity={editEntity}
            handleClearEditing={handleClearEditing}
            isPlaceholder={isPlaceholder}
            addEntity={addEntity}
            creatingEntity={creatingEntity}
          />
        );
      case 'complex':
        return <ComplexField entity={entity} deleteEntity={handleDelete} />;
      default:
        return null;
    }
  };

  // Render complex entity group with children.
  if (entityType === 'complex') {
    const entityValue = entity.value as { complex: Record<string, IDocumentEnrichedEntity> };
    return (
      <div
        className={clsx(s.row_group, {
          [s.row_group__active]: isActive,
          [s.row_group__open]: isActive,
        })}
        data-status={isFailed && 'failed'}
      >
        <div
          onClick={handleSetActive}
          className={clsx(
            s.row_item,
            s.row_item__field,
            { [s.active]: isActive },
            { [s.pending]: isPending },
            { [s.optional]: isPlaceholder && !entity.isMandatory && !isActive && !isEditingCurrent },
          )}
          data-status={isFailed && 'failed'}
          key={entity.id}
        >
          <div className={s.row_item_left}>
            <div className={s.row_badge}>
              {renderSourceIcon}
              <span>{entity.typeDetails.name}</span>
            </div>
          </div>
          {isEditingCurrent ? renderEditingField() : renderValueField()}
        </div>
        <div className={s.row_group_children}>
          {entityValue?.complex &&
            Object.entries(entityValue.complex).map(([key, value]) => (
              <DocumentLabelerSidebarField
                parent={entity}
                entity={value}
                key={key}
                isFailedCheckTarget={
                  // Only mark specific child fields as failed, not all children
                  failedCheckTargets?.[value.id] || failedCheckTargets?.[`${entity.id}:child:${key}`] || false
                }
              />
            ))}
        </div>
      </div>
    );
  }

  // Render regular (non‑complex) entity.
  return (
    <div
      onClick={handleSetActive}
      className={clsx(
        s.row_item,
        s.row_item__field,
        { [s.active]: isActive && !isPlaceholder },
        { [s.pending]: isPending },
        { [s.optional]: isPlaceholder && !entity.isMandatory && !isActive && !isEditingCurrent },
      )}
      data-status={isFailed || isFailedCheckTarget ? 'failed' : 'succeeded'}
      key={entity.id}
    >
      <div className={s.row_item_left}>
        <div className={s.row_badge} title={entity.typeDetails.name}>
          {renderSourceIcon} <span>{entity.typeDetails.name}</span>
        </div>
      </div>
      {isEditingCurrent ? renderEditingField() : renderValueField()}
    </div>
  );
};

export default DocumentLabelerSidebarField;
