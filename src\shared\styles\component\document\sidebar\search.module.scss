@import "../../../vars/_vars";


.amount {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin: 10px auto;
}


.cards {
  display: flex;
  overflow: scroll;
  flex-direction: column;
  max-width: 100%;
  height: 100%;
  margin-top: 10px;
  //padding: 16px;p1
  padding: 2px;
  gap: 20px;
}


.hide {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  color: $medium-dark-gray;

  svg {
    color: $medium-dark-gray;

  }
}


::-webkit-scrollbar-track {
  border-radius: 10px;
  background: transparent;
}


::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background: #EEEEEE;
}


::-webkit-scrollbar {
  width: 5px;
}


.card_overlay {
  font-size: 15px;
  font-weight: 700;
  //position: absolute;
  z-index: 2;
  top: -100px;
  left: 0;
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: center;
  width: calc(100%);
  max-height: 0;
  padding: 0;
  cursor: pointer;
  transition: all 0.2s 0.1s;
  transform: translateY(0px);
  pointer-events: none;
  opacity: 0;
  color: $paperbox-blue;
  border: none;

  border-radius: 7px;
  background-color: rgba(255, 255, 255, 0.97);
  box-shadow: rgba(0, 0, 0, 0.05) 0px 4px 12px;
  user-focus: none;

  &:hover {
    box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 5px;
  }

  .card_overlay_icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 18px;
    max-height: 18px;
    transition: transform 0.2s;

    svg {
      height: 20px;
    }
  }

}


.card_overlay_item {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  gap: 8px;
  padding-inline: 10px;

  & + & {
    border-left: 1px solid $paperbox-blue--fade-extra;
  }

  &:hover {
    color: $paperbox-blue-light;
  }
}


.card {
  position: relative;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  flex-shrink: 0;
  width: 100%;
  border: 1px solid #EEEEEE;
  border-radius: 5px;

  &:hover .card_overlay {
    z-index: 2;
    top: 0;
    max-height: 40px;
    padding: 10px 0;
    pointer-events: auto;
    opacity: 1;
    border: 1px solid transparent;
    border-top: 1px solid #EEEEEE;
    user-focus: auto;

  }
}


.header {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 13px;
}


.rows {
  max-height: 216px;
  transition: max-height 0.2s ease;

  &__expanded {
    max-height: 500px;
  }
}


.overflow {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 60px;
  background: linear-gradient(180deg, rgba(#EEEEEE, 0) 0%, rgba(white, 1) 100%);
}


.more {
  font-size: 14px;
  display: flex;
  justify-content: center;
}


.row {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 10px 13px;
  background-color: #FCFBFC;
  gap: 16px;

  &__matched {
    background-color: $paperbox-blue--fade-extra;
  }

  & + & {
    border-top: 1px solid #EEEEEE;
  }
}


.info {
  display: flex;
  flex-direction: row-reverse;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  gap: 20px;
}


.type {
  font-size: 14px;
  color: #6F758A;

}


.value {
  font-size: 16px;
  overflow: hidden;
  text-align: end;
  white-space: nowrap;
  text-overflow: ellipsis;

  b {
    font-weight: 500;
    color: $paperbox-blue;
  }
}

.value_text {
  color: black;
}
.value_list{
  display: flex;
  flex-direction: row;
  gap: 4px;
  .item{
    font-size: 13px;
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    border-radius: 4px;
    padding: 2px 4px;
    font-weight: 500;
    background-color: white;

    b {
      font-weight: 700;
      color: $paperbox-blue;
    }
  }
}


.tag {
  position: absolute;
  top: 16px;
  right: 4px;
  width: 4px;
  height: 4px;
  border-radius: 4px;
  background-color: $paperbox-blue;
}

.tooltip_content {
  color: black;

  b {
    font-weight: 700;
    color: $paperbox-blue;
  }
}

.tooltip_item {
  line-height: 1.4;
}

.tooltip_text {
  color: black;
}
