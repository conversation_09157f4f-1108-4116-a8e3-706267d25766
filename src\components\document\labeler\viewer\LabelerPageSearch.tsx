import { PaginatedToken } from '@shared/models/document';
import { ReactComponent as ChrevronDown } from '@svg/chevron-down.svg';
import { ReactComponent as CrossIcon } from '@svg/cross-icon.svg';
import clsx from 'clsx';
import React, { useEffect, useMemo, useState } from 'react';
import s from './labeler-view.module.scss';

interface Props {
  setSearchQuery: (query: string) => void;
  searchQuery: string;
  paginatedTokens: PaginatedToken[];
  setActiveToken: (token: PaginatedToken) => void;
}

const LabelerPageSearch: React.FC<Props> = ({
  searchQuery,
  setSearchQuery,
  paginatedTokens,
  setActiveToken,
}) => {
  const [activeTokenIndex, setActiveTokenIndex] = useState<number>(0);

  const matches = useMemo(() => {
    if (searchQuery)
      return paginatedTokens.filter((e) => e.text.toLowerCase().includes(searchQuery.toLowerCase())) ?? [];
    return [];
  }, [paginatedTokens, searchQuery]);

  useEffect(() => {
    if (matches.length > 0) {
      setActiveTokenIndex(0);
    }
  }, [matches]);

  useEffect(() => {
    setActiveToken(matches[activeTokenIndex]);
  }, [activeTokenIndex, matches, setActiveToken]);

  return (
    <div className={s.page_search}>
      <input className={s.input} type="text" onChange={(e) => setSearchQuery(e.target.value)} />
      <div className={s.counter}>{`${activeTokenIndex + 1}/${matches.length}`}</div>
      <button
        className={s.counter_nav}
        onClick={() => setActiveTokenIndex(Math.min(activeTokenIndex + 1, matches.length - 1))}
      >
        <ChrevronDown style={{ transform: 'rotate(180deg)' }} />
      </button>
      <button
        className={s.counter_nav}
        onClick={() => setActiveTokenIndex(Math.max(activeTokenIndex - 1, 0))}
      >
        <ChrevronDown />
      </button>
      <button className={clsx(s.counter_nav, s.counter_nav__delete)}>
        <CrossIcon />
      </button>
    </div>
  );
};

export default LabelerPageSearch;
