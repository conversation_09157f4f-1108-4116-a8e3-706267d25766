import InboxSidebarItemNew from '@components/inbox/sidebar/InboxSidebarItemNew.tsx';
import StyledSelect from '@components/shared/dropdown/StyledSelect';
import { useDocumentCounters } from '@shared/hooks/useDocumentCounters.ts';
import { UrlParams } from '@shared/models/generic';
import { resetFilters, resetPaginationFilters } from '@shared/store/documentListSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store';
import { userInboxesSelector } from '@shared/store/userSlice.ts';
import s from '@shared/styles/component/inbox/inbox-sidebar.module.scss';
import { ReactComponent as DashboardIcon } from '@svg/dashboard-icon.svg';
import { ReactComponent as HistoryIcon } from '@svg/historical.svg';
import { ReactComponent as InboxIcon } from '@svg/inbox-icon-old.svg';
import { ReactComponent as PaperboxLogo } from '@svg/paperbox-logo.svg';
import clsx from 'clsx';
import React, { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { NavLink, useLocation, useNavigate, useParams } from 'react-router';
import InboxSidebarNavItem from './InboxSidebarNavItem';

interface Props {}

const InboxSidebarNew: React.FC<Props> = () => {
  const { inboxId }: UrlParams = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const userInboxes = useSelector(userInboxesSelector);
  const userAccount = useSelector((state) => state.user.userAccount);
  const filters = useSelector((state) => state.documentList.filters);
  const isHistorical = location.pathname.includes('historical');
  const totalDocCount = useSelector((state) => state.documentList.paginationCounts.totalDocCount);

  const { data: statsData } = useDocumentCounters({ historical: isHistorical });

  useEffect(() => {
    if (inboxId && userInboxes.length > 0) {
      const inbox = userInboxes.find((inbox) => inbox.id === inboxId);
      if (inbox) {
        document.title = `${inbox.settings.name} | Paperbox`;
      }
    }
    return () => {
      document.title = 'Home | Paperbox';
    };
  }, [filters.docTypeId, inboxId, userInboxes]);

  const handleClearNavOptions = () => {
    // dispatch(resetFilters());
    dispatch(resetPaginationFilters());
  };

  const activeInboxItem = useMemo(() => {
    const matchedInbox = userInboxes.find((inbox) => inbox.id === inboxId);
    if (!matchedInbox) return null;
    return { value: matchedInbox.id, label: matchedInbox.settings.name };
  }, [userInboxes, inboxId]);
  const inboxOptions = useMemo(() => {
    return userInboxes.map((item) => ({
      value: item.id,
      label: item.settings.name,
    }));
  }, [userInboxes]);

  return (
    <div className={s.container} data-testid="inbox-sidebar">
      <div className={s.header}>
        <NavLink aria-label="home" to={`/inbox/${inboxId}`}>
          <PaperboxLogo className={s.logo} />
          {import.meta.env.VITE_PAPERBOX_ENVIRONMENT !== 'production' && (
            <div className={s.tag}>
              <span>{import.meta.env.VITE_PAPERBOX_ENVIRONMENT}</span>
            </div>
          )}
        </NavLink>
      </div>
      <InboxSidebarNavItem
        icon={<DashboardIcon />}
        path={`/inbox/${inboxId}/dashboard`}
        title={t('home:dashboard.title')}
      />
      <InboxSidebarNavItem
        icon={<InboxIcon />}
        path={`/inbox/${inboxId}`}
        title={t('home:inbox')}
        onClick={handleClearNavOptions}
      />
      {userAccount.isAdmin && (
        <InboxSidebarNavItem
          testId={'inbox-sidebar-historical'}
          icon={<HistoryIcon />}
          path={`/inbox/${inboxId}/historical`}
          title={t('home:historical')}
          onClick={handleClearNavOptions}
        />
      )}

      {!location.pathname.includes('dashboard') && (
        <>
          <div style={{ marginTop: 20 }} className={clsx(s.segment, s.segment__top)}>
            <div className={s.segment_head}>
              <div className={s.segment_title}>{t('home:inboxes')}</div>
            </div>

            <div className={s.dropdown}>
              <StyledSelect
                smallOptionList
                testId={userInboxes.length > 0 ? 'inbox-select-active' : 'inbox-select'}
                value={activeInboxItem}
                onChange={(e: any) => {
                  let path = '';
                  if (!inboxId) {
                    path = `/inbox/${e.value}`;
                  } else if (e && inboxId !== e.value) {
                    path = location.pathname.replace(inboxId, e.value);
                    dispatch(resetFilters());
                  }
                  navigate(path, { replace: true });
                }}
                options={inboxOptions}
                isLoading={!(userInboxes.length > 0 && inboxId)}
              />
            </div>
          </div>
          <div data-testid={'inboxes-sidebar-segment'} className={clsx(s.segment, s.segment__top)}>
            <div className={s.segment_head}>
              <div className={s.segment_title}>{t('home:docTypes')}</div>
            </div>
            <NavLink to={isHistorical ? `/inbox/${inboxId}/historical` : `/inbox/${inboxId}`}>
              <InboxSidebarItemNew
                counterItem={{
                  id: '',
                  count: statsData?.actualTotal,
                }}
                filteredItem={{
                  id: '',
                  count: totalDocCount,
                }}
              />
            </NavLink>
            {statsData?.rawCounters && (
              <div className={s.list}>
                {statsData.rawCounters.map((item) => {
                  const filteredMatch = statsData?.filteredCounters.find((e) => e.id === item.id);
                  return (
                    <InboxSidebarItemNew key={item.id} counterItem={item} filteredItem={filteredMatch} />
                  );
                })}
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default InboxSidebarNew;
