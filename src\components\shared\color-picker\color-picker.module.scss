@import "src/shared/styles/vars/_vars";


.container {
  width: 100%;
  height: 32px;
  padding: 6px 15px;
  position: relative;
  border-radius: 5px;
  transition: background-color 0.3s ease-in-out;
  will-change: background-color;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}


.picker {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: white;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  border-radius: 5px;
}


@keyframes growIn {
  0% {
    transform: scale(0.1);
    opacity: 0.5;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }

}


.color {
  cursor: pointer;
  height: 100%;
  width: 100%;
  border-radius: 5px;
  animation: growIn 0.5s cubic-bezier(.24, 1.3, .65, 1.24);
  transition: transform 0.2s ease-in-out;

  &:hover {
    transform: scale(1.1);
  }

}


