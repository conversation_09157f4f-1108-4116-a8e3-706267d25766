import s from '@shared/styles/component/admin/admin-section.module.scss';
import { ReactComponent as PlusIcon } from '@svg/plus-icon.svg';
import { ReactComponent as TrashIcon } from '@svg/trash-icon-alt.svg';
import clsx from 'clsx';
import { cloneDeep } from 'lodash';
import React, { useState } from 'react';

interface Props {
  examples: Record<string, any>[] | null;
  onChange: (examples: Record<string, any>[] | null) => void;
  title: string;
  description: string;
}

const AdminDatasourceExamplesEditor: React.FC<Props> = ({ examples, onChange, title, description }) => {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingJson, setEditingJson] = useState<string>('');

  const addExample = () => {
    const newExamples = examples ? [...examples] : [];
    newExamples.push({});
    onChange(newExamples);
    setEditingIndex(newExamples.length - 1);
    setEditingJson('{}');
  };

  const removeExample = (index: number) => {
    if (!examples) return;
    const newExamples = examples.filter((_, i) => i !== index);
    onChange(newExamples.length > 0 ? newExamples : null);
    if (editingIndex === index) {
      setEditingIndex(null);
      setEditingJson('');
    }
  };

  const startEditing = (index: number) => {
    if (!examples) return;
    setEditingIndex(index);
    setEditingJson(JSON.stringify(examples[index], null, 2));
  };

  const saveExample = () => {
    if (!examples || editingIndex === null) return;
    
    try {
      const parsedJson = JSON.parse(editingJson);
      const newExamples = cloneDeep(examples);
      newExamples[editingIndex] = parsedJson;
      onChange(newExamples);
      setEditingIndex(null);
      setEditingJson('');
    } catch (error) {
      // Invalid JSON - don't save
      alert('Invalid JSON format. Please check your syntax.');
    }
  };

  const cancelEditing = () => {
    setEditingIndex(null);
    setEditingJson('');
  };

  const renderExample = (example: Record<string, any>, index: number) => {
    const isEditing = editingIndex === index;
    
    if (isEditing) {
      return (
        <div key={index} className={s.example_editor}>
          <div className={s.example_header}>
            <span>Example {index + 1}</span>
            <div className={s.example_actions}>
              <button
                type="button"
                onClick={saveExample}
                className={s.example_save}
              >
                Save
              </button>
              <button
                type="button"
                onClick={cancelEditing}
                className={s.example_cancel}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={() => removeExample(index)}
                className={s.example_remove}
              >
                <TrashIcon />
              </button>
            </div>
          </div>
          <textarea
            value={editingJson}
            onChange={(e) => setEditingJson(e.target.value)}
            className={s.example_textarea}
            rows={10}
            placeholder="Enter JSON example data..."
          />
        </div>
      );
    }

    return (
      <div key={index} className={s.example_item}>
        <div className={s.example_header}>
          <span>Example {index + 1}</span>
          <div className={s.example_actions}>
            <button
              type="button"
              onClick={() => startEditing(index)}
              className={s.example_edit}
            >
              Edit
            </button>
            <button
              type="button"
              onClick={() => removeExample(index)}
              className={s.example_remove}
            >
              <TrashIcon />
            </button>
          </div>
        </div>
        <pre className={s.example_preview}>
          {JSON.stringify(example, null, 2)}
        </pre>
      </div>
    );
  };

  return (
    <div className={clsx(s.item, s.item__vertical)}>
      <div className={s.item_text}>
        <h4>{title}</h4>
        <p>{description}</p>
      </div>
      <div className={s.item_action}>
        <div className={s.examples_editor}>
          {examples && examples.length > 0 ? (
            examples.map((example, index) => renderExample(example, index))
          ) : (
            <div className={s.examples_empty}>
              <p>No examples defined. Add some example data to help with testing and validation.</p>
            </div>
          )}
          
          <button
            type="button"
            onClick={addExample}
            className={s.top_button}
            disabled={editingIndex !== null}
            style={{ marginTop: '16px' }}
          >
            <PlusIcon /> Add Example
          </button>
        </div>
      </div>
    </div>
  );
};

export default AdminDatasourceExamplesEditor;
