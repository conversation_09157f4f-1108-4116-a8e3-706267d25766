import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import sr from '@shared/styles/component/admin/admin-item-row.module.scss';
import se from '@shared/styles/component/admin/admin-section.module.scss';
import { ReactComponent as PlusIcon } from '@svg/plus-icon.svg';
import { ReactComponent as TrashIcon } from '@svg/trash-icon-alt.svg';
import clsx from 'clsx';
import { cloneDeep } from 'lodash';
import React, { useEffect } from 'react';
import AdminMultiSelectDialog from '../AdminMultiSelectDialog.tsx';

interface Props {
  hidden?: boolean;
  title?: string;
  description?: string;
  value: Array<{
    id: string;
    children?: string[] | null;
  }>;
  onChange: (value: any) => void;
  options: Array<{ id: string; name: string; children?: Array<{ id: string; name: string }> }>;
  showDialog: (component: JSX.Element) => void;
  noChildrenText?: string;
  restrictDeleteAll?: boolean;
}

interface GroupedItem {
  parentDetails: { id: string; name: string; children?: Array<{ id: string; name: string }> };
  parentId: string;
  items: Array<{
    name: string;
    id: string;
    parentId?: string;
    isOnlyOne: boolean;
    isAll?: boolean;
  }>;
  hasChildren: boolean;
}

const FormMultiSelectField: React.FC<Props> = ({
  hidden,
  title,
  description,
  value,
  onChange,
  options,
  showDialog,
  noChildrenText,
  restrictDeleteAll = false,
}) => {
  // Normalize empty children arrays to null (which means "all children selected")
  useEffect(() => {
    if (!value) return;

    const hasEmptyChildrenArrays = value.some(
      (opt) => Array.isArray(opt.children) && opt.children.length === 0,
    );

    if (hasEmptyChildrenArrays) {
      const normalizedValue = value.map((opt) => ({
        ...opt,
        children: Array.isArray(opt.children) && opt.children.length === 0 ? null : opt.children,
      }));

      onChange(normalizedValue);
    }
  }, [value, onChange]);

  if (hidden) return null;

  // Group items by parent for better UI organization
  const groupedItems = (): Array<GroupedItem> => {
    // If there are no values or no options, return empty array
    if (!value || value.length === 0 || !options || options.length === 0) return [];

    // Group items by parent ID
    const itemsByParent: Record<string, GroupedItem> = {};
    const ungroupedItems: Array<{
      name: string;
      id: string;
      parentId?: string;
      isOnlyOne: boolean;
    }> = [];

    value.forEach((opt) => {
      const details = options.find((o) => o.id === opt.id);
      if (!details) return;

      const hasChildren = details?.children && details.children.length > 0;

      // Only create groups for items that have children
      if (
        hasChildren &&
        (opt.children === null || (Array.isArray(opt.children) && opt.children.length > 0))
      ) {
        // Create entry for this parent if it doesn't exist
        if (!itemsByParent[opt.id]) {
          itemsByParent[opt.id] = {
            parentDetails: details,
            parentId: opt.id,
            items: [],
            hasChildren: true,
          };
        }

        // Case 1: "All Tables" or "All Document Types" option
        if (
          opt.children === null ||
          opt.children === undefined ||
          (Array.isArray(opt.children) && opt.children.length === 0)
        ) {
          itemsByParent[opt.id].items.push({
            name: `${noChildrenText || 'All'}`,
            id: details.id,
            isOnlyOne: true,
            isAll: true,
          });
        }
        // Case 2: Specific selected children
        else if (Array.isArray(opt.children) && opt.children.length > 0) {
          opt.children.forEach((optChild) => {
            const childDetails = details.children?.find((detailed) => detailed.id === optChild);
            if (childDetails) {
              itemsByParent[opt.id].items.push({
                name: childDetails.name,
                id: childDetails.id,
                parentId: opt.id,
                isOnlyOne: opt.children?.length === 1,
              });
            }
          });
        }
      }
      // Case 3: Item with no children - add to ungrouped items
      else {
        ungroupedItems.push({
          name: details.name,
          id: details.id,
          isOnlyOne: value.length === 1,
        });
      }
    });

    // If we have ungrouped items, add them as a separate non-grouped section
    if (ungroupedItems.length > 0) {
      itemsByParent['ungrouped'] = {
        parentDetails: { id: 'ungrouped', name: '' },
        parentId: 'ungrouped',
        items: ungroupedItems,
        hasChildren: false,
      };
    }

    return Object.values(itemsByParent);
  };

  const renderItem = (item: {
    name: string;
    id: string;
    parentId?: string;
    isOnlyOne: boolean;
    isAll?: boolean;
  }) => {
    const disableDelete = restrictDeleteAll && item.isOnlyOne;
    const isChild = !!item.parentId;
    const deleteButton = (
      <button
        className={clsx(sr.action, sr.trash)}
        data-testid={'sortable-item-delete'}
        onClick={(e) => {
          if (disableDelete) return;
          e.preventDefault();
          e.stopPropagation();

          const newValue = cloneDeep(value);
          if (item.parentId) {
            const parentIndex = newValue.findIndex((v) => v.id === item.parentId);
            if (parentIndex === -1) return;
            const parent = newValue[parentIndex];
            if (Array.isArray(parent.children)) {
              const newChildren = parent.children.filter((id) => id !== item.id);
              if (newChildren.length === 0) {
                onChange(newValue.filter((v) => v.id !== item.parentId));
              } else {
                newValue[parentIndex] = { ...parent, children: newChildren };
                onChange(newValue);
              }
            }
          } else {
            onChange(newValue.filter((v) => v.id !== item.id));
          }
        }}
        disabled={disableDelete}
        title={disableDelete ? 'At least one item must remain selected' : undefined}
      >
        <TrashIcon />
      </button>
    );

    return (
      <div
        key={`${item.parentId || ''}-${item.id}-${item.name}`}
        style={{ direction: 'ltr' }}
        data-testid="sortable-item"
        className={clsx(sr.wrapper, sr.wrapper__no_anim, {
          [sr.wrapper__small]: true,
          [sr.childItem]: isChild,
        })}
      >
        <div className={sr.container}>
          <span className={sr.title}>{item.name}</span>
        </div>
        {disableDelete ? (
          <Tooltip content={'At least one item must remain selected'} position={'top'}>
            {deleteButton}
          </Tooltip>
        ) : (
          deleteButton
        )}
      </div>
    );
  };

  const groups = groupedItems();

  return (
    <div className={clsx(se.item, se.item__vertical)}>
      <div className={se.item_head}>
        <div className={se.item_text} style={{ marginBottom: 10 }}>
          <h4>{title}</h4>
          <p>{description}</p>
        </div>

        {options && options.length > 0 ? (
          <button
            type="button"
            className={se.item_field}
            style={{ maxWidth: 42, justifySelf: 'flex-end' }}
            onClick={() =>
              showDialog(
                <AdminMultiSelectDialog
                  title={title}
                  description={description}
                  selectedTypes={value ?? []}
                  handleCheckTypes={(e) => onChange(e)}
                  detailedList={options}
                />,
              )
            }
          >
            <PlusIcon className={se.field_add} />
          </button>
        ) : (
          <div />
        )}
      </div>
      <div className={se.item_action}>
        <div
          // className={se.item_fields}
          style={{ display: 'flex', flexDirection: 'column', width: '100%' }}
        >
          {groups.map((group) => {
            if (!group.hasChildren)
              return <div className={se.item_fields}> {group.items.map((item) => renderItem(item))}</div>;
            return (
              <div key={`group-${group.parentId}`} className={clsx({ [sr.group]: group.hasChildren })}>
                {group.hasChildren && (
                  <div className={sr.group_header}>
                    <span>{group.parentDetails.name}</span>
                  </div>
                )}
                <div
                  className={clsx({
                    [se.item_fields]: true,
                  })}
                  style={!group.hasChildren ? { flexWrap: 'wrap' } : {}}
                >
                  {group.items.map((item) => renderItem(item))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default FormMultiSelectField;
