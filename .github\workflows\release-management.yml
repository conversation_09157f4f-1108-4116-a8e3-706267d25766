name: Release Management
on:
  release:
    types: [prereleased, released]
  workflow_dispatch:
    inputs:
      tag_name:
        type: string
        required: true
        description: Version tag (e.g. v1.52.0)
      release_type:
        type: choice
        description: 'Release type'
        required: true
        default: 'prerelease'
        options:
          - prerelease
          - published

jobs:
  update_branches:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ssh-key: ${{ secrets.PRIVATE_DEPLOY_KEY }}
      - name: Determine release type
        id: release_type
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "RELEASE_TYPE=${{ github.event.inputs.release_type }}" >> $GITHUB_ENV
          else
            if [ "${{ github.event.release.prerelease }}" == "true" ]; then
              echo "RELEASE_TYPE=prerelease" >> $GITHUB_ENV
            else
              echo "RELEASE_TYPE=released" >> $GITHUB_ENV
            fi
          fi
          
          # Log the event action for debugging
          echo "Release event action: ${{ github.event.action }}"

      - name: Update branches based on release type
        run: |
          # Get the tag name from the release or input
          TAG_NAME="${{ github.event.release.tag_name || github.event.inputs.tag_name }}"
          
          if [ -z "$TAG_NAME" ]; then
            echo "No tag name provided"
            exit 1
          fi
          
          echo "Processing release: $TAG_NAME with type: $RELEASE_TYPE"
          
          # Always update pre-release branch for any release type (prerelease or released)
          echo "Updating pre-release branch to tag: $TAG_NAME"
          git checkout -B pre-release $TAG_NAME
          git push -f origin pre-release
          
          # Only update release branch for released releases
          if [ "$RELEASE_TYPE" == "released" ]; then
            echo "Updating release branch to tag: $TAG_NAME"
            git checkout -B release $TAG_NAME
            git push -f origin release
          fi

      - name: Create Sentry Release
        if: env.RELEASE_TYPE == 'released'
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: react
          SENTRY_RELEASE_VERSION: ${{ github.event.release.tag_name || github.event.inputs.tag_name }}
        run: |
          curl -sL https://sentry.io/get-cli/ | bash
          sentry-cli releases set-commits --auto ${{ env.SENTRY_RELEASE_VERSION }}
          sentry-cli releases finalize ${{ env.SENTRY_RELEASE_VERSION }}
          sentry-cli releases deploys ${{ env.SENTRY_RELEASE_VERSION }} new -e production
