import Checkbox from '@components/shared/checkbox/Checkbox';
import s from '@shared/styles/component/admin/admin-user-dropdown.module.scss';
import React, {useState} from 'react';

interface Props {
  name: string;
  isSelected: boolean;
  setIsSelected: (selected: boolean) => void;
}

const AdminUserDropdownItem: React.FC<Props> = ({ name, isSelected, setIsSelected }) => {
  const [isChecked, setIsChecked] = useState(isSelected);

  const handleSelection = () => {
    setIsSelected(isSelected);
    setIsChecked(!isChecked);
  };

  return (
    <div data-testid={'admin-user-option'} onClick={handleSelection} className={s.bottom_row}>
      <span>{name}</span>
      <Checkbox checked={isChecked} />
    </div>
  );
};

export default AdminUserDropdownItem;
