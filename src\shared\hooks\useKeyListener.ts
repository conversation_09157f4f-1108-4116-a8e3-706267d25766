import { useCallback, useEffect } from 'react';

interface UseKeyListenerOptions {
  /** The key to listen for (e.g. 'Shift', 'Enter', etc.) */
  key: string;
  /** Optional callback to run when the key is pressed down */
  onKeyDown?: (e: KeyboardEvent) => void;
  /** Optional callback to run when the key is released */
  onKeyUp?: (e: KeyboardEvent) => void;
}

/**
 * useKeyListener sets up global keydown/keyup listeners for the specified key,
 * and calls the provided callbacks when that key is pressed or released.
 */
const useKeyListener = ({ key, onKeyDown, onKeyUp }: UseKeyListenerOptions) => {
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === key && onKeyDown) onKeyDown(e);
    },
    [key, onKeyDown],
  );

  const handleKeyUp = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === key && onKeyUp) onKeyUp(e);
    },
    [key, onKeyUp],
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleKeyDown, handleKeyUp]);
};

export default useKeyListener;
