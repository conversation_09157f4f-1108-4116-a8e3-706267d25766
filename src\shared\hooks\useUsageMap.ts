import { useMemo } from 'react';
import { IClientDocType } from '@shared/helpers/converters/doctype.ts';

export interface UsageInfo {
  count: number;
  docTypeNames: string[];
}

export type UsageMap = Map<string, UsageInfo>;

/**
 * Custom hook to calculate usage of field types or metadata types across document types
 * @param inboxDocTypes - Array of document types
 * @param extractorFn - Function to extract the relevant IDs from each document type
 * @returns Map of item ID to usage information
 */
export const useUsageMap = (
  inboxDocTypes: IClientDocType[],
  extractorFn: (docType: IClientDocType) => { id: string }[] | undefined
): UsageMap => {
  return useMemo(() => {
    const usageMap = new Map<string, UsageInfo>();
    
    inboxDocTypes.forEach((docType) => {
      const items = extractorFn(docType);
      items?.forEach((item) => {
        const itemId = item.id;
        const existing = usageMap.get(itemId) || { count: 0, docTypeNames: [] };
        existing.count += 1;
        existing.docTypeNames.push(docType.name);
        usageMap.set(itemId, existing);
      });
    });
    
    return usageMap;
  }, [inboxDocTypes, extractorFn]);
};

// Specific hooks for field types and metadata types
export const useFieldTypeUsageMap = (inboxDocTypes: IClientDocType[]): UsageMap => {
  return useUsageMap(inboxDocTypes, (docType) => docType.entityTypes);
};

export const useMetadataUsageMap = (inboxDocTypes: IClientDocType[]): UsageMap => {
  return useUsageMap(inboxDocTypes, (docType) => docType.metadataKeys);
};
