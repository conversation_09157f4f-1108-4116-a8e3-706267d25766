import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import { UsageInfo } from '@shared/hooks/useUsageMap.ts';
import s from '@shared/styles/component/admin/admin-item-row.module.scss';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
  usageInfo: UsageInfo;
}

// Constants for better maintainability
const TRUNCATION_THRESHOLD = 12;
const MAX_VISIBLE_TAGS = 2;
const TOOLTIP_MAX_WIDTH = '300px';
const TAG_TOOLTIP_MAX_WIDTH = '200px';

/**
 * Component for individual doctype tags with truncation detection
 */
const DoctypeTag: React.FC<{ docTypeName: string }> = ({ docTypeName }) => {
  // Character-length-based truncation detection
  // Based on CSS: max-width: 80px, font-size: 10px, padding: 2px 6px
  const isTextTruncated = docTypeName.length > TRUNCATION_THRESHOLD;

  const tagElement = <div className={s.usage_doctype_tag}>{docTypeName}</div>;

  if (isTextTruncated) {
    return (
      <Tooltip content={docTypeName} position="top" tooltipStyle={{ maxWidth: TAG_TOOLTIP_MAX_WIDTH }}>
        {tagElement}
      </Tooltip>
    );
  }

  return tagElement;
};

/**
 * Displays usage information for field types or metadata types
 */
const UsageDisplay: React.FC<Props> = ({ usageInfo }) => {
  const { t } = useTranslation();

  const limitedList = useMemo(() => {
    return usageInfo.docTypeNames.slice(0, 15);
  }, [usageInfo]);

  if (usageInfo.count === 0) {
    return (
      <div className={s.usage_info}>
        <div className={s.usage_unused}>
          <span>{t('admin:fieldType.usage.unused')}</span>
        </div>
      </div>
    );
  }

  const visibleDoctypes = usageInfo.docTypeNames.slice(0, MAX_VISIBLE_TAGS);
  const hasMoreDoctypes = usageInfo.count > MAX_VISIBLE_TAGS;

  return (
    <div className={s.usage_info}>
      <div className={s.usage_display}>
        <div className={s.usage_doctypes}>
          {visibleDoctypes.map((docTypeName, index) => (
            <DoctypeTag key={index} docTypeName={docTypeName} />
          ))}
          {hasMoreDoctypes && (
            <Tooltip
              content={
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: '8px', overflow: 'auto' }}>
                    {t('admin:fieldType.usage.usedIn')}
                  </div>
                  <ul style={{ margin: 0, paddingLeft: '16px' }}>
                    {limitedList.map((docTypeName, index) => (
                      <li key={index} style={{ marginBottom: '4px' }}>
                        {docTypeName}
                      </li>
                    ))}
                    {limitedList.length < usageInfo.count && (
                      <li style={{ fontStyle: 'italic' }}>
                        {t('admin:fieldType.usage.andMore', { count: usageInfo.count - limitedList.length })}
                      </li>
                    )}
                  </ul>
                </div>
              }
              position="left"
              alignment="center"
              tooltipStyle={{ maxWidth: TOOLTIP_MAX_WIDTH }}
            >
              <div className={s.usage_more}>+{usageInfo.count - MAX_VISIBLE_TAGS}</div>
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

export default UsageDisplay;
