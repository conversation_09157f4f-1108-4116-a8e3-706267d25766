import Modal from '@components/shared/modal/Modal.tsx';
import React from 'react';
import s from './christmas.module.scss';
import { useSelector } from '@src/shared/store/store';
import clsx from 'clsx';
interface Props { }

const ChristmasLetterModal: React.FC<Props> = () => {
  const { language } = useSelector((state) => state.user.userAccount.preferences)
  return (
    <Modal>
      {/* Unfolding Letter */}
      <div className={s.letter_wrapper}>
        <div className={clsx(s.letter, { [s.letter_nl]: language === 'nl' })} />
      </div>
    </Modal>
  );
};

export default ChristmasLetterModal;
