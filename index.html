<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <link href="/favicon.ico" rel="icon"/>
    <meta content="width=device-width, initial-scale=1" name="viewport"/>
    <meta
            content="Using artificial intelligence to distill your documents and contracts automatically. Providing insights and actions."
            name="description"
    />
    <meta content="Paperbox" property="twitter:title">
    <meta content="Paperbox" property="og:title">
    <meta content="Releasing the world of manual paperwork! Using artificial intelligence to distill your documents and contracts automatically. As an Enterprise Document Intelligence Platform, Paperbox was founded on the principles of turning unstructured content into structured data regardless of the look and feel of the source information."
          name="description">
    <meta content="#06101f" name="msapplication-TileColor">
    <meta content="website" property="og:type">
    <meta content="https://paperbox.ai" property="og:url">
    <meta content="Paperbox | Let us do your paperwork." property="og:title">
    <meta content="Paperbox uses artificial intelligence to distill your documents and emails. Paperbox automates, extracts and acts."
          property="og:description">
    <meta content="/banner.jpg" property="og:image">
    <meta content="#0f1825" name="theme-color">
    <link crossorigin href="https://fonts.googleapis.com/" rel="preconnect">
    <link href="/site.webmanifest" rel="manifest"/>
    <link as="style" href="https://fonts.googleapis.com/css2?family=Yantramanav:wght@300;400;500;700&display=swap"
          rel="preload">
    <link href="https://fonts.googleapis.com/css2?family=Yantramanav:wght@300;400;500;700&display=swap"
          rel="stylesheet">

    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
    <link href="/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180">
    <link href="/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png">
    <link href="/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png">
    <link color="#2d8fe9" href="/safari-pinned-tab.svg" rel="mask-icon">
    <title>Paperbox</title>

</head>
<body>
<div id="root"></div>
<script src="/src/index.tsx" type="module"></script>
</body>

</html>
