@import "@shared/styles/vars/_vars";


.container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  gap: 8px;

  .slider {
    margin-right: 16px;
  }

  &__left {
    flex-direction: row-reverse;

    .slider {
      margin-right: 0;
      margin-left: 16px;
    }
  }

}


.thumb {
  color: $paperbox-blue;
}


.slider {
  width: 100%;
  height: 4px;
  transition: background 450ms ease-in;
  border-radius: 8px;
  outline: none;
  background: linear-gradient(to right, $paperbox-blue 0%, $paperbox-blue 50%, #BFCAD9 50%, #BFCAD9 100%);
  -webkit-appearance: none;

  &:disabled {
    background: linear-gradient(to right, $medium-gray 0%, $medium-gray 50%, $medium-light-gray 50%, $medium-light-gray 100%);

    &::-webkit-slider-thumb, .thumb {
      background: $medium-gray;
    }
  }

  &::-webkit-slider-thumb, .thumb {
    width: 14px;
    height: 14px;
    cursor: pointer;
    border-radius: 10px;
    background: $paperbox-blue;
    -webkit-appearance: none;

    &:active {
      background-color: $paperbox-blue-medium;
    }
  }

}


.number {
  font-weight: 700;
  width: 60px;
  height: 35px;
  padding-right: 5px;
  padding-left: 10px;
  text-align: center;
  border: 1px solid $medium-gray;
  border-radius: 7px;
  background: $white;

  &:active, &:focus {
    border-color: $paperbox-blue;
    outline: none;

  }

  &:disabled {
    opacity: 0.4;
  }

  &__light {
    border: none;
    background: white;
  }

  &__small {
    height: 25px;
    padding-right: 5px;
    padding-left: 5px;
    border-radius: 3px;

    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      margin: 0;
      -webkit-appearance: none;
    }
  }

  &__hidden {
    color: #FAFAFA;
  }
}
