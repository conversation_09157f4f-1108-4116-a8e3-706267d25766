@import "@shared/styles/vars/_vars";


.sk_cube_grid {
  width: 50px;
  height: 50px;
  margin: 100px auto;
}


.sk_cube_grid .sk_cube {
  float: left;
  width: 33%;
  height: 33%;
  -webkit-animation: sk_cubeGridScaleDelay 1.3s infinite ease-in-out;
  animation: sk_cubeGridScaleDelay 1.3s infinite ease-in-out;
  background-color: $paperbox-blue;
}


.sk_cube_grid .sk_cube1 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}


.sk_cube_grid .sk_cube2 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}


.sk_cube_grid .sk_cube3 {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}


.sk_cube_grid .sk_cube4 {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}


.sk_cube_grid .sk_cube5 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}


.sk_cube_grid .sk_cube6 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}


.sk_cube_grid .sk_cube7 {
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}


.sk_cube_grid .sk_cube8 {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}


.sk_cube_grid .sk_cube9 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}


@-webkit-keyframes sk_cubeGridScaleDelay {
  0%, 70%, 100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}


@keyframes sk_cubeGridScaleDelay {
  0%, 70%, 100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}
