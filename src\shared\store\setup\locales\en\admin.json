{"actionType": {"alreadyExists": "Bounce field with this name already exists.", "config": "Configuration", "dangerZone": "Danger zone", "deleteType": "Delete bounce field", "deleteTypeDescription": "Deleting this bounce type will remove this field from the options users can select when triggering a bounce action.", "error": "An error has occurred, please try again.", "id": "Id", "idDescription": "This will be used to identity the type in a connected service. \ne.g. Webhook, API, ... ", "name": "Name", "nameDescription": "The name that will be visible to users in the UI.", "newOption": "New bounce option", "newType": "New bounce field", "optionAlreadyExists": "Option with this name already exists.", "optionDelete": "Delete option", "optionDeleteDescription": "Delete this option will remove it from the options a user can select when triggering a bounce action.", "providerId": "Provider Id", "providerIdDescription": "This will be used to identity the type in a connected service, when the provider id is not provided, the normal ID will be used. ", "sensitive": "Sensitive", "sensitiveDescription": "When a type is marked as sensitive, Paperbox will anonymize the data.", "valueOptions": "Allowed options", "valueOptionsDescription": "Options which will be available to the user when inputting this field.", "valueType": "Value type", "valueTypeDescription": "Change this option to select the type of this options value.", "values": "Values"}, "connectors": {"addConnector": "Add Connector", "auth": {"dataConfig": "Data Configuration", "enabled": "Active", "enabledDescription": "Enable or Disable OAuth Request, when this is enabled, you can use an extra OAuth request to authorize each request that uses this connector.", "headers": "Headers", "headersDescription": "Key, value pairs to be added as headers in the request.", "payload": "OAuth Request Payload", "payloadDescription": "These are key-value pairs that will form the data portion of the OAuth request.", "queryParams": "Query Params", "queryParamsDescription": "These params will be attached as query parameters to your auth URL.", "responseTokenKey": "Response Token Key", "responseTokenKeyDescription": "Name of the key in the <PERSON><PERSON><PERSON> response which contains the authentication token.", "title": "Auth Configuration", "url": "Auth URL", "urlDescription": "The URL which will be used when making the Auth request."}, "ccs": {"account": "Account", "accountDescription": "The account used for authenticating on the CCS server.", "password": "Password", "passwordDescription": "The password used for authenticating on the CCS server.", "url": "URL", "urlDescription": "The URL which will be used when making requests using this connector.", "username": "Username", "usernameDescription": "The username used for authenticating on the CCS server."}, "dangerZone": "Danger zone", "dataConfig": "Data Configuration", "delete": "Delete connector", "deleteDescription": "Deleting this connector might break existing connections that still use this connector, please check if there are any existing webhooks / APIs that still use this connector.", "environment": "Brio Environment", "environmentDescription": "The Brio environment of this connection.", "generalInfo": "General Info", "headers": "Headers", "headersDescription": "Key, value pairs to be added as headers in all requests.", "mailboxes": "Mailboxes", "ms365": {"addRule": "Add new rule", "approve": "Approved Documents", "bounce": "Bounce Documents", "connected": "Connected", "connectedTo": "Connected to", "connectedToMS365": "Connect with Microsoft 365", "delete": "Deleted Documents", "description": "Here you can approve Paperbox to access your Microsoft 365 tenant.", "editMailboxes": "Edit connected mailboxes", "emailPicker": {"disableAll": "Disable All", "disabled": "Disabled", "enableAll": "Enable All", "enabled": "Active", "selectEmails": "Select active email accounts"}, "inbound": "Inbound Rules", "inboundDescription": "Set up your email mapping to direct Microsoft 365 emails to specific Paperbox inboxes / document types.", "mailbox": "Mailbox", "outbound": "Outbound Destinations", "outboundDescription": "Configure the Microsoft inbox folder for documents processed in Paperbox", "title": "Microsoft 365 Connection", "userPicker": {"selectPath": "Email folder selecteren"}}, "name": "Name", "nameDescription": "The name used to identify the connector in the workspace settings.", "newConnector": "New Connector", "officeId": "Office ID", "officeIdDescription": "The ID of the office/database item that you want to connect to.", "payload": "Base payload", "payloadDescription": "These are key-value pairs that will be added to the data portion of each request that uses this connector.", "pi2Key": "PI2 Key", "pi2KeyDescription": "The key used to connect to the correct Portima Brio Database.", "queryParams": "Query Params", "queryParamsDescription": "These params will be attached as query parameters to the Base URL.", "save": "Save Changes", "saving": "Saving", "sftp": {"ip": "IP Address", "ipDescription": "The IP address of the SFTP server.", "password": "Password", "passwordDescription": "The password used for authenticating on the SFTP server.", "port": "Port", "portDescription": "The port of the SFTP server.", "privateKey": "Private Key", "privateKeyDescription": "The private key used for authenticating on the SFTP server.", "username": "Username", "usernameDescription": "The username user for authenticating on the SFTP server."}, "subOfficeId": "Sub Office ID", "subOfficeIdDescription": "The ID of user which is connected to the office/database item.", "title": "Connectors", "type": "Connector Type", "typeDescription": "Define which type the connector is.", "url": "Base URL", "urlDescription": "The base URL which will be used when making requests using this connector."}, "datasources": {"basicInfo": "Basic Information", "dangerZone": "Danger zone", "delete": "Delete datasource", "deleteConfirmation": "Are you sure you want to delete this datasource?", "deleteDescription": "Deleting this datasource will remove it and all associated data from Paperbox. This action cannot be undone.", "derivedFields": "Derived Fields", "derivedMappings": "Derived Mappings", "derivedMappingsDescription": "Configure computed fields that are derived from other field values using expressions.", "errorExists": "Datasource with this name already exists.", "errorGeneric": "Something went wrong, please try again later.", "exampleData": "Example Data", "exampleDataDescription": "Provide sample data records to help with testing and validation of the datasource configuration.", "fieldMappings": "Field Mappings", "frequency": "Update Frequency", "frequencyDescription": "How often the datasource should be updated automatically (in seconds). Leave empty for manual updates only.", "id": "ID", "idDescription": "Unique identifier for this datasource.", "mappings": "Field Mappings", "mappingsDescription": "Configure how fields from your data source are mapped and processed by Paperbox.", "name": "Name", "nameDescription": "A descriptive name for this datasource.", "newDatasource": "New Datasource", "operationRetention": "Operation Retention", "operationRetentionDescription": "Number of operations to retain in history.", "recordIdField": "Record ID Field", "recordIdFieldDescription": "The field that uniquely identifies each record in your datasource.", "sampleFile": "Sample File", "sampleFileDescription": "Upload a sample file (JSON, CSV, or Excel) to automatically generate field mappings.", "sampleRequired": "Please upload a sample file first to generate field mappings.", "sampleUploadError": "Failed to process sample file. Please check the format and try again.", "uploadSample": "Upload Sample & Configure"}, "docType": {"add": "Confirm", "ageThreshold": "Maximum document age", "ageThresholdDescription": "Adjust the maximum age a document can reach before it is \nmarked as overdue.", "alreadyExists": "Document type with this name already exists.", "approvalChecks": "Approval checks", "approvalThreshold": "Classification confidence threshold", "approvalThresholdDescription": "Change the threshold a document with this document type must \nreach to be marked as 'good'.", "autoApprove": "Auto Approve", "autoApproveDescription": "Change this option to enable / disable automatic approval of this \ndocument type when certain criteria are met.", "automation": "Automation", "categories": "Field categories", "categoriesDescription": "These are used to group certain fields in the labeling workspace.", "categoriesEdit": {"addFieldDescription": "Select the field types you want to include in this category.", "addFieldTitle": "Select field types"}, "config": "Configuration", "dangerZone": "Danger zone", "deleteType": "Delete document type", "deleteTypeDescription": "Deleting this document type will prevent any future documents to be detected as this type. \nCurrent documents with this type will not be deleted.", "docSubTypes": "Document subtypes", "docSubTypesNotFound": "No subtypes found", "docSubTypesSearch": "Search subtypes", "entityTypes": "Field types", "entityTypesDescription": "Select the field types that doctype can use. These can be reordered by dragging and dropping.", "fieldAutomation": "Automation Confidence", "fieldName": "Name", "fieldOccurrences": "Unique Occurrences", "fieldSearchDescription": "Select the field types you want to include in this document type.", "fields": "Fields", "fixed": "Fixed type", "fixedDescription": "When this option is enabled, users will not be able to change the doctype for documents with this doctype, changing the subtype will still work.", "id": "Id", "idDescription": "This will be used to identity the type in a connected service. \ne.g. Webhook, API, ... ", "masterdataCheck": "Masterdata Check", "masterdataCheckDescription": "When this option is enabled, Paperbox will use the provided masterdata table to lookup data on this document type.", "metaSearchDescription": "Select the metadata types you want to include in this document type.", "metadata": "<PERSON><PERSON><PERSON>", "metadataKeys": "Metadata types", "metadataKeysDescription": "Select the allowed metadata types that are assigned to this doctype.\nWhen a type has an option selected, this value will be automatically assigned when the document is processed.", "name": "Name", "nameDescription": "The name that will be visible to users in the UI.", "newType": "New document type", "ocrThreshold": "OCR Confidence threshold", "ocrThresholdDescription": "Change the threshold that text within a document with this document type must \nreach to be marked as 'good'.", "promptConfig": {"description": "Description", "descriptionDescription": "This field adds context to help the GenAI model better understand and classify documents. \nFor instance, specifying 'A medical document ...' for a health report aids in more accurate categorization.", "title": "GenAI Config"}, "providerId": "Provider Id", "providerIdDescription": "This will be used to identity the type in a connected service, when the provider id is not provided, the normal ID will be used. ", "quickEditCategories": {"metadata": "Metadata Fields"}, "subType": {"delete": "Delete subtype", "deleteDescription": "Deleting this subtype will prevent any future documents to be detected as this subtype. \nCurrent documents with this subtype will not be deleted."}, "type": "Type", "typeDescription": "Determines the applicable scenarios for this doctype."}, "errors": {"401": "You are not authorized to perform this action.", "403": "You do not have permission to perform this action.", "404": "The requested resource was not found.", "409": "Item with this ID already exists.", "500": "Something went wrong. Please try again later.", "inboxCreate": "Failed to create inbox. Please check your inputs and try again."}, "fieldType": {"alreadyExists": "Field type with this name already exists.", "complexFields": "Complex fields", "complexFieldsDescription": "Define the fields that are present as items in the complex field. You can also choose whether the field is mandatory or not.", "config": "Configuration", "dangerZone": "Danger zone", "deleteType": "Delete field type", "deleteTypeDescription": "Deleting this field type will prevent any future fields to be detected/labeled as this type. \nCurrent fields with this type will not be deleted.", "groups": {"basic": "Basic Fields", "data": "Data Fields", "complex": "Complex Fields", "media": "Media Fields"}, "id": "Id", "idDescription": "This will be used to identity the type in a connected service. \ne.g. Webhook, API, ... ", "name": "Name", "nameDescription": "The name that will be visible to users in the UI.", "newType": "New fieldtype", "options": "Values", "optionsDescription": "The values that will be available to the user when creating a field of this type.", "promptConfig": {"description": "Description", "descriptionDescription": "This field enhances the GenAI model's ability to identify this field within a document.\nFor example, specifying 'A Policy ID consists of 6-8 characters starting with BE' allows the model to more precisely recognize and process this field.", "title": "GenAI Config"}, "providerId": "Provider Id", "providerIdDescription": "This will be used to identity the type in a connected service, when the provider id is not provided, the normal ID will be used. ", "sensitive": "Sensitive", "sensitiveDescription": "When a type is marked as sensitive, Paperbox will anonymize the data.", "tableFields": "Table fields", "tableFieldsDescription": "Define the fields that are present as columns in the table.", "tableMandatory": "Mandatory", "tableSelectDescription": "Select the field types you want to include in this table", "tableSelectTitle": "Select field types", "type": "Type", "typeDescription": "This option defines the type of the entity, this is used when labeling in the UI.", "usage": {"unused": "Unused", "usedIn": "Used in:", "andMore": "And {{count}} more"}}, "inboxes": {"actionTypes": "Bounce fields", "actionTypesDescription": "Define the available options that users can select when bouncing a document.", "addInbox": "Add Inbox", "autoDelete": {"actionType": "Action Type", "actionTypeDescription": "Choose which action is taken when a document gets processed, choose between 'Bounce', 'Delete', 'Approve', or no action. \n\nWhen 'No Action' is selected, a webhook will not be triggered.", "enabled": "Cleanup rules", "enabledDescription": "With the cleanup rules you can automatically process documents after a pre-set amount of time has elapsed. Here you can enable/disable it. ", "options": {"approve": "Approve", "bounce": "<PERSON><PERSON><PERSON>", "delete": "Delete", "noAction": "No Action"}, "time": "Time Period", "timeDescription": "Specify the time period after which documents will be automatically processed.", "title": "Inbox Cleanup"}, "createInbox": "Create inbox", "days": "days", "docTypes": "Document types", "docTypesDescription": "Define the types that are used to classify each document, these are divided in 3 categories. Bundle types, Document types and Mail types.", "documentRetentionTime": "Document retention time", "documentRetentionTimeDescription": "The number of days documents will be retained by Paperbox after it's been processed.", "fieldTypes": "Field types", "fieldTypesDescription": "Configure the fields that Paperbox should extract from the document, and are available in the document labeling screen.", "id": "Id", "idDescription": "Used for identifying the inbox in our APIs.", "inboxMove": {"enabled": "Use Whitelist", "enabledDescription": "When this feature is enabled, users will only be able to move documents to the inboxes specified in the list below. ", "list": "Whitelisted Inboxes", "listDescription": "Select the inboxes that users will be allowed to move documents to, by default all inboxes are whitelisted.", "title": "Document Inbox Move"}, "inboxSettings": "Inbox settings", "inboxType": "Inbox Type", "inboxTypeDescription": "Select the type of inbox you want to create. ", "language": "Language", "languageDescription": "Select the template language you want to use for this inbox.", "masterdata": "Masterdata", "masterdataDescription": "All configuration related to the search and lookup in masterdata tables. Masterdata table can be uploaded via the workspace settings or via the Paperbox integration API (see <1>https://docs.paperbox.ai</1>)", "metadataTypes": "Metadata types", "metadataTypesDescription": "Configure the fields that Paperbox should not extract directly from the document, but are available in the output and/or search.", "name": "Inbox Name", "nameDescription": "The name that will be visible to the users in the UI.", "saveChanges": "Save Changes", "saving": "Saving", "sections": {"actionTypeDelete": "Are you sure you want to delete this bounce field?", "actionTypeOptionDelete": "Are you sure you want to delete this bounce field option?", "actionTypeSearch": "Search bounce fields", "addNew": "Add new", "docTypeDelete": "Are you sure you want to delete this document type?", "docTypeMultiDelete": "Are you sure you want to delete these document types?", "docTypeSearch": "Search document types", "fieldTypeDelete": "Are you sure you want to delete this field type?", "fieldTypeMultiDelete": "Are you sure you want to delete these field types?", "fieldTypeSearch": "Search field types", "masterdataSearch": "Search masterdata table list", "masterdataTableDelete": "Are you sure you want to delete this masterdata table?", "metadataTypeDelete": "Are you sure you want to delete this metadata type?", "metadataTypeSearch": "Search metadata types", "noActionTypeFound": "No bounce fields found", "noDocTypeFound": "No document types found", "noFieldTypeFound": "No field types found", "noMasterdataFound": "No masterdata tables found", "noMetadataTypeFound": "No metadata types found", "noTagTypeFound": "No Tags found", "tagTypeDelete": "Are you sure you want to delete this tag?", "tagTypeMultiDelete": "Are you sure you want to delete these tags?", "tagTypeSearch": "Search tag"}, "selectConnector": "Connector", "selectConnectorDescription": "Choose an existing connector to use with this inbox.", "settings": {"autoAdvance": "Auto Advance", "autoAdvanceDesc": "This feature allows users to move swiftly from one document to the next in the queue automatically after a document is finished, increasing efficiency in document processing.", "bounce": "Document Bounce", "bounceDesc": "The Document Bounce feature provides an option to reject a document and send it back. This is useful when a document lacks the required information to be correctly processed.", "documentCopy": "Document Copy", "documentCopyDesc": "The Document Copy feature allows users to create duplicates of a document. This is useful for creating different versions of a document which might need to be processed differently.", "documentDownload": "Document Download", "documentDownloadDesc": "This feature enables users to download documents directly to their local machine for offline use or further analysis.", "documentTransform": "Document Transformations", "documentTransformDesc": "This feature allows users to split specific sections of a document bundle, such as an email attachment, and classify each section individually within the same bundle. Additionally, when the document copy feature is enabled, users can create partial copies by excluding selected parts of the document.", "fileUpload": "UI Upload", "fileUploadDesc": "Allows users to upload documents straight from the Paperbox UI, useful for testing purposes.", "labelingMode": "Labeling Mode", "labelingModeDesc": "In labeling mode, the workspace will change various parameters so it is optimised for efficiently labeling and reviewing labeled documents for further training.", "mailroom": "Mailroom Inbox", "mailroomDesc": "Enable 'Mailroom' mode for this inbox."}, "system": "System", "systemDescription": "Select the system you want your inbox to use.", "tableCols": "Inbox Table Columns", "tableColsDescription": "Configure which columns will be visible to the users on the Paperbox document overview screen.", "tagTypes": "Tags", "tagTypesDescription": "Tags can be used to mark documents with a specific state (e.g. Priority, Status, ...). You can also use these tags to filter your documents.", "templateSelection": "Template Selection", "templates": {"brio": "Brio Inbox", "brioDescription": "Create an inbox that is pre-configured to work with B<PERSON>", "ccs": "CCS Inbox", "ccsDescription": "Create an inbox that is pre-configured to work with CCS", "custom": "Custom Inbox", "customDescription": "Create a custom inbox you can configure later with your own settings"}, "title": "Inbox configuration", "uploadMail": "Document upload email", "uploadMailDescription": "Linked email address where documents can be uploaded to for processing by Paperbox.", "useExistingConnector": "Use Existing Connector", "useExistingConnectorDescription": "Use an existing connector to connect to your system.", "workflow": "Active workflow", "workflowDescription": "Version of the workflow that's currently active on this inbox."}, "masterdata": {"boost": "Boost", "boostDescription": "Defines how relevant this table is compared to the other tables. \nDuring lookup and search, the table with the highest boost will be displayed first or prioritized. (0 - 10)", "config": "Configuration", "copyId": "Masterdata table ID copied to clipboard.", "dangerZone": "Danger zone", "delete": "Delete masterdata table", "deleteDescription": "Deleting this table will remove it and all data from Paperbox. This action cannot be undone.", "downloadCSV": "Download CSV", "downloadCSVDescription": "Download the data that was last uploaded to this table in CSV format.", "errorExists": "Masterdata table with this name already exists.", "errorGeneric": "Something went wrong, please try again later.", "errorMismatch": "Column mismatch in uploaded file. Please check and try again.", "fileSelect": "Select File", "fileUpload": "Upload ", "fileUploaded": "Uploaded successfully", "fileUploading": "Uploading", "headerChars": "Filtered Characters", "headerCharsInput": "Enter a character", "headerCharsTooltip": "Add characters that need to be filtered out when querying on this specific column.", "headerDisplay": "Field / Display Name", "headerDisplayTooltip": "When no field or display name is configured\nthis masterdata column will be ignored.", "headerLabel": "Table label", "headerLabelTooltip": "Name of column in the uploaded data", "headerMapping": "Mapping", "headerMappingTooltip": " Toggles between using:\n- Not Mapped / Hidden\n- Basic Display Name\n- Paperbox Field", "headerPin": "<PERSON><PERSON>", "headerPinTooltip": "Pinning an item will make it show up higher in the search results.", "headerSearchable": "Searchable", "headerSearchableTooltip": "When using the masterdata search, this column will be matchable.", "headerType": "Type", "headerTypeTooltip": "Toggles between search optimalised types:\n- Identifiers e.g. a claims id or a sap number\n- Full Text e.g. an address or a name", "id": "Id", "idDescription": "This will be used to identity the table in a connected service. \ne.g. Webhook, API, ... ", "latestUpload": "Latest Upload", "latestUploadDescription": "Shows when the data for this table was last updated", "lookup": "Lookup", "lookupDescription": "When enabled, the data in this table will be used during the document processing process.", "name": "Masterdata Tables", "tableMapping": "Table Mapping", "tableMappingDescription": "Here you can configure the mapping of your masterdata table data so it can be used in Paperbox.", "tableSearch": "Search", "tableSearchDescription": "Search within the selected tables", "title": "Add Table", "totalLineCount": "Number of rows", "totalLineCountDescription": "Shows the total amount of rows that have been added to this table in the latest upload.", "uploadData": "Upload new Data", "uploadDataDescription": "Allows you to upload new data for this table, which - after processing - will become accessible to the users."}, "metadataType": {"alreadyExists": "Metadata type with this name already exists.", "config": "Configuration", "dangerZone": "Danger zone", "deleteType": "Delete metadata type", "deleteTypeDescription": "Deleting this metadata type will remove this type from the list of metadata types that can be added to the document. \nCurrent documents that contain this metadata type will not be deleted.", "error": "An error has occurred, please try again.", "hidden": "Internal", "hiddenDescription": "When designated as 'internal,' this metadata remains hidden from users within the UI. This is specifically useful for internal identifiers and supplementary metadata relevant to downstream systems which is not relevant to users.", "id": "Id", "idDescription": "This will be used to identity the type in a connected service. \ne.g. Webhook, API, ... ", "name": "Name", "nameDescription": "The name that will be visible to users in the UI.", "newType": "New metadata type", "providerId": "Provider Id", "providerIdDescription": "This will be used to identity the type in a connected service, when the provider id is not provided, the normal ID will be used. ", "sensitive": "Sensitive", "sensitiveDescription": "When a type is marked as sensitive, Paperbox will anonymize the data.", "useTopologyPartsToAggregate": "Aggregate To Bundle Metadata", "useTopologyPartsToAggregateDescription": "When enabled, Paperbox will combine all mapped values of this metadata field within a bundle into one field, using the specified order of options.\nThis is useful for scenarios like using the highest priority value of a metadata field across all parts in a bundle.", "valueType": "Value type", "valueTypeDescription": "The type of the value", "values": {"addDescription": "Input the value you want to make available as an option.", "addNew": "Add New", "addTitle": "Input a new value", "description": "These are the choices you'll have while setting up this metadata field for a document type.\nThe order you arrange them in will determine their priority in case of a conflict.", "error": "Option already exists.", "title": "Values"}}, "multiSave": {"changeLogDescription": "These fields will be changed on {{number}} item(s)", "description": "Only fields you have edited will be changed, these changes will be applied to {{number}} item(s)", "title": "Are you sure?", "unsavedChangeLogDescription": "Changes to the following fields will be lost."}, "multiSelect": {"confirm": "Confirm", "deselect": "Deselect All", "select": "Select All"}, "notifications": {"updateError": "Update failed", "updateSuccess": "Update successful"}, "page": {"backToOverview": "Back to overview", "next": "Next", "previous": "Previous"}, "tagType": {"alreadyExists": "Tag with this name already exists.", "color": "Color", "colorDescription": "This will be used to visually distinguish the tags in the UI ", "config": "Configuration", "dangerZone": "Danger zone", "deleteType": "Delete tag", "deleteTypeDescription": "Deleting this tag will remove it from the list of tags that can be added to a document. \nCurrent documents that contain this tag will not be deleted.", "error": "An error has occurred, please try again.", "id": "Id", "idDescription": "This will be used to identity the type in a connected service. \ne.g. Webhook, API, ... ", "name": "Name", "nameDescription": "The name that will be visible to users in the UI.", "newType": "New tag", "providerId": "Provider Id", "providerIdDescription": "This will be used to identity the type in a connected service, when the provider id is not provided, the normal ID will be used. "}, "tenant": {"apiKey": "API Key", "apiKeyDescription": "Retrieve the API key that can be used to authenticate API requests to this workspace. <1/><0>See our documentation for more info.</0>", "domains": "Whitelisted Domains", "domainsAddDescription": "Please enter a new domain name you want to whitelist below. eg. gmail.com", "domainsAddTitle": "Add Domain", "domainsDescription": "Setting domains in this whitelist will allow users with this e-mail domain to log in to the workspace.", "fetchApiKey": "Fetch API key", "fetchPrivateKey": "Fetch Private key", "general": "General Settings", "id": "Id", "idDescription": "The ID for this workspace", "language": "Default Language", "languageDescription": "Here you can configure the default language that will be used in the UI for the users, users can manually change their language later.", "locale": "Locale", "name": "Name", "nameDescription": "Configure the name of your workspace, this will be used in personalised emails, password resets, user invites, etc.", "netlifyBeta": "Beta Features", "netlifyBetaDescription": "Enable/Disable Paperbox Beta features for ALL users in this tenant. Changes will take effect after page reload.", "privateKey": "Private Key", "privateKeyDescription": "Retrieve the private key that can be used to safely retrieve JWT Tokens for authentication.<1/><0>See our documentation for more info.</0>", "timeZone": "Timezone", "timeZoneDescription": "Setting the timezone of your workspace will affect the dashboard and reporting.", "title": "Workspace Settings"}, "unsavedChanges": {"description": "It looks like you have been editing something. If you leave before saving, your changes will be lost.", "title": "Unsaved Changes"}, "users": {"adminAccess": "Admin access", "createUser": "Add User", "creatingUser": "Creating", "deleteText": "This action can not be undone. The user won't be able to log in again until a new account has been created.", "emailSent": "Email sent to", "filter": "Filter Users", "inboxes": {"addDescription": "Select the inboxes to assign to this user.", "addTitle": "Assign Inboxes", "title": "Inboxes"}, "resetText": "This will send a password-reset email to the selected user.", "success": "Success", "title": "User management"}, "webhookValues": {"@ACTION": "The action that was performed on the document. Any of 'approve', 'delete' or 'bounce'.", "@ACTION_METADATA": "The metadata of the action that was performed on the document.", "@ACTOR": "Email of the actor, can be either 'paperbox' or a user.", "@CONFIDENCE": "The confidence at which a document was processed. Can be a value from 0 to 1.", "@DOCUMENT_CLASS": "The Document Type of the processed document.", "@DOCUMENT_SUBCLASS": "The Sub Document Type of the processed document.", "@ENTITIES": "The Field Types of the processed document.", "@ID": "ID of the document that was provided to Paperbox on ingestion.", "@METADATA": "The Metadata of the processed document.", "@MUTATION_ID": "The ID of a mutation if the processed document is not the original.", "@MUTATION_TYPE": "The Type of a mutation if the processed document not the original.", "@PAPERBOX_ID": "The Paperbox ID of the document.", "@PB_JSON_PAYLOAD": "Default Paperbox JSON Payload", "@PB_PRIMITIVE_JSON_PAYLOAD": "Primitive Paperbox JSON Payload", "@PB_RANDOM_UUIDv4": "A randomly generated UUIDv4 ID", "@PB_STRING_PAYLOAD": "Stringified Paperbox Payload", "@PROCESSED_DATE": "ISO String when the document was processed.", "placeholder": "Insert data or input text"}, "webhooks": {"BRIO_NOT_SUPPORTED": "Brio webhook configuration not yet supported", "add": "Add Webhook", "auth": {"customPayload": "OAuth Request Payload", "customPayloadDescription": "These are key-value pairs that will form the data portion of the OAuth request.", "dataConfig": "Data Configuration", "extend": "Extend Connector Auth Config", "extendDescription": "Enable this option to expand on the settings that are already configured in your connector config.", "headers": "Headers", "headersDescription": "Key, value pairs to be added as headers in the request.", "queryParams": "Query Params", "queryParamsDescription": "These params will be attached as query parameters to your URL.", "responseTokenKey": "Response Token Key", "responseTokenKeyDescription": "Name of the key in the <PERSON><PERSON><PERSON> response which contains the authentication token.", "title": "OAuth Configuration", "url": "Auth URL", "urlDescription": "The URL which will be used when making the Auth request."}, "brio": {"activitySettings": {"description": "Configure the default settings for new activities", "rds": "Default Service", "rdsDescription": "Enter the ID of the default Brio service that should be used whenever the administrator field is empty.", "title": "Activity Settings"}, "docTypeConfig": {"add": "Add document type", "addDescription": "Add a new document type to the Brio configuration", "addTitle": "Add new document type", "createActivity": "Create activity", "description": "Configure the processing of different document types to BRIO", "docType": "Document type", "hierarchy": "Preferred hierarchy", "options": {"claims": "<PERSON><PERSON><PERSON>", "contracts": "Contracts", "documents": "Documents", "parties": "Parties"}, "title": "Document Type Configuration"}, "metadataActivityMapping": {"archive_code": "Archive Code", "archive_codeDescription": "Select the metadata type whose value will appear as the archive code in Brio.", "description": "Description", "descriptionDescription": "Select the metadata type whose value will appear as the Activity description in Brio.", "due_date_days_delta": "Due Date Days Delta", "due_date_days_deltaDescription": "Select the metadata type whose numerical value will be added in days to the document recieve date? to decide the activity due date", "handler": "Handler", "handlerDescription": "Select the metadata type whose value will decide the Brio user the activity is assigned to", "is_completed": "Is Completed", "is_completedDescription": "Select the metadata type whose value will indicate if this item is completed in Brio.", "is_visible_my_broker": "Is Visible My Broker", "is_visible_my_brokerDescription": "Select the metadata type whose value will indicate If an activity will be visible in Mybroker from Brio.", "priority": "Priority", "priorityDescription": "Select the metadata type whose value will appear as the Activity priority in Brio. ", "sub_type": "Sub Type", "sub_typeDescription": "Select the metadata type whose value will appear as the sub type in Brio.", "title": "Map the configured Paperbox metadata to the responding Brio activity values", "type": "Type", "typeDescription": "Select the metadata type whose value will decide the type of activity that is created"}, "metadataDocumentMapping": {"archive_delay": "Archive Delay", "archive_delayDescription": "Select the metadata type whose value will appear as the archive delay in Brio.", "carrier_type": "Document Medium", "carrier_typeDescription": "Select the metadata type whose value will appear as the Document Medium in Brio.", "category": "Category", "categoryDescription": "Select the metadata type whose value will appear as the category in Brio; this is based on Brio table 962", "delete_delay": "Delete Delay", "delete_delayDescription": "Select the metadata type whose value will appear as the delete delay in Brio.", "description": "Description", "descriptionDescription": "Select the metadata type whose value will appear as the Document description in Brio.", "description_my_broker": "Description My Broker", "description_my_brokerDescription": "Select the metadata type whose value will appear as the broker description in Brio.", "is_favorite": "Is Favorite", "is_favoriteDescription": "Select the metadata type whose value will indicate if this is a favorite in Brio.", "is_visible_my_broker": "Document Visibility MyBroker", "is_visible_my_brokerDescription": "Select the metadata type whose value will indicate If a document will be visible in Mybroker from Brio.", "language": "Language", "languageDescription": "Select the metadata type whose value will appear as the language in Brio.", "origin": "Origin", "originDescription": "Select the metadata type whose value will appear as the origin in Brio; Is the document recieved or send", "qualifier": "Brio Doctype", "qualifierDescription": "Select the metadata type whose value will appear as the Brio Doctype in Brio; this is based on Brio table 970", "reference": "Reference", "referenceDescription": "Select the metadata type whose value will appear as the reference in Brio.", "security_level": "Security Level", "security_levelDescription": "Select the metadata type whose value will appear as the security level in Brio.", "title": "Set up mappings between Paperbox metadata fields and Brio fields, so that when a document is processed, specified metadata values are automatically populated in Brio."}, "metadataHierarchy": {"description": "Map the configured Paperbox fields from the referential data to the correct BRIO locations", "title": "Referential Data mapping"}}, "brioEndpointId": "Brio Endpoint ID", "brioEndpointIdDescription": "The Internal ID of the Brio Endpoint to be used for this webhook.", "ccs": {"agendaMapping": {"description": "Map Paperbox metadata to CCS agenda fields for automatic population.", "fields": {"create_agenda": "Create Agenda", "create_agendaDescription": "Select metadata type to determine if an agenda should be created.", "description": "Description", "descriptionDescription": "Select metadata type for the agenda description in CCS.", "due_date_delta": "Due Date Delta", "due_date_deltaDescription": "Select metadata type for ...", "employee_number": "Employee Number", "employee_numberDescription": "Select metadata type for the employee number", "reason": "Reason", "reasonDescription": "Select metadata type for the agenda reason in CCS."}, "title": "CCS Agenda Mapping"}, "archiveMapping": {"description": "Map Paperbox metadata to CCS archive fields", "fields": {"description": "Description", "descriptionDescription": "Select metadata type for the archive description in CCS.", "document_type": "Document Type", "document_typeDescription": "Select metadata type for the document type in CCS.", "is_secret": "Is Secret", "is_secretDescription": "Select metadata type to determine if archive is marked 'secret'."}, "title": "CCS Archive Mapping"}, "claimMapping": {"description": "Map Paperbox metadata to CCS claim fields", "fields": {"claim_number_office": "Claim Number Office", "claim_number_officeDescription": "Select metadata type for the office claim number in CCS.", "company_number": "Company Number", "company_numberDescription": "Select metadata type for the company number in CCS.", "intermediary_person_number": "Intermediary Person Number", "intermediary_person_numberDescription": "Select metadata type for the intermediary person number in CCS."}, "title": "CCS Claim Mapping"}, "docTypeConfig": {"add": "Add document type", "addDescription": "Add a new document type to the CCS configuration", "addTitle": "Add new document type", "agendaCreate": "Create Agenda", "agendaReason": "Agenda Reason", "description": "Configure the processing of different document types in CCS.", "docType": "Document Type", "title": "Document Type Configuration"}}, "connection": "Connection Settings", "connector": "Connector", "connectorDescription": "Select which connector is used for this webhook, \n all webhook calls will use the settings / parameters set in the connector", "customPayload": "Custom payload", "customPayloadDescription": "These are key-value pairs that will form the data portion of the request.", "dangerZone": "Danger zone", "dataConfig": "Data Configuration", "dataExtend": "Extend Connector Data Config", "dataExtendDescription": "Enable this option to expand on the settings that are already configured in your connector config.", "defaultPayload": "Use default payload", "defaultPayloadDescription": "When this option is enabled, the payload of the request will be the default Paperbox defined payload.", "delete": "Delete webhook", "deleteDescription": "Deleting an active webhook will break any/all existing connections to the connected system, please make sure you no longer need this webhook before deleting.", "dependsOn": "Depends on", "dependsOnDescription": "Select the webhooks that need to be triggered before this webhook can be triggered.", "enabled": "Active", "enabledDescription": "Enable or Disable the webhook, when the webhook is disabled it will no longer be used.", "generalInfo": "General Info", "headers": "Headers", "headersDescription": "Key, value pairs to be added as headers in all requests.", "id": "Id", "idDescription": "The Internal ID for this webhook", "name": "Name", "nameDescription": "The name used to identify the webhook in the UI.", "queryParams": "Query Params", "queryParamsDescription": "These params will be attached as query parameters to your URL.", "request": "Request", "save": "Save Changes", "saving": "Saving", "selectDocTypes": "Select Document Types", "selectDocTypesDescription": "Choose specific document types for each inbox that will trigger this webhook.", "selectInboxes": "Select Inboxes", "selectInboxesDescription": "Choose the inboxes that will trigger this webhook.", "selectTables": "Select Tables", "selectTablesDescription": "Choose specific tables for each inbox that will trigger this webhook.", "title": "Webhook config", "triggerActionTypes": "Trigger Action Types", "triggerActionTypesDescription": "The webhook will only trigger on the selected action types when triggered by the action event. Default is all action types.", "triggerActions": "Actions", "triggerDocTypes": "Document Types", "triggerDocTypesDescription": "Define the document types for each inbox on which this webhook should be triggered.", "triggerEvent": "Trigger Event", "triggerEventDescription": "Specify the events that trigger this webhook. The webhook will only trigger on the selected events.", "triggerInboxes": "Inboxes", "triggerInboxesDescription": "Define the inboxes on which this webhook should be triggered.", "triggerTables": "Tables", "triggers": "Triggers", "unflatten": "Unflatten", "unflattenDescription": "By default we convert fields with double underscores into nested dictionaries. \nDisable this option to turn this behavior off.", "url": "URL Path", "urlDescription": "The path which will be used for the webhook, this path will be 'attached' to the Base URL set in your selected connector. "}}