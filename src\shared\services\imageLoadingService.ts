// imageLoadingService.ts - Simplified image loading system
import apiClient from '@shared/helpers/apiClient.ts';
import { documentSlice } from '@shared/store/documentSlice.ts';
import { AppThunk } from '@shared/store/store.ts';

// Simple cache for cleanup functionality
const imageCache = new Map<string, string>();

// Helper function to clear cache for memory management
const clearImageCache = (docId?: string): void => {
  if (docId) {
    for (const key of imageCache.keys()) {
      if (key.startsWith(docId)) {
        imageCache.delete(key);
      }
    }
  } else {
    imageCache.clear();
  }
};

// Redux thunks for image loading
export const loadDocumentThumbnails =
  (inboxId: string, docId: string, pageCount: number): AppThunk =>
  async (dispatch, getState) => {
    console.log(`Loading ALL thumbnails for document: ${docId} (${pageCount} pages)`);

    // Set loading state
    dispatch(documentSlice.actions.setThumbnailsLoading({ docId, isLoading: true }));

    // Get current state to check what's already loaded
    const state = getState();
    const pageImagesMap = state.document.pageImagesMap;

    // Create array of pages that need thumbnails
    const pagesToLoad: number[] = [];
    for (let pageNo = 1; pageNo <= pageCount; pageNo++) {
      const hasThumb = pageImagesMap?.[docId]?.[pageNo]?.thumbUrl;
      if (!hasThumb) {
        pagesToLoad.push(pageNo);
      }
    }

    console.log(`Need to load thumbnails for ${pagesToLoad.length} pages:`, pagesToLoad);

    if (pagesToLoad.length === 0) {
      console.log('All thumbnails already loaded');
      dispatch(documentSlice.actions.setThumbnailsLoading({ docId, isLoading: false }));
      return;
    }

    // Load ALL thumbnails in parallel
    const loadPromises = pagesToLoad.map(async (pageNo) => {
      try {
        console.log(`Starting load for page ${pageNo}`);
        const url = `${import.meta.env.VITE_PAPERBOX_CDN_URL}/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}?w=490&q=30`;

        const response = await apiClient.get(url, {
          responseType: 'blob',
          timeout: 30000,
          headers: { accept: 'image/*' },
        });

        // Convert to base64
        const base64Data = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => resolve(reader.result as string);
          reader.onerror = reject;
          reader.readAsDataURL(response.data);
        });

        console.log(`✅ Thumbnail loaded for page ${pageNo}`);

        // Update Redux immediately
        dispatch(
          documentSlice.actions.setPageImage({
            docId,
            pageNo,
            thumbUrl: base64Data,
          }),
        );

        return { pageNo, success: true };
      } catch (error) {
        console.error(`❌ Failed to load thumbnail for page ${pageNo}:`, error);
        return { pageNo, success: false, error };
      }
    });

    // Wait for ALL thumbnails to complete
    console.log(`Waiting for ${loadPromises.length} thumbnail requests to complete...`);
    const results = await Promise.allSettled(loadPromises);

    // Log results
    const successful = results.filter((r) => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.length - successful;

    console.log(`Thumbnail loading completed: ${successful} successful, ${failed} failed`);

    // Mark loading as complete
    dispatch(documentSlice.actions.setThumbnailsLoading({ docId, isLoading: false }));
  };

export const loadPageImage =
  (inboxId: string, docId: string, pageNo: number): AppThunk =>
  async (dispatch, getState) => {
    console.log(`Loading full image for page ${pageNo} of document ${docId}`);

    // Check if already loaded
    const state = getState();
    const pageImagesMap = state.document.pageImagesMap;
    if (pageImagesMap?.[docId]?.[pageNo]?.imageUrl) {
      console.log(`Full image for page ${pageNo} already loaded`);
      return;
    }

    // Set loading state
    dispatch(documentSlice.actions.setPageImageLoading({ docId, pageNo, isLoading: true }));

    try {
      console.log(`Starting full image load for page ${pageNo}`);
      const url = `${import.meta.env.VITE_PAPERBOX_CDN_URL}/inboxes/${inboxId}/documents/${docId}/pages/${pageNo}?w=2000`;

      const response = await apiClient.get(url, {
        responseType: 'blob',
        timeout: 30000,
        headers: { accept: 'image/*' },
      });

      // Convert to base64
      const base64Data = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(response.data);
      });

      console.log(`✅ Full image loaded for page ${pageNo}`);

      // Update Redux immediately
      dispatch(
        documentSlice.actions.setPageImage({
          docId,
          pageNo,
          imageUrl: base64Data,
        }),
      );
    } catch (error) {
      console.error(`❌ Failed to load full image for page ${pageNo}:`, error);
    } finally {
      // Always clear loading state
      dispatch(documentSlice.actions.setPageImageLoading({ docId, pageNo, isLoading: false }));
    }
  };

// Cleanup function for when documents are unloaded
export const cleanupDocumentImages =
  (docId: string): AppThunk =>
  async (dispatch) => {
    console.log(`Cleaning up images for document: ${docId}`);

    // Clear cache for this document
    clearImageCache(docId);

    // Clear Redux state
    dispatch(documentSlice.actions.clearDocumentImages(docId));
  };
