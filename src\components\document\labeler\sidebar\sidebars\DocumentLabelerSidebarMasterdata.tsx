import Search from '@components/document/labeler/sidebar/search/Search.tsx';
import s from '@components/document/labeler/sidebar/sidebar.module.scss';
import DocumentLabelerSidebarField from '@components/document/labeler/sidebar/sidebars/items/DocumentLabelerSidebarField.tsx';
import { useGetMasterDataMappingsQuery } from '@shared/helpers/rtk-query/firestoreApi.ts';
import { useDocumentOperations } from '@shared/hooks/useDocumentOperations.ts';
import { useNotification } from '@shared/hooks/useNotificationBar.tsx';
import { selectActiveDocument } from '@shared/store/documentSlice';
import { useSelector } from '@shared/store/store.ts';
import { ReactComponent as ChevronDown } from '@svg/chevron-down.svg';
import { DotPulse } from '@uiball/loaders';
import clsx from 'clsx';
import { t } from 'i18next';
import React, { useMemo, useState } from 'react';
import { useParams } from 'react-router';

interface Props {}

const DocumentLabelerSidebarMasterdata: React.FC<Props> = () => {
  const { inboxId } = useParams();
  const [activeTable, setActiveTable] = useState('all');
  const [isLinkedItemsOpen, setIsLinkedItemsOpen] = useState(true);
  const [isUnlinking, setIsUnlinking] = useState(false);

  const { unLinkMasterdata } = useDocumentOperations();
  const { showNotification } = useNotification();

  // Wrapper function to handle loading state and errors
  const handleUnlinkMasterdata = async () => {
    setIsUnlinking(true);

    try {
      await unLinkMasterdata();
    } catch (error) {
      console.error('Error unlinking masterdata:', error);
      // Show error notification only
      showNotification(t('document:masterdata.unlinkError'), 'error');
    } finally {
      setIsUnlinking(false);
    }
  };

  // Get active document from Redux store
  const activeDocument = useSelector(selectActiveDocument);

  const masterdataMappings = useGetMasterDataMappingsQuery({ inboxId }).data;

  const tables = useMemo(() => {
    if (!masterdataMappings) return [];
    return Object.entries(masterdataMappings).map(([_, value]) => ({
      name: value.name,
      data: value,
    }));
  }, [masterdataMappings]);

  const selectedTableIds = useMemo(() => {
    if (tables.length === 0) return [];

    if (activeTable === 'all') return tables.map((table) => table.data.id);
    return [tables.find((e) => e.name === activeTable).data.id];
  }, [activeTable, tables]);

  // Filter entities with source === 'masterdata'
  const linkedEntities = useMemo(() => {
    if (!activeDocument?.entities) return [];
    return activeDocument.entities.filter((entity) => entity.source === 'masterdata');
  }, [activeDocument?.entities]);

  return (
    <>
      {linkedEntities.length > 0 && (
        <div className={s.section_wrapper}>
          <div className={s.section}>
            <div className={clsx(s.header)} onClick={() => setIsLinkedItemsOpen(!isLinkedItemsOpen)}>
              <h2>Linked Items</h2>
              <ChevronDown className={clsx({ [s.chevron_up]: isLinkedItemsOpen })} />
            </div>
            {isLinkedItemsOpen && (
              <div className={s.section_content}>
                {linkedEntities.map((entity) => (
                  <DocumentLabelerSidebarField key={entity.id} entity={entity} />
                ))}
                {linkedEntities.length === 0 && <div className={s.no_items}>No linked items found</div>}
              </div>
            )}
          </div>
        </div>
      )}
      <div className={s.section_wrapper}>
        <div className={s.section}>
          <div className={s.header}>
            <h2>Masterdata</h2>
          </div>
          <div className={s.section_content}>
            {linkedEntities.length > 0 ? (
              // Show Unlink button when linked fields are present
              <button className={s.unlink_button} onClick={handleUnlinkMasterdata} disabled={isUnlinking}>
                {isUnlinking ? (
                  <DotPulse size={20} speed={1.3} color="#e74c3c" />
                ) : (
                  t('document:masterdata.clearAll') || 'Unlink Masterdata'
                )}
              </button>
            ) : (
              <>
                <div className={s.tabs}>
                  <button
                    className={clsx(s.tab, { [s.active]: activeTable === 'all' })}
                    onClick={() => setActiveTable('all')}
                  >
                    <span>Alle</span>
                  </button>

                  {tables.map((table) => (
                    <button
                      key={table.name}
                      className={clsx(s.tab, { [s.active]: activeTable === table.name })}
                      onClick={() => setActiveTable(table.name)}
                    >
                      <span>{table.name}</span>
                    </button>
                  ))}
                </div>
                <Search selectedTableIds={selectedTableIds} />
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default DocumentLabelerSidebarMasterdata;
