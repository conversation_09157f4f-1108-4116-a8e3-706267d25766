@import "../../vars/_vars";


.grid {
  display: flex;
  overflow: hidden;
  height: calc(100vh - 50px);
  max-height: calc(100vh - 50px);
  background: $light-gray;

}


.sidebar {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-width: 365px;
  max-width: 365px;
  transition: all 0.2s ease-in-out;
  border-right: 1px solid $medium-gray;
  background: $white;

  &__masterdata {
    min-width: 425px;
    max-width: 425px;
  }
}


.placeholder {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 100%;

}


.delete_all {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  justify-content: center;
  margin: 8px auto;
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
  transition-property: color, background-color;
  text-align: center;
  color: $error;
  border-radius: 5px;
  background: white;

  svg {
    height: 12px;
    margin-top: -2px;
    margin-right: 8px;
    transition: color 0.2s ease-in-out;
    color: $error;

  }

  &:hover, &:focus {
    color: white;
    background: $error;

    svg {
      color: white;
    }
  }
}


.main {
  position: relative;
  display: flex;
  overflow: hidden;
  align-items: flex-start;
  flex-direction: column;
  justify-content: flex-start;
  width: 100%;
  height: 100%;
  outline: none;

}


.document {
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 1;
  justify-content: center;
  width: 100%;
  height: auto;
  max-height: calc(100vh - 115px);
  margin-top: 0;
  outline: none;
  justify-self: flex-start;

  :focus {
    outline: none;
  }

  ::selection {
    background: $paperbox-blue--fade;
  }
}


.scrollbar {
  position: absolute;
  z-index: 9998;
  top: 2.5%;
  right: 7px;
  overflow: hidden;
  width: 6px;
  height: 95%;
  border-radius: 5px;

  background: $medium-gray;
}


.scrollbar_scroller {
  position: absolute;
  width: 6px;
  height: 100px;
  transition: top 0.1s ease-in-out, height 0.1s ease-in-out;
  border-radius: 5px;
  background: $medium-dark-gray;
}


.document_page_actions_wrapper {
  position: fixed;
  z-index: 1000;
  right: 0;
  left: 365px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  will-change: opacity;

  &__faded {
    transition: 0.2s opacity;
    pointer-events: none;
    opacity: 0.1;
  }
}


.document_page_actions {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 400px;
  padding: 12px 10px;
  transition: 0.2s opacity;
  pointer-events: auto;
  border-radius: 5px;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 13, 33, 0.1);
  gap: 8px;

}


.loader {
  position: absolute;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;

  div {
    width: 100%;
    margin: 0;
  }
}


.scroller {
  position: absolute;
  z-index: 1000;
  top: 0;
  left: 50%;
  width: 20px;
  height: 20px;
}


.document_page_divider {
  width: 1px;
  height: 24px;
  background: rgba(0, 0, 0, 0.2);
  margin-inline: 10px;
}


.document_page_nav {
  display: flex;
  justify-content: center;
  cursor: pointer;
  color: $font-color-black;
  outline: none;

  & > * {
    width: 18px;
    height: 18px;

  }

  & > div {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &:hover {
    color: $paperbox-blue;
  }

  &:disabled {
    color: $medium-gray;

  }

}


.document_page_action {
  @include flex-center;
  /* Chrome, Safari, Edge, Opera */
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    margin: 0;
    -webkit-appearance: none;
  }

  /* Firefox */
  input[type=number] {
    -moz-appearance: textfield;
  }

  input {
    font-family: $base-font;
    font-size: 16px;
    width: 20px;
    border: none;
    border-bottom: 1px solid $font-color-black;
    outline: none;

  }
}


.document__wrapper {
  @include flex-center;
  width: 100%;
  height: 100%;
  max-height: calc(100% - 50px);
}


.document__placeholder {
  display: flex;
  overflow: auto;
  align-items: center;
  flex-direction: column;
  width: 100%;
  max-width: 1500px;
  max-height: calc(100% - 50px);
  margin: 100px 50px 0;
  transition: width 0.2s ease-in-out;
  background: white;
  box-shadow: 0 2px 10px rgba(0, 13, 33, 0.1);
}


.document__tools {
  position: absolute;
  z-index: 8;
  right: 20px;
  bottom: 200px;
  display: flex;
  flex-direction: column;
}


.document_actions {
  position: fixed;
  z-index: 8;
  right: 20px;
  display: flex;
  flex-direction: column;
  border-radius: 6px;

  will-change: opacity;

  &__faded {
    transition: 0.2s opacity;
    pointer-events: none;
    opacity: 0.1;
  }

  & > div:first-child:not(.button__abs) > button {
    border-radius: 6px 6px 0 0 !important;

  }

  & > div:last-child:not(.button__abs) > button {
    border-radius: 0 0 6px 6px !important;

  }

  .button__action {
    font-size: rem(20);
    font-weight: 900;
    width: 38px;
    height: 38px;
    color: $white;
    border-radius: 0 0 0 0;
    background: $dark-blue;
    box-shadow: 0 1px 10px rgba(0, 13, 33, 0.2);

    &:hover {
      color: $paperbox-blue;
    }

    &:active, &:focus {
      outline: none;
    }
  }

  .button__lock {

    width: 30px;
    height: 30px;
    border: none;
    border-radius: 6px !important;

    &__active {
      background: $paperbox-blue;

      &:hover {
        svg {
          color: $paperbox-blue-light
        }
      }
    }

    svg {
      width: 14px;
    }
  }

  .button__zoom {
    border-top: 1px solid $dark-gray;
    border-radius: 0;
  }

}


.button__download {
  border-top: 1px solid $dark-gray;

  svg {
    width: 18px;
    height: auto;
  }
}


.button__fit {
  border-top: 1px solid $dark-gray;

  svg {
    width: 22px;
    height: auto;
  }
}


.button__rotate {
  border-top: 1px solid $dark-gray;

  svg {
    width: 18px;
    height: auto;
  }
}


.button__action__wrapper {
  position: relative;

}


.popup__container {
  position: absolute;
  z-index: 100;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  padding: 10px;
  color: $white;
  border-radius: 5px;
  background: white;
  background: $dark-blue;
  box-shadow: 0 2px 10px rgba(0, 13, 33, 0.20);

}


.popup__option {
  text-transform: capitalize;
}


.popup__dropdown {
  height: 28px;
  padding: 5px 3px;
  text-transform: capitalize;
  border-radius: 5px 0 0 5px;

  &:focus {
    outline: none;
  }
}


.popup__title {
  font-family: $base-font;
  font-size: rem(16);
  font-weight: 500;
  margin-bottom: 8px;
  text-align: center;
}


.popup__create {
  display: flex;
}


.popup__button {
  font-size: rem(17);
  font-weight: 800;
  width: 28px;
  height: 28px;
  border-radius: 0 5px 5px 0;
  background: $paperbox-blue;
  background: $paperbox-blue;
}


.media__delete {
  width: 0;
  transition: all 0.3s ease-in-out;
  opacity: 0;
}


.media__entity {
  font-family: $base-font;
  font-size: rem(13);
  font-weight: 600;
  display: flex;
  align-items: center;
  max-height: 29px;
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  color: $paperbox-blue-dark;
  border-radius: 5px;
  background: rgba($paperbox-blue, 0.2);
  will-change: padding;

  &:hover {
    padding-right: 10px;
    padding-left: 9px;
    transition: padding 0.2s ease-in-out;

    .media__delete {
      width: 17px;
      height: 14px;
      margin-top: -2px;
      margin-right: -4px;
      transition: all 0.2s ease-in-out;
      opacity: 1;

      path {
        fill: $medium-dark-gray;
      }

      &:hover {
        path {
          fill: $error;
        }
      }
    }
  }
}


.media__entity_active {
  padding-right: 10px;
  padding-left: 9px;
  transition: padding 0.2s ease-in-out;
  color: white;
  background: rgba($paperbox-blue, 1);

  &:hover {
    .media__delete path {
      fill: $white;
    }
  }

  .media__delete {
    width: 17px;
    height: 14px;
    margin-top: -2px;
    margin-right: -4px;
    transition: all 0.2s ease-in-out;
    opacity: 1;

    path {
      fill: $white;
    }

    &:hover {
      path {
        fill: $error;
      }
    }

  }
}


.media__entity_grid {
  display: flex;
  flex-wrap: wrap;
  width: auto;
  padding: 20px;
}


.media__sidebar {
  font-family: $base-font;
}


.media__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 20px;
  cursor: pointer;
  border-top: 1px solid $medium-light-gray;
  border-bottom: 1px solid $medium-light-gray;
  background: $light-gray;
}


.media__subitem {
  background: $white;
}


.media__title {
  font-size: rem(14);
  font-weight: 500;
  text-transform: capitalize;
}


.media__count {
  @include flex-center;
  font-size: rem(11);
  font-weight: 700;
  height: 20px;
  margin-right: 15px;
  margin-left: auto;
  padding: 6px 12px 6px 12px;
  transition: all 0.3s ease-in-out;
  color: $font-color-black;
  border-radius: 50px;
  background: $medium-gray;
}


.media__arrow {
  transition: transform 0.2s ease-in-out;
  transform: rotate(0deg);

  path {
    fill: $dark-gray;
  }
}


.media__arrow_active {
  transition: transform 0.2s ease-in-out;
  transform: rotate(180deg);
}


.hide {
  position: absolute;
  z-index: 10;
  display: flex;
  overflow: hidden;
  align-items: flex-start;
  justify-content: center;
  width: 40px;
  height: 16px;
  cursor: pointer;
  transform: translateY(-32px);
  pointer-events: all;
  border-radius: 5px 5px 0 0;

  background: white;
  box-shadow: 0 2px 6px rgba(0, 13, 33, 0.10);
  clip-path: inset(-20px -20px 0px -20px);

  &__hidden {
    height: 26px;
    box-shadow: 0 2px 6px rgba(0, 13, 33, 0.20);

    svg {
      transform: rotate(180deg);
    }
  }

}


.page_search {
  position: absolute;
  display: flex;
  overflow: hidden;
  justify-content: space-between;
  width: 400px;
  height: 59px;
  padding: 5px 5px 15px;
  transition: opacity 0.1s, transform 0.2s;
  pointer-events: auto;
  opacity: 0;
  border-radius: 5px;
  background: #FAFAFA;
  box-shadow: 0 2px 10px rgba(0, 13, 33, 0.1);

  &__active {
    transform: translateY(-50px);
    opacity: 1;

  }

}


.input_wrapper {
  position: relative;
}


.input {
  font-size: 13px;
  flex: 1;
  width: auto;
  min-width: 283px;
  height: 44px;
  margin-top: 0;
  padding: 6px 10px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;

  &:focus {
    outline: none;
  }

}


.counter {
  font-size: 14px;
  position: absolute;
  top: 15px;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  padding: 0 10px;
}


.counter_nav {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 44px;

  &:hover {
    color: $paperbox-blue;
  }

  &__delete svg {
    color: black;

    &:hover {
      color: $error;

    }
  }
}
