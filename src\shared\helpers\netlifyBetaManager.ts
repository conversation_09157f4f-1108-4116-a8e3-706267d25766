/**
 * Utility functions for managing Netlify beta cookies based on tenant configuration
 */

/**
 * Gets the current Netlify beta cookie value
 */
export const getNetlifyBetaCookie = (): string | null => {
  const cookie = document.cookie.split('; ').find((row) => row.startsWith('nf_ab='));
  return cookie ? cookie.split('=')[1] : null;
};

/**
 * Sets the Netlify beta cookie
 */
export const setNetlifyBetaCookie = (): void => {
  const expires = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toUTCString();
  document.cookie = `nf_ab=beta; path=/; expires=${expires};`;
};

/**
 * Sets the Netlify master cookie (disables beta)
 */
export const setNetlifyMasterCookie = (): void => {
  const expires = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toUTCString();
  document.cookie = `nf_ab=master; path=/; expires=${expires};`;
};

/**
 * Removes the Netlify beta cookie
 */
export const removeNetlifyBetaCookie = (): void => {
  document.cookie = 'nf_ab=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT;';
};

/**
 * Gets the personal beta override from localStorage
 */
export const getPersonalBetaOverride = (): boolean | null => {
  const override = localStorage.getItem('personalBetaOverride');
  return override !== null ? JSON.parse(override) : null;
};

/**
 * Sets the personal beta override in localStorage
 */
export const setPersonalBetaOverride = (value: boolean): void => {
  localStorage.setItem('personalBetaOverride', JSON.stringify(value));
};

/**
 * Removes the personal beta override from localStorage
 */
export const removePersonalBetaOverride = (): void => {
  localStorage.removeItem('personalBetaOverride');
};

/**
 * Checks if the current cookie state matches the desired beta state
 */
export const isNetlifyBetaCookieCorrect = (shouldBeBeta: boolean): boolean => {
  const currentCookie = getNetlifyBetaCookie();

  if (shouldBeBeta) {
    // Should be beta: cookie should exist and not be 'master' and not be a number
    return currentCookie && currentCookie !== 'master' && !Number(currentCookie);
  } else {
    // Should not be beta: cookie should be null, 'master', or a number
    return !currentCookie || currentCookie === 'master' || Boolean(Number(currentCookie));
  }
};

/**
 * Updates the Netlify beta cookie based on tenant configuration
 * Returns true if a page reload is needed
 * Checks for personal override first before applying tenant settings
 */
export const updateNetlifyBetaFromTenant = (netlifyBeta?: boolean): boolean => {
  // Check for personal override first
  const personalOverride = getPersonalBetaOverride();

  // If personal override exists, use that value instead of tenant setting
  const shouldBeBeta = personalOverride !== null ? personalOverride : netlifyBeta === true;
  const isCurrentlyCorrect = isNetlifyBetaCookieCorrect(shouldBeBeta);

  if (isCurrentlyCorrect) {
    return false; // No change needed
  }

  // Update the cookie
  if (shouldBeBeta) {
    setNetlifyBetaCookie();
  } else {
    setNetlifyMasterCookie();
  }

  return true; // Page reload needed
};
