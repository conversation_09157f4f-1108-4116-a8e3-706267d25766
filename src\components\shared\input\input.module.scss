@import "@shared/styles/vars/_vars";


.input {
  font-family: $base-font;
  font-size: 14px;
  line-height: 1.25;
  box-sizing: border-box;
  width: 100%;
  height: 50px;
  padding: 6px 15px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: $light-gray;
  background: #FAFAFA;

  &:active, &:focus {
    border: 1px solid $paperbox-blue;
    outline: none;
  }

  &::placeholder {
    color: #AAAAAA;
  }
}


.input__icon {
  position: absolute;
  top: 16px;
  right: 30px;
}


.input_error {
  border: 1px solid $error !important;
  background: rgba($error, 0.05);
}


.error {
  font-size: rem(13);
  margin-top: 8px;
  color: $error;
}


.entry {
  position: relative;
  display: flex;
  flex-direction: column;

  & + & {
    margin-top: 20px;
  }
}


.label {
  font-family: $base-font;
  margin-bottom: 10px;
  color: $font-color-blue;
}
