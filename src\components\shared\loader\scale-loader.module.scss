@import "@shared/styles/vars/_vars";


.spinner {
  font-size: 10px;
  height: var(--scale-height);
  text-align: center;
}


.spinner > div {
  display: inline-block;
  width: var(--scale-width);
  height: 100%;
  margin-right: calc(var(--scale-width) / 1.4);
  -webkit-animation: sk-stretchdelay 0.9s infinite ease-in-out;
  animation: sk-stretchdelay 0.9s infinite ease-in-out;

  border-radius: 2px;
  background-color: $paperbox-blue;
}


.spinner .rect2 {
  -webkit-animation-delay: -0.9s;
  animation-delay: -0.9s;
}


.spinner .rect3 {
  -webkit-animation-delay: -0.8s;
  animation-delay: -0.8s;
}


.spinner .rect4 {
  -webkit-animation-delay: -0.7s;
  animation-delay: -0.7s;
}


.spinner .rect5 {
  -webkit-animation-delay: -0.6s;
  animation-delay: -0.6s;
}


@-webkit-keyframes sk-stretchdelay {
  0%, 100% {
    -webkit-transform: scaleY(0.5)
  }
  20%, 40% {
    -webkit-transform: scaleY(1.0)
  }
}


@keyframes sk-stretchdelay {
  0%, 100% {
    transform: scaleY(0.5);
    -webkit-transform: scaleY(0.5);
  }
  20%, 40% {
    transform: scaleY(1.0);
    -webkit-transform: scaleY(1.0);
  }
}
