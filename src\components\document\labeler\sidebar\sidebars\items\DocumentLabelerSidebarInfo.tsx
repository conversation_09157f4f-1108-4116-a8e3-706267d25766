import s from '@components/document/labeler/sidebar/sidebar.module.scss';
import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import clsx from 'clsx';
import React from 'react';

interface Props {
  value: string;
  label: string;
}

const DocumentLabelerSidebarInfo: React.FC<Props> = ({ value, label }) => {
  return (
    <Tooltip
      shouldShow={() => value.length > 10}
      content={value}
      tooltipStyle={{ maxWidth: '300px' }}
      position="right"
    >
      <div className={clsx(s.row_item, s.row_info)}>
        <div className={s.row_item_left} style={{ width: '100%', minWidth: 'min-content' }}>
          <span>{label}</span>
        </div>

        <div className={s.row_item_right}>
          <span className={clsx(s.value, s.value__no_edit)}>{value}</span>
        </div>
      </div>
    </Tooltip>
  );
};

export default DocumentLabelerSidebarInfo;
