import { useTranslation } from 'react-i18next';

/**
 * Type definition for a translatable description
 */
export type TranslatableDescription = string | {
  key: string;
  values?: Record<string, any>;
  fallback?: string;
};

/**
 * Hook to translate a description that can be either a string or a translation object
 * @param description The description to translate
 * @returns The translated description
 */
export function useTranslatedDescription(description?: TranslatableDescription): string | undefined {
  const { t } = useTranslation();
  
  if (!description) {
    return undefined;
  }
  
  if (typeof description === 'string') {
    return description;
  }
  
  // It's a translation object
  try {
    const translatedText = t(description.key, description.values || {});
    
    // If the translation key doesn't exist, i18next will return the key itself
    // In that case, use the fallback if provided
    if (translatedText === description.key && description.fallback) {
      return description.fallback;
    }
    
    return translatedText;
  } catch (error) {
    console.error('Translation error:', error);
    return description.fallback || description.key;
  }
}

/**
 * Function to translate a description without using hooks (for non-React contexts)
 * @param t The translation function from i18next
 * @param description The description to translate
 * @returns The translated description
 */
export function getTranslatedDescription(
  t: (key: string, options?: any) => string,
  description?: TranslatableDescription
): string | undefined {
  if (!description) {
    return undefined;
  }
  
  if (typeof description === 'string') {
    return description;
  }
  
  // It's a translation object
  try {
    const translatedText = t(description.key, description.values || {});
    
    // If the translation key doesn't exist, i18next will return the key itself
    // In that case, use the fallback if provided
    if (translatedText === description.key && description.fallback) {
      return description.fallback;
    }
    
    return translatedText;
  } catch (error) {
    console.error('Translation error:', error);
    return description.fallback || description.key;
  }
}

/**
 * Function to format a description with values without translation
 * Useful for testing or when translation is not available
 * @param description The description to format
 * @returns The formatted description
 */
export function formatDescription(description?: TranslatableDescription): string | undefined {
  if (!description) {
    return undefined;
  }
  
  if (typeof description === 'string') {
    return description;
  }
  
  // It's a translation object, but we'll just use the fallback or key
  return description.fallback || description.key;
}
