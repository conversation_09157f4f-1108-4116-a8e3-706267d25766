import SearchResults from '@components/document/labeler/sidebar/search/SearchResults.tsx';
import { DropdownOption } from '@components/shared/dropdown/StyledSelect';
import { IDocumentEnrichedEntity } from '@shared/helpers/converters/document.ts';
import { sleep, uuid4hex } from '@shared/helpers/helpers';
import {
  useGetFieldtypesQuery,
  useGetMasterDataMappingsQuery,
  useGetMetadataTypesQuery,
} from '@shared/helpers/rtk-query/firestoreApi.ts';
import { UrlParams } from '@shared/models/generic';
import { searchMasterData } from '@shared/services/documentService';
import { useSelector } from '@shared/store/store.ts';
import s from '@shared/styles/component/document/document-labeler-sidebar-search.module.scss';
import { ReactComponent as CrossIcon } from '@svg/cross-icon.svg';
import { ReactComponent as SearchIcon } from '@svg/search-icon.svg';
import { Pulsar } from '@uiball/loaders';
import clsx from 'clsx';
import React, { useEffect, useMemo, useRef, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';
import SearchRow from './SearchRow';

export type Field = {
  id: string;
  prompt: string;
  reference?: string;
  reference_type?: string;
  tableId?: string;
};

type FieldState = {
  fields: Field[];
};

interface Props {
  hasMasterdataLink?: boolean;
  selectedTableIds?: string[];
}

const Search: React.FC<Props> = ({ hasMasterdataLink, selectedTableIds = [] }) => {
  const { inboxId, docId }: UrlParams = useParams();
  const { t } = useTranslation();

  const entityTypes = useGetFieldtypesQuery({ inboxId }).data;
  const metadataTypes = useGetMetadataTypesQuery({ inboxId }).data;
  const masterDataMappings = useGetMasterDataMappingsQuery({ inboxId }).data;
  const creatingEntity = useSelector((state) => state.labeler.creatingEntity);
  const activeDocument = useSelector((state) => state.document.activeDocument);
  const activeEntityPair = useSelector((state) => state.labeler.activeEntityPair);

  // Local state for search fields, status, and results.
  const [fieldState, setFieldState] = useState<FieldState>({
    fields: [{ id: 'first', prompt: '', reference_type: 'global', reference: 'global' }],
  });
  const [searchStatus, setSearchStatus] = useState<'idle' | 'searching' | 'error' | 'no-results'>('idle');
  const [searchResults, setSearchResults] = useState<any[] | null>(null);

  const formRef = useRef<HTMLFormElement>(null);

  // --- Compute analytics options in a memoized way ---
  const computedAnalyticsOptions = useMemo(() => {
    if (!masterDataMappings) return {};
    const analyticsList: Record<string, any[]> = {};
    // Filter mappings to only those for the selected table IDs.
    const filteredMappings = Object.entries(masterDataMappings)
      .filter(([key]) => selectedTableIds.includes(key))
      .flatMap(([_, value]) => value);

    const mappedAnalytics = filteredMappings.flatMap((table: any) => {
      if (!table.analytics) return [];
      return table.analytics.map((analytic: any) => {
        const mappedItem = table.mapping.find((entry: any) => entry.id === analytic.tableId);
        return { ...analytic, id: mappedItem?.reference };
      });
    });
    const groupedById: Record<string, any[]> = {};
    mappedAnalytics.forEach((analytic: any) => {
      const filteredList = analytic.uniqueValues.filter((v: any) => v !== null && v !== '');
      if (!filteredList.length) return;
      if (!groupedById[analytic.id]) {
        groupedById[analytic.id] = [filteredList];
      } else {
        groupedById[analytic.id].push(filteredList);
      }
    });
    Object.entries(groupedById)
      .filter(([_, values]) => values.length === selectedTableIds.length)
      .forEach(([key, values]) => {
        analyticsList[key] = values.flat();
      });
    return analyticsList;
  }, [masterDataMappings, selectedTableIds]);

  // --- Compute all available options (for pill searchability) ---
  const allAvailableOptions = useMemo(() => {
    let allOptions: DropdownOption[] = [
      { label: t('document:masterdata.global'), value: { id: 'global', type: 'global' } },
    ];
    if (!masterDataMappings) return allOptions;

    // Create merged mappings with table information (similar to SearchRow logic)
    const mergedMappings = Object.values(masterDataMappings).flatMap(
      (mapping: any) => mapping.mapping?.map((e: any) => ({ ...e, tableName: mapping.name })) || [],
    );
    const spreadMappings = mergedMappings.filter((e: any) => e.isSearchable !== false);

    const addFilteredOptions = (source: any[], referenceType: string) => {
      const filtered = source.filter((item) =>
        spreadMappings.find(
          (mapping: any) =>
            mapping.reference === item.id && mapping.referenceType === referenceType && !item.isArchived,
        ),
      );
      allOptions = [
        ...allOptions,
        ...filtered.map((item) => {
          return {
            label: item.name,
            value: {
              id: item.id,
              type: referenceType,
            },
          };
        }),
      ];
    };

    if (entityTypes) {
      addFilteredOptions(entityTypes, 'entity_types');
    }
    if (metadataTypes) {
      addFilteredOptions(metadataTypes, 'metadata_keys');
    }
    return allOptions;
  }, [t, masterDataMappings, entityTypes, metadataTypes]);

  // --- Compute dropdown options (filtered for dropdown usage) ---
  const computedDropdownOptions = useMemo(() => {
    if (fieldState.fields) {
      return allAvailableOptions.filter(
        (option) => !fieldState.fields.find((field) => field.reference === option.value.id),
      );
    }
    return allAvailableOptions;
  }, [allAvailableOptions, fieldState.fields]);

  const canAddQuery = computedDropdownOptions.length > 0;

  // --- Handlers for search fields ---
  const handleChange = (field: Field) => {
    setFieldState((prev) => {
      const updated = prev.fields.map((f) => (f.id === field.id ? field : f));
      return { fields: updated };
    });
  };

  const handleAddQuery = () => {
    setFieldState((prev) => ({
      fields: [...prev.fields, { id: uuid4hex(), prompt: '', reference_type: '', reference: '' }],
    }));
  };

  const handleRemoveQuery = (id: string) => {
    setFieldState((prev) => ({
      fields: prev.fields.filter((f) => f.id !== id),
    }));
  };

  // Handle pill click - intelligently updates existing rows or creates new ones
  const handlePillClick = useCallback(
    (entity: IDocumentEnrichedEntity) => {
      // Get the entity value as a string
      const entityValue =
        typeof entity.value === 'string' ? entity.value : entity.rawValue || entity.value?.toString() || '';

      if (!entityValue) return;

      // Find the matching option for this entity type from all available options
      const matchingOption = allAvailableOptions.find(
        (option) => option.value.id === entity.type && option.value.type === 'entity_types',
      );

      const metadataOption = allAvailableOptions.find(
        (option) => option.value.id === entity.type && option.value.type === 'metadata_keys',
      );

      const selectedOption = matchingOption || metadataOption;

      if (!selectedOption) {
        console.warn('No matching dropdown option found for entity type:', entity.type);
        return;
      }

      setFieldState((prev) => {
        const updatedFields = [...prev.fields];

        // First, check if there's already a search row with the same entity type
        const existingFieldIndex = updatedFields.findIndex((field) => field.reference === entity.type);

        if (existingFieldIndex !== -1) {
          // Update the existing field with the new value
          const existingField = updatedFields[existingFieldIndex];
          existingField.prompt = entityValue;
          existingField.reference = selectedOption.value.id;
          existingField.reference_type = selectedOption.value.type;
          if (selectedOption.value.tableId) {
            existingField.tableId = selectedOption.value.tableId;
          }
        } else {
          // No existing field with this entity type, check if we can use an empty field
          const lastField = updatedFields[updatedFields.length - 1];
          const isLastFieldEmpty = !lastField?.prompt || lastField.prompt.trim() === '';

          if (isLastFieldEmpty) {
            // Update the existing empty field
            if (lastField) {
              lastField.prompt = entityValue;
              lastField.reference = selectedOption.value.id;
              lastField.reference_type = selectedOption.value.type;
              if (selectedOption.value.tableId) {
                lastField.tableId = selectedOption.value.tableId;
              }
            }
          } else {
            // Last field has content, create a new field
            const newField: Field = {
              id: uuid4hex(),
              prompt: entityValue,
              reference: selectedOption.value.id,
              reference_type: selectedOption.value.type,
              tableId: selectedOption.value.tableId || undefined,
            };
            updatedFields.push(newField);
          }
        }

        return { fields: updatedFields };
      });
    },
    [allAvailableOptions],
  );

  const filteredPills = useMemo(() => {
    if (!activeDocument || !activeDocument.entities) return [];

    // Filter entities that have values and are searchable
    return activeDocument.entities.filter((entity) => {
      // Only show entities that have a value
      if (!entity.value || entity.value === null || entity.value === '') return false;

      // Only show entities that have type details (are properly enriched)
      if (!entity.typeDetails) return false;

      // Exclude temporary entities (like the one being created)
      if (entity.type === 'temp') return false;

      // Only show entities that can be used for searching
      // Check if the entity type is available in all options (either entity_types or metadata_keys)
      const isSearchable = allAvailableOptions.some(
        (option) => option.value.id === entity.type && (option.value.type === 'entity_types' || option.value.type === 'metadata_keys'),
      );

      if (!isSearchable) return false;

      // Hide pill if there's already a search row with the same entity type AND same value
      const entityValue = typeof entity.value === 'string' ? entity.value : entity.rawValue || entity.value?.toString() || '';
      const hasMatchingSearchRow = fieldState.fields.some((field) => {
        return field.reference === entity.type && field.prompt === entityValue;
      });

      return !hasMatchingSearchRow;
    });
  }, [activeDocument, allAvailableOptions, fieldState.fields]);

  useEffect(() => {
    setFieldState({
      fields: [{ id: 'first', prompt: '', reference_type: 'global', reference: 'global' }],
    });
  }, [docId]);

  // Update search term when creatingEntity is present
  useEffect(() => {
    if (creatingEntity?.value) {
      // Update the last query item with the entity value
      setFieldState((prev) => {
        const updatedFields = [...prev.fields];
        // Get the last field in the array
        const lastField = updatedFields[updatedFields.length - 1];

        if (lastField) {
          lastField.prompt =
            typeof creatingEntity.value === 'string' ? creatingEntity.value : creatingEntity.rawValue || '';
        }

        return { fields: updatedFields };
      });
    }
  }, [creatingEntity]);

  // Handle entity selection from document viewer - behaves like pill click
  useEffect(() => {
    if (activeEntityPair?.entityId && activeDocument?.entities) {
      // Find the selected entity in the document
      const selectedEntity = activeDocument.entities.find((entity) => entity.id === activeEntityPair.entityId);

      if (selectedEntity) {
        // For complex entities with childId, we need to extract the specific child value
        if (activeEntityPair.childId && selectedEntity.value && typeof selectedEntity.value === 'object') {
          const complexValue = selectedEntity.value as any;
          const childValue = complexValue.complex?.[activeEntityPair.childId];

          if (childValue) {
            // Create a simplified entity object for the child value
            const childEntity: IDocumentEnrichedEntity = {
              ...selectedEntity,
              type: activeEntityPair.childId, // Use childId as the type
              value: childValue.value || childValue,
              rawValue: childValue.rawValue || childValue.value || childValue,
            };
            handlePillClick(childEntity);
          }
        } else {
          // Handle simple entities - use the existing handlePillClick logic
          handlePillClick(selectedEntity);
        }
      }
    }
  }, [activeEntityPair, activeDocument, handlePillClick]);

  const hasExternalTable = useMemo(() => {
    return masterDataMappings
      ? Object.values(masterDataMappings).some((el: any) => el.type === 'external')
      : false;
  }, [masterDataMappings]);

  // --- Search Handler using service function ---
  const handleSearch = useCallback(
    async (e?: React.FormEvent, currentFields?: Field[]) => {
      e?.preventDefault();
      const fields = currentFields || fieldState.fields;
      const payload: any = { prompt: null, fields: [], table_ids: selectedTableIds };
      const global = fields.find((f) => f.reference === 'global');
      if (global?.prompt) {
        payload.prompt = global.prompt;
      }
      payload.fields = fields
        .filter((f) => f.reference !== 'global' && f.prompt !== '')
        .map((f) => {
          const { id, ...rest } = f;
          return computedAnalyticsOptions[f.tableId] ? { ...rest, prompt: `"${f.prompt}"` } : rest;
        });
      if (payload.fields.length === 0 && !payload.prompt) return;
      try {
        setSearchStatus('searching');
        const response = await searchMasterData(payload, inboxId);
        if (!response || response.length === 0) {
          setSearchStatus('no-results');
          setSearchResults(null);
          sleep(2000).then(() => {
            setSearchStatus('idle');
          });
        } else {
          setSearchResults(response);
          setSearchStatus('idle');
        }
      } catch (error) {
        console.log(error);
        setSearchStatus('error');
      }
    },
    [fieldState.fields, selectedTableIds, computedAnalyticsOptions, inboxId],
  );

  return (
    <>
      <div className={s.container}>
        {!hasMasterdataLink && !hasExternalTable && (
          <form ref={formRef} onSubmit={handleSearch} className={s.container}>
            {fieldState.fields.map((field) => (
              <SearchRow
                key={field.id}
                // onFocus={() => dispatch(documentSlice.actions.setIsMasterDataSearchActive(true))}
                fields={fieldState.fields}
                field={field}
                handleRemove={() => handleRemoveQuery(field.id)}
                handleChange={handleChange}
                handleSearch={handleSearch}
                analyticsOptions={computedAnalyticsOptions}
                searchStatus={searchStatus}
              />
            ))}
            {searchStatus === 'no-results' ? (
              <div className={clsx(s.bottom, s.bottom__expanded)}>
                <div className={s.no_results}>{t('document:masterdata.noResults')}</div>
              </div>
            ) : (
              <div className={clsx(s.bottom, { [s.bottom__expanded]: true })}>
                {canAddQuery ? (
                  <div className={s.add_query} onClick={handleAddQuery}>
                    + {t('document:masterdata.addQuery')}
                  </div>
                ) : (
                  <div className={s.add_query} />
                )}
                <div className={s.bottom_buttons}>
                  {searchResults && (
                    <button disabled type="button" className={s.search_button}>
                      <span>
                        {searchResults.length} {t('document:masterdata.results')}
                      </span>
                    </button>
                  )}
                  <button
                    type="button"
                    onClick={(e) => {
                      e.preventDefault();
                      setSearchStatus('idle');
                      setSearchResults(null);
                      setFieldState({
                        fields: [{ id: 'first', prompt: '', reference_type: 'global', reference: 'global' }],
                      });
                    }}
                    className={s.search_button}
                  >
                    <CrossIcon />
                    <span>{t('document:masterdata.clear')}</span>
                  </button>
                  <button
                    data-testid="masterdata-search-button"
                    onClick={handleSearch}
                    className={s.search_button}
                  >
                    {searchStatus === 'searching' ? <Pulsar color="#0085FF" size={17} /> : <SearchIcon />}
                    <span>{t('document:masterdata.search')}</span>
                  </button>
                </div>
              </div>
            )}
          </form>
        )}
      </div>
      {filteredPills.length > 0 && (
        <div className={s.pills}>
          {filteredPills.map((entity) => {
            const displayValue =
              typeof entity.value === 'string'
                ? entity.value
                : entity.rawValue || entity.value?.toString() || '';

            return (
              <div
                key={entity.id}
                className={clsx(s.pill, s.pill__clickable)}
                onClick={() => handlePillClick(entity)}
                title={`Click to search for "${displayValue}" in ${entity.typeDetails?.name || 'this field'}`}
              >
                <span className={s.pill__type}>{entity.typeDetails?.name}</span>
                <span className={s.pill__separator}>:</span>
                <span className={s.pill__value}>{displayValue}</span>
              </div>
            );
          })}
        </div>
      )}
      <SearchResults searchResults={searchResults} />
    </>
  );
};

export default Search;
