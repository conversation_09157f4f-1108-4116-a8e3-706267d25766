@import "../../vars/_vars";


.container {
  font-family: $base-font;
  font-size: rem(16);
  font-weight: 300;
  position: relative;
  display: flex;
  align-items: center;
  height: rem(32);
}


.title {
  margin-right: 8px;
  color: $white;
}


.icon {
  width: auto;
  height: 12px;
  max-height: 100%;
  margin-right: 7px;
  border-radius: 50px;


  path {
    fill: $dark-gray;
  }
}


.name {
  margin-right: rem(10);
  text-transform: capitalize;
  color: $white;
}


.dropdown__select {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}


.dropdown {
  font-family: $base-font;
  font-weight: 400;
  position: absolute;
  z-index: 1000;
  top: 25px;
  left: 20px;
  overflow: hidden;
  padding: 16px;
  color: $white;
  border-radius: 5px;
  background: rgba($dark-blue, 0.9);
  box-shadow: 0 2px 10px rgba(0, 13, 33, 0.10);
}


.dropdown__title {
  font-size: rem(14);
  font-weight: 600;
  color: $white;
}


.dropdown__arrow {
  width: auto;
  height: rem(8);
  transition: transform 0.2s ease-in-out;
  transform: rotate(180deg);
}


.dropdown__arrow_active {
  transition: transform 0.2s ease-in-out;
  transform: rotate(0deg);
}


.dropdown__item {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 16px;
  cursor: pointer;
  white-space: nowrap;
  text-wrap: none;


  &:hover {
    text-decoration: underline;
  }
}


.dropdown__text {
  font-family: $base-font;
  font-size: rem(14);
  text-transform: capitalize;
}


.dropdown__icon {
  width: auto;
  height: 10px;
  margin-right: 8px;
}


.modal {
  display: flex;
  flex-direction: column;
  width: 450px;
  margin: 0 auto 15vh;
  color: $font-color-black;
  border-radius: 5px;
  background-color: $white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);

}


.header {
  display: flex;
  justify-content: space-between;
  height: 55px;
  padding: 20px 30px;
  border-bottom: 1px solid rgba($dark-gray, 0.3);
}


.modal_title {
  font-size: rem(20);
  font-weight: 700;
}


.modal_description {

  font-size: rem(16);
  line-height: 1.25;

}


.modal_types {
  font-size: rem(16);
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: auto;
  margin: 15px auto 0;
  padding: 10px;
  border: 1px solid $medium-light-gray;
  background: $light-gray;


  svg {
    width: 30px;
    height: 25px;
    margin-top: -2px;
    padding: 0 6px;

  }
}


.modal_type {
  font-weight: 700;
  width: 50%;
  text-align: center;
  text-transform: capitalize;
  border-radius: 5px;
}


.modal_body {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding: 15px 30px 20px;
}


.modal_close {
  cursor: pointer;


  path {
    fill: $font-color-black;
  }


  &:hover {
    path {
      fill: $error;
    }

  }
}


.info {
  position: absolute;
  right: 30px;
  bottom: 27px;
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: center;


  .info_text {
    font-size: 14px;
    margin-right: 8px;
    color: $medium-dark-gray;


    &__active {
      color: $font-color-black
    }
  }
}


.modal_buttons {
  display: flex;
  justify-content: center;

}


.modal_button {
  font-size: rem(14);
  align-self: center;
  width: 120px;
  margin-top: 25px;

}


