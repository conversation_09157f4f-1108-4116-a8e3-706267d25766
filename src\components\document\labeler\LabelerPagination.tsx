import Tooltip from '@components/shared/tooltip/Tooltip';
import { usePrevious } from '@shared/helpers/helpers.ts';
import { useGetActiveInboxQuery } from '@shared/helpers/rtk-query/firestoreApi.ts';
import { PaginatedToken } from '@shared/models/document';
import { UrlParams } from '@shared/models/generic.ts';
import {
  selectActivePageNo,
  selectPageIndexMap,
  selectIsDocumentThumbnailsLoading,
} from '@shared/store/documentSlice';
import labelerSlice from '@shared/store/labelerSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store';
import s from '@shared/styles/component/document/document-labeler.module.scss';
import { ReactComponent as ChevronDoubleLeft } from '@svg/chevron-double-left.svg';
import { ReactComponent as ChrevronDown } from '@svg/chevron-down.svg';
import { ReactComponent as ChevronLeft } from '@svg/chevron-left.svg';
import { ReactComponent as CrossIcon } from '@svg/cross-icon.svg';
import { ReactComponent as DownloadIcon } from '@svg/download.svg';
import { ReactComponent as FitHorizontalIcon } from '@svg/fit-horizontal-icon.svg';
import { ReactComponent as FitVerticalIcon } from '@svg/fit-vertical-icon.svg';
import { ReactComponent as ImagePlaceholderIcon } from '@svg/image-placeholder.svg';
import { ReactComponent as MinusIcon } from '@svg/minus-icon.svg';
import { ReactComponent as PlusIcon } from '@svg/plus-large-icon.svg';
import { ReactComponent as RotateIcon } from '@svg/rotate-icon.svg';
import { ReactComponent as SearchIcon } from '@svg/search-icon.svg';
import { Pulsar } from '@uiball/loaders';
import clsx from 'clsx';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router';

interface Props {
  innerWidth: number;
  innerHeight: number;
  isMouseDown: boolean;
  isSelectionVisible: boolean;
  handleScale: (isPositive: boolean) => void;
  handleFitToScreen?: (fitType: 'all' | 'width' | 'height') => void;
  handleRotate?: () => void;
  handleDownload?: () => void;
  handleSetThumbsVisible?: () => void;
  setSearchQuery: (query: string) => void;
  searchQuery: string;
  paginatedTokens: PaginatedToken[];
  setActiveToken: (token: PaginatedToken) => void;
  handleSetPage: (newPageIndex: number) => void;
}

const LabelerPagination: React.FC<Props> = ({
  innerWidth,
  innerHeight,
  isMouseDown,
  isSelectionVisible,
  handleScale,
  handleFitToScreen,
  handleRotate,
  handleDownload,
  handleSetThumbsVisible,
  searchQuery,
  setSearchQuery,
  paginatedTokens,
  handleSetPage,
  setActiveToken,
}) => {
  const { inboxId, docId }: UrlParams = useParams();
  const activePageNo = useSelector(selectActivePageNo);
  const pageIndexMap = useSelector(selectPageIndexMap) ?? [];
  const { settings } = useGetActiveInboxQuery({ inboxId }).data ?? {};
  const { documentDownload } = settings ?? {};
  // Use the new thumbnail loading state from documentSlice
  const isThumbsLoading = useSelector(selectIsDocumentThumbnailsLoading(docId));
  const [isDownloading, setIsDownloading] = useState(false);

  const handleDownloadWithLoading = () => {
    if (!handleDownload || isDownloading) return;
    setIsDownloading(true);
    try {
      handleDownload();
      setTimeout(() => setIsDownloading(false), 1000);
    } catch (error) {
      setIsDownloading(false);
    }
  };

  const [pageInputValue, setPageInputValue] = useState<string>('1');
  const [isFitWidth, setIsFitWidth] = useState(false);
  const [activeTokenIndex, setActiveTokenIndex] = useState<number>(0);
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [isHidden, setIsHidden] = useState(false);

  const inputRef = useRef();
  const matches = useMemo(() => {
    if (searchQuery)
      return paginatedTokens?.filter((e) => e.text.toLowerCase().includes(searchQuery.toLowerCase())) ?? [];
    return [];
  }, [paginatedTokens, searchQuery]);

  useEffect(() => {
    if (matches.length > 0) {
      setActiveTokenIndex(0);
    }
  }, [matches]);

  useEffect(() => {
    setActiveToken(matches[activeTokenIndex]);
  }, [activeTokenIndex, matches, setActiveToken]);
  const dispatch = useDispatch();

  useEffect(() => {
    if (activePageNo !== null) {
      setPageInputValue((cur) => {
        if (pageIndexMap.findIndex((e) => e === activePageNo) !== -1) {
          const update = `${pageIndexMap.findIndex((e) => e === activePageNo) + 1}`;
          if (cur !== update) {
            //TODO: Double check
            dispatch(labelerSlice.actions.setActiveEntityPair(null));
            return update;
          }
        } else {
          let fallbackPageNo = pageIndexMap[Number.parseInt(cur) - 1];
          if (fallbackPageNo == null) fallbackPageNo = 1;
          return fallbackPageNo.toString();
        }
        return cur;
      });
    }
  }, [activePageNo, dispatch, pageIndexMap]);
  const prevPageInputValue = usePrevious(pageInputValue);

  useEffect(() => {
    if (prevPageInputValue === pageInputValue) return;
    if (pageInputValue == null) return;
    handleSetPage(pageIndexMap[Number.parseInt(pageInputValue) - 1]);
  }, [handleSetPage, pageIndexMap, pageInputValue, prevPageInputValue]);

  const handleSearch = (e: KeyboardEvent) => {
    if ((e.ctrlKey && e.code === 'KeyF') || (e.code === 'KeyF' && e.metaKey)) {
      e.preventDefault();
      setIsSearchActive(true);
      if (inputRef.current) {
        const input = inputRef.current as HTMLInputElement;
        input.focus();
      }
    }
  };
  useEffect(() => {
    if (!isSearchActive) {
      setSearchQuery('');
    }
  }, [isSearchActive, setSearchQuery]);

  useEffect(() => {
    document.addEventListener('keydown', handleSearch);
    return () => {
      document.removeEventListener('keydown', handleSearch);
    };
  }, []);

  const handleTokenNav = (isNext: boolean) => {
    let newValue: number;
    if (isNext) {
      if (activeTokenIndex === matches.length - 1) {
        newValue = 0;
      } else {
        newValue = activeTokenIndex + 1;
      }
    } else {
      if (activeTokenIndex === 0) {
        newValue = matches.length - 1;
      } else {
        newValue = activeTokenIndex - 1;
      }
    }
    setActiveTokenIndex(newValue);
  };
  const handleChangeBack = () => {
    const currentMappedIndex = pageIndexMap.findIndex((e) => e === activePageNo);
    const nextPageNo = pageIndexMap[currentMappedIndex - 1];
    handleSetPage(nextPageNo);
  };
  const handleChangeForward = () => {
    const currentMappedIndex = pageIndexMap.findIndex((e) => e === activePageNo);
    const nextPageNo = pageIndexMap[currentMappedIndex + 1];
    handleSetPage(nextPageNo);
  };
  const searchText = useMemo(() => {
    if (searchQuery == null || searchQuery === '') {
      return '';
    }
    if (matches.length === 0) return 'No matches';
    return `${activeTokenIndex + 1}/${matches.length}`;
  }, [activeTokenIndex, matches, searchQuery]);
  const wrapperRef = useRef();

  const wrapperStyle = useMemo(() => {
    const wrapper = wrapperRef.current as HTMLDivElement;
    if (!wrapper) return {};
    const parent = wrapper.parentElement as HTMLDivElement;
    const parentRect = parent.getBoundingClientRect();
    let style = {
      left: parentRect.left,
      width: innerWidth,
      transform: 'translateY(20px)',
      top: innerHeight ?? 2000,
      transition: 'transform 0.2s , left 0.2s',
    } as React.CSSProperties;
    if (isHidden) {
      style = {
        ...style,
        transform: ' translateY(90px)',
      };
    }
    return style;
  }, [isHidden, innerHeight, wrapperRef, innerWidth]);

  return (
    <div
      ref={wrapperRef}
      className={clsx(s.document_page_actions_wrapper, {
        [s.document_page_actions_wrapper__faded]: isMouseDown && isSelectionVisible,
      })}
      style={wrapperStyle}
    >
      <button onClick={() => setIsHidden(!isHidden)} className={clsx(s.hide, { [s.hide__hidden]: isHidden })}>
        <ChrevronDown />
      </button>
      <div className={clsx(s.page_search, { [s.page_search__active]: isSearchActive })}>
        <div className={s.input_wrapper}>
          <input
            value={searchQuery ?? ''}
            ref={inputRef}
            className={s.input}
            type="text"
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <div className={s.counter}>{searchText}</div>
        </div>
        <button className={s.counter_nav} onClick={() => handleTokenNav(false)}>
          <ChrevronDown style={{ transform: 'rotate(180deg)' }} />
        </button>
        <button className={s.counter_nav} onClick={() => handleTokenNav(true)}>
          <ChrevronDown />
        </button>
        <button
          onClick={() => setIsSearchActive(false)}
          className={clsx(s.counter_nav, s.counter_nav__delete)}
        >
          <CrossIcon />
        </button>
      </div>
      <div
        data-tour="pagination"
        className={clsx(s.document_page_actions, {
          [s.document_page_actions__faded]: isMouseDown && isSelectionVisible,
        })}
      >
        <button
          disabled={activePageNo === 1}
          className={s.document_page_nav}
          onClick={() => handleSetPage(1)}
        >
          <ChevronDoubleLeft />
        </button>
        <button
          disabled={activePageNo === 1}
          className={s.document_page_nav}
          onClick={() => handleChangeBack()}
        >
          <ChevronLeft />
        </button>

        <div className={s.document_page_action}>
          <input
            style={{
              width: pageInputValue ? pageInputValue.toString().length * 7 + 6 : 13,
            }}
            max={pageIndexMap?.length}
            type="number"
            value={pageInputValue}
            onKeyDown={(e) => {
              if (e.key === 'enter') {
                e.preventDefault();
                e.stopPropagation();
              }
            }}
            onChange={(event) => {
              if (
                Number.parseInt(event.target.value) >= 1 &&
                Number.parseInt(event.target.value) <= pageIndexMap.length
              ) {
                setPageInputValue(event.target.value);
              } else if (event.target.value === '') {
                setPageInputValue('');
              } else {
                // setPageInputValue(`${activePageNo}`);
              }
              return false;
            }}
          />
          /{pageIndexMap?.length ?? '?'}
        </div>

        <button
          disabled={pageIndexMap.findIndex((e) => e === activePageNo) + 1 === pageIndexMap.length}
          className={s.document_page_nav}
          onClick={() => handleChangeForward()}
        >
          <ChevronLeft style={{ transform: 'rotate(180deg)' }} />
        </button>
        <button
          disabled={activePageNo === pageIndexMap[pageIndexMap.length - 1]}
          className={s.document_page_nav}
          onClick={() => {
            handleSetPage(pageIndexMap[pageIndexMap.length - 1]);
          }}
        >
          <ChevronDoubleLeft style={{ transform: 'rotate(180deg)' }} />
        </button>
        <div className={s.document_page_divider} />
        <Tooltip delay={100} position={'top'} content={isFitWidth ? 'Fit Width' : 'Fit Height'}>
          <button
            data-testid={'fit-document'}
            className={clsx(s.document_page_nav)}
            onClick={() => {
              if (isFitWidth) {
                handleFitToScreen('height');
                setIsFitWidth(false);
              } else {
                handleFitToScreen('width');
                setIsFitWidth(true);
              }
            }}
          >
            {isFitWidth ? <FitHorizontalIcon /> : <FitVerticalIcon />}
          </button>
        </Tooltip>
        <Tooltip delay={100} position={'top'} content={'Rotate 90 degrees'}>
          <button className={clsx(s.document_page_nav)} onClick={handleRotate}>
            <RotateIcon />
          </button>
        </Tooltip>
        <Tooltip delay={100} position={'top'} content={'Zoom (Ctrl + scroll)'}>
          <button className={clsx(s.document_page_nav)} onClick={() => handleScale(true)}>
            <PlusIcon />
          </button>
        </Tooltip>
        <Tooltip delay={100} position={'top'} content={'Zoom (Ctrl + scroll)'}>
          <button className={clsx(s.document_page_nav)} onClick={() => handleScale(false)}>
            <MinusIcon />
          </button>
        </Tooltip>

        <div className={s.document_page_divider} />

        {documentDownload && (
          <Tooltip delay={100} position={'top'} content={'Download Original'}>
            <button className={clsx(s.document_page_nav)} onClick={handleDownloadWithLoading}>
              {isDownloading ? <Pulsar size={15} color="#0085ff" /> : <DownloadIcon />}
            </button>
          </Tooltip>
        )}

        {isThumbsLoading ? (
          <div className={s.document_page_nav}>
            <div>
              <Pulsar size={15} />
            </div>
          </div>
        ) : (
          <Tooltip delay={100} position={'top'} content={'Show / Hide Thumbnails'}>
            <button className={s.document_page_nav} onClick={() => handleSetThumbsVisible()}>
              <ImagePlaceholderIcon />
            </button>
          </Tooltip>
        )}
        <Tooltip delay={100} position={'top'} content={'Document Search'}>
          <button className={clsx(s.document_page_nav)} onClick={() => setIsSearchActive(true)}>
            <SearchIcon />
          </button>
        </Tooltip>
      </div>
    </div>
  );
};

export default LabelerPagination;
