import SearchResultCardSimple from '@components/document/labeler/sidebar/search/SearchResultCardSimple.tsx';
import StyledSelect from '@components/shared/dropdown/StyledSelect.tsx';
import Input from '@components/shared/input/Input.tsx';
import Modal from '@components/shared/modal/Modal.tsx';
import { MasterDataSearchPayload, searchMasterData } from '@shared/services/documentService.ts';
import { useDispatch, useSelector } from '@shared/store/store.ts';
import s from '@shared/styles/component/admin/admin-table-search.module.scss';
import { DotPulse } from '@uiball/loaders';
import { debounce } from 'lodash';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface Props {}

const AdminMasterdataTableSearch: React.FC<Props> = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const inboxId = useSelector((state) => state.admin.activeInboxId);
  const tables = useSelector((state) => state.admin.inboxMasterdataTables);

  const [searchInput, setSearchInput] = useState('');
  const [selectedTables, setSelectedTables] = useState(null);
  const [masterdataResults, setMasterdataResults] = useState(null);
  const [masterdataStatus, setMasterdataStatus] = useState('idle');

  const tableOptions = useMemo(() => {
    return tables.map((table) => {
      return {
        value: table.id,
        label: table.name,
      };
    });
  }, [tables]);

  useEffect(() => {
    if (selectedTables === null && tableOptions.length > 0) {
      setSelectedTables(tableOptions);
    }
  }, [selectedTables, tableOptions]);

  useEffect(() => {
    const debouncedHandleSearch = debounce(() => {
      const tableIds = selectedTables.map((e) => e.value);
      setMasterdataStatus('searching');
      const payload: MasterDataSearchPayload = {
        prompt: searchInput,
        fields: [],
        table_ids: tableIds,
      };
      searchMasterData(payload, inboxId).then((res) => {
        if (res.length === 0) {
          setMasterdataResults(null);
          setMasterdataStatus('no-results');
        } else {
          setMasterdataResults(res);
          setMasterdataStatus('idle');
        }
      });
    }, 300);

    debouncedHandleSearch();
    return () => {
      debouncedHandleSearch.cancel();
    };
  }, [dispatch, inboxId, searchInput, selectedTables]);

  useEffect(() => {
    return () => {
      setMasterdataResults(null);
    };
  }, []);

  return (
    <Modal>
      <div className={s.container}>
        <div className={s.header}>Masterdata Search</div>
        <div className={s.content}>
          <div className={s.search}>
            <Input
              value={searchInput}
              placeholder={t('admin:masterdata.tableSearchDescription')}
              setValue={(value) => setSearchInput(value)}
              id={''}
            />
            <StyledSelect
              value={selectedTables}
              options={tableOptions}
              onChange={(e) => {
                setSelectedTables(e as any);
              }}
              isMulti
            />
          </div>
          {(!masterdataResults || masterdataStatus === 'searching') && searchInput !== '' && (
            <div className={s.status}>
              {masterdataStatus === 'searching' && <DotPulse color={'#0085FF'} />}
              {masterdataStatus === 'no-results' && <div>No Results found</div>}
            </div>
          )}

          {masterdataResults && (
            <div className={s.results}>
              {masterdataResults?.map((e) => {
                return <SearchResultCardSimple result={e} key={e.id} />;
              })}
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

export default AdminMasterdataTableSearch;
