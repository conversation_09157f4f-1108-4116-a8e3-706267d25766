import Checkbox from '@components/shared/checkbox/Checkbox';
import ContentModal from '@components/shared/content-modal/ContentModal.tsx';
import StyledSelect from '@components/shared/dropdown/StyledSelect';
import { IClientBounceField } from '@shared/helpers/converters/bouncefield.ts';
import { useModal } from '@shared/hooks/useModal';
import s from '@shared/styles/component/document/document-type-switch.module.scss';
import clsx from 'clsx';
import { cloneDeep } from 'lodash';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
  handleBounce: (value: Record<string, any>) => Promise<any>;
  actionTypes: IClientBounceField[];
}

const DocumentBounceModal: React.FC<Props> = ({ handleBounce, actionTypes }) => {
  const { t } = useTranslation();
  const { closeModal } = useModal();
  const [fieldState, setFieldState] = useState<Record<string, any>>({});

  const setState = (key: string, value: any) => {
    setFieldState((prevState) => {
      const copy = cloneDeep(prevState);
      copy[key] = value;
      return copy;
    });
  };

  const handleSubmit = () => {
    return handleBounce(fieldState).then(() => closeModal());
  };

  useEffect(() => {
    if (!actionTypes) return;
    const initialState: Record<string, any> = {};
    actionTypes.forEach((action) => {
      if (action.type === 'choice' && action.options && action.options.length > 0) {
        initialState[action.id] = action.options[0].id;
      } else if (action.type === 'boolean') {
        initialState[action.id] = false;
      } else if (action.type === 'multi-choice') {
        initialState[action.id] = [];
      } else {
        initialState[action.id] = null;
      }
    });
    setFieldState(initialState);
  }, [actionTypes]);

  return (
    <ContentModal
      title={t('document:bounceAction.title')}
      description={t('document:bounceAction.description')}
      onClose={closeModal}
      onAction={handleSubmit}
      actionButtonText={t('document:bounceAction.button')}
      initialFocus
    >
      <div data-testid="bounce-modal-content" className={s.modal}>
        {actionTypes?.map((actionType) => {
          let actionTypeOptions = [];
          let currentValue;
          if (actionType.options) {
            actionTypeOptions = actionType.options.map((e) => ({
              label: e.name,
              value: e.id,
            }));
            currentValue = actionTypeOptions.find((e) => e.value === fieldState[actionType.id]);
          }
          return (
            <div
              key={actionType.id}
              className={clsx(s.group, {
                [s.group__horizontal]: actionType.type === 'boolean',
              })}
            >
              <span className={s.label}>{actionType.name}</span>
              {actionType.type === 'multi-choice' && (
                <StyledSelect
                  onChange={(e: any) =>
                    setState(
                      actionType.id,
                      e.map((option: any) => option.value),
                    )
                  }
                  isMulti
                  isLoading={!actionType.options}
                  value={currentValue}
                  options={actionTypeOptions}
                />
              )}
              {actionType.type === 'choice' && actionType.options && (
                <StyledSelect
                  defaultValue={actionTypeOptions[0]}
                  value={currentValue}
                  onChange={(e: any) => setState(actionType.id, e.value)}
                  options={actionTypeOptions}
                />
              )}
              {actionType.type === 'text' && (
                <textarea
                  className={s.input}
                  onChange={(e) => {
                    setState(actionType.id, e.target.value);
                  }}
                />
              )}
              {actionType.type === 'boolean' && (
                <Checkbox
                  checked={fieldState[actionType.id] ?? false}
                  onClick={() => setState(actionType.id, !fieldState[actionType.id])}
                />
              )}
            </div>
          );
        })}
      </div>
    </ContentModal>
  );
};

export default DocumentBounceModal;
