@import "../../vars/_vars";


.container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 5px 5px 5px 5px;
  border-radius: 5px;

}


.input_group {
  font-size: 14px;
  display: flex;
  align-items: center;
  width: 20%;
  min-width: 20%;
  //background: red;

  .icon {
    width: 20px;
    height: 20px;
    margin-left: 8px;
    cursor: pointer;

    color: $success;

    svg {
      width: auto;
      height: 16px;
      margin-top: 2px;

    }

    &__edit {
      color: #898B99;

      svg {
        width: auto;
        height: 12px;
        margin-top: 3px;
      }
    }
  }
}


.types {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  width: 100%;
  max-width: 75%;
  height: auto;
  margin-right: 10px;
  margin-left: auto;
  gap: 8px;

  &__empty {
    justify-content: flex-end;
  }
}


