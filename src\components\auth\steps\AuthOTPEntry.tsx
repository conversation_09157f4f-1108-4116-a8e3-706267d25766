import Recaptcha from '@components/auth/helpers/Recaptcha';
import AuthButton from '@components/auth/methods/AuthButton';
import { errorTextMap } from '@components/reset/PasswordReset';
import SuspenseLoader from '@components/shared/suspense-loader/SuspenseLoader';
import { TwoFactorCodeInput } from '@components/two-factor/TwoFactorCodeInput';
import { sleep } from '@shared/helpers/helpers';
import { auth } from '@shared/store/setup/firebase-setup';
import { useDispatch, useSelector } from '@shared/store/store';
import userSlice from '@shared/store/userSlice';
import s from '@shared/styles/component/auth/auth.module.scss';
import { ReactComponent as PaperboxLogo } from '@svg/paperbox-logo.svg';
import {
  PhoneAuthProvider,
  PhoneMultiFactorGenerator,
  getMultiFactorResolver,
  isSignInWithEmailLink,
  signInWithEmailLink,
} from 'firebase/auth';
import queryString from 'query-string';
import React, { useCallback, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';

const AuthOTPEntry: React.FC = () => {
  const storeTenantId = useSelector((state) => state.tenant.tenantId);
  const allowedDomains = useSelector((state) => state.tenant.details.settings.allowedDomains);

  const location = useLocation();
  const navigate = useNavigate();

  const tenantId = queryString.parse(location.search).tenantId as string;
  const email = queryString.parse(location.search).email as string;

  const [error, setError] = useState<string>(null);
  const dispatch = useDispatch();
  const [MFAResolver, setMFAResolver] = useState<any>();
  const [userHas2FA, setUserHas2FA] = useState(false);
  const [verificationId, setVerificationId] = useState<string>();
  const [smsCode, setSmsCode] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');

  const handleError = useCallback(
    (err) => {
      setError(err.code);
      dispatch(userSlice.actions.setIsAuthPopupActive(false));
      if (err.code === 'auth/multi-factor-auth-required') {
        // The user is a multi-factor user. Second factor challenge is required.
        const resolver = getMultiFactorResolver(auth, err);
        const appVerifier = window['recaptchaVerifier'];
        setMFAResolver(resolver);
        setUserHas2FA(true);
        const phoneInfoOptions = {
          multiFactorHint: resolver.hints[0],
          session: resolver.session,
        };
        const hint = resolver.hints[0] as any;
        setPhoneNumber(hint.phoneNumber);

        const phoneAuthProvider = new PhoneAuthProvider(auth);
        // Send SMS verification code.
        phoneAuthProvider.verifyPhoneNumber(phoneInfoOptions, appVerifier).then((verificationId) => {
          setVerificationId(verificationId);
        });
      }
    },
    [dispatch],
  );

  useEffect(() => {
    if (tenantId) {
      if (storeTenantId !== tenantId) {
        const currentLocation = window.location.origin;
        const tenantParts = tenantId.lastIndexOf('-');
        const tenantName = tenantId.substring(0, tenantParts);
        const redirect = currentLocation.replace(
          /(?:http:\/\/)?(?:([^.]+)\.)?paperbox\.ai/,
          `https://${tenantName}.paperbox.ai`,
        );
        window.location.replace(redirect + location.pathname + location.search);
        auth.tenantId = tenantId;
        auth.tenantId = storeTenantId;
      }

      auth.tenantId = storeTenantId;
      if (isSignInWithEmailLink(auth, window.location.href)) {
        signInWithEmailLink(auth, email, window.location.href)
          .then(() => {
            navigate('/inbox');
          })
          .catch((err) => {
            handleError(err);
          });
      } else {
        navigate('/login');
      }
    }
  }, [tenantId, navigate, location, storeTenantId, email, handleError]);

  const [isLoading, setIsLoading] = useState(false);
  const handleSubmit = () => {
    if (userHas2FA) {
      setIsLoading(true);
      if (error === 'auth/invalid-verification-code') setError('');
      const cred = PhoneAuthProvider.credential(verificationId, smsCode);
      const multiFactorAssertion = PhoneMultiFactorGenerator.assertion(cred);
      MFAResolver.resolveSignIn(multiFactorAssertion)
        .then((res) => {
          let allowed = false;
          allowedDomains.forEach((item) => {
            if (res.user.email.endsWith(`@${item}`)) {
              allowed = true;
            }
          });
          if (allowed) {
            navigate('inbox');
          } else {
            setError('auth/not-allowed');
            sleep(3000).then(() => {
              auth.signOut();
              navigate('login');
            });
          }
        })
        .catch((err) => {
          setError(err.code);
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      navigate('/inbox', { replace: true });
    }
  };
  return (
    <>
      <Recaptcha />
      {!error && <SuspenseLoader name={'auth-otp-placeholder'} fullPage />}
      {error && (
        <div className={s.container}>
          <div className={s.card}>
            <PaperboxLogo className={s.logo} />
            <h2 className={s.title}>
              {userHas2FA ? (
                <span>
                  We've sent your 2FA Code to {phoneNumber}. <br /> Please enter it below.
                </span>
              ) : (
                (errorTextMap[error] ?? 'Something went wrong.')
              )}
            </h2>
            {userHas2FA && (
              <div className={s.mfa}>
                <TwoFactorCodeInput
                  setValue={setSmsCode}
                  error={error !== 'auth/multi-factor-auth-required' ? error : null}
                />
              </div>
            )}
            <AuthButton isLoading={isLoading} onClick={handleSubmit} isActive>
              {userHas2FA ? 'Continue' : 'To Login'}
            </AuthButton>
          </div>
        </div>
      )}
    </>
  );
};

export default AuthOTPEntry;
