{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": ["node_modules", "build", "coverage", "scripts", ".nyc_output", "tests", "shared/types"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 110}, "organizeImports": {"enabled": true}, "linter": {"rules": {"complexity": {"noForEach": "off", "useLiteralKeys": "off"}, "performance": {"noDelete": "off"}, "correctness": {"useExhaustiveDependencies": "off"}, "suspicious": {"noExplicitAny": "off", "noEmptyInterface": "off", "noImplicitAnyLet": "off", "noArrayIndexKey": "off"}, "recommended": true, "a11y": {"useKeyWithClickEvents": "off", "useKeyWithMouseEvents": "off", "noSvgWithoutTitle": "off", "noNoninteractiveTabindex": "off", "useButtonType": "off", "noAutofocus": "off"}, "style": {"useImportType": "off"}}}, "javascript": {"formatter": {"quoteStyle": "single"}}}