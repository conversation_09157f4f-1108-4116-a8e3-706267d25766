import s from '@shared/styles/component/auth/auth.module.scss';
import {ReactComponent as Confirmation} from '@svg/confirmation.svg';
import React from 'react';

interface Props {
  email: string;
  type: 'onboard' | 'login' | 'password';
}

const AuthOTPSent: React.FC<Props> = ({ email, type }) => {
  return (
    <>
      <div className={s.otp}>
        <Confirmation />
        {type === 'login' && (
          <p>
            We've sent a confirmation email to <b>{email}</b> Please click the link in that message within 24
            hours to log in to your account.
          </p>
        )}
        {type === 'onboard' && (
          <p>
            We've sent a confirmation email to <b>{email}</b> Please click the link in that message within 24
            hours to finish setting up your account.
          </p>
        )}
        {type === 'password' && (
          <p>
            We've sent a password reset email to <b>{email}</b> Please click the link in that message within
            24 hours to reset your password.
          </p>
        )}
      </div>
    </>
  );
};

export default AuthOTPSent;
