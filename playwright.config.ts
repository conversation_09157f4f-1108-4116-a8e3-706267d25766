import { PlaywrightTestConfig } from '@playwright/test';

const config: PlaywrightTestConfig = {
  // globalSetup: require.resolve('./global-setup'),
  webServer: {
    port: 3000,
    command: process.env.CI ? 'yarn start-tst-ci' : 'yarn start-tst',
    env: {
      USE_BABEL_PLUGIN_ISTANBUL: '1',
    },
  },
  timeout: 3 * 60 * 1000,
  expect: {
    timeout: 5 * 1000,
  },
  use: {
    actionTimeout: 20 * 1000,
    baseURL: 'http://localhost:3000',
    headless: true,
    viewport: { width: 1400, height: 810 },
    screenshot: process.env.CI ? 'only-on-failure' : 'on',
    video: process.env.CI ? 'retain-on-failure' : 'retain-on-failure',
    trace: process.env.CI ? 'retain-on-failure' : 'on',
  },
  forbidOnly: !!process.env.CI,

  retries: 0,
};
export default config;
