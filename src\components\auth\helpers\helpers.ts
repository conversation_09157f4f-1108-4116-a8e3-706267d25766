import { auth } from '@shared/store/setup/firebase-setup';
import userSlice from '@shared/store/userSlice';
import { signInWithPopup } from 'firebase/auth';
import { NavigateFunction } from 'react-router';

export const loginWithPopup = (
  provider,
  dispatch,
  allowedDomains,
  navigate: NavigateFunction,
  handleError,
) => {
  dispatch(userSlice.actions.setIsAuthPopupActive(true));
  signInWithPopup(auth, provider)
    .then((res) => {
      let allowed = false;
      if (res.user) {
        allowedDomains.forEach((item) => {
          if (res.user.email.endsWith(`@${item}`)) allowed = true;
        });
        if (allowed) {
          navigate('inbox');
        } else {
          auth.signOut();
          handleError({ code: 'domain-not-allowed' });
          // navigate('/login', {
          //   replace: true,
          //   state: {
          //     reason: 'not-allowed',
          //   },
          // });
        }
      }
      console.log(res);
      dispatch(userSlice.actions.setIsAuthPopupActive(false));
    })
    .catch((err) => {
      console.log(err);
      dispatch(userSlice.actions.setIsAuthPopupActive(false));
      handleError(err);
    });
};
