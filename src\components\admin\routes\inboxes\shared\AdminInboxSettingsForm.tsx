import FormInputField from '@components/admin/components/form/FormInputField';
import FormSection from '@components/admin/components/form/FormSection';
import Checkbox from '@components/shared/checkbox/Checkbox';
import { DropdownOption } from '@components/shared/dropdown/StyledSelect';
import s from '@shared/styles/component/admin/admin-section.module.scss';
import { IClientInbox } from '@src/shared/helpers/converters/inbox';
import { useSelector } from '@src/shared/store/store';
import clsx from 'clsx';
import { cloneDeep } from 'lodash';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface AdminInboxSettingsFormProps {
  inboxState: Partial<IClientInbox>; // Using any to accommodate both creation and settings interfaces
  handleInput: (value: any, field: string) => void;
  tableColumnsMapping?: Record<string, string>;
  isCreation?: boolean;
  setState?: React.Dispatch<React.SetStateAction<any>>;
}

const AdminInboxSettingsForm: React.FC<AdminInboxSettingsFormProps> = ({
  inboxState,
  handleInput,
  tableColumnsMapping = {},
  isCreation = false,
  setState,
}) => {
  const { t } = useTranslation();
  const inboxes = useSelector((state) => state.admin.inboxes);

  const inboxOptions = useMemo(() => {
    if (inboxes) {
      return inboxes.map((inbox) => {
        return {
          name: inbox.settings.name,
          id: inbox.id,
        };
      });
    }
  }, [inboxes]);
  const actionTypeOptions = useMemo(() => {
    return [
      { value: 'approve', label: t('admin:inboxes.autoDelete.options.approve') },
      { value: 'delete', label: t('admin:inboxes.autoDelete.options.delete') },
      { value: 'bounce', label: t('admin:inboxes.autoDelete.options.bounce') },
      {
        value: 'none',
        label: t('admin:inboxes.autoDelete.options.noAction'),
        color: '#FF5555',
        tag: { value: 'danger', isMinimal: false, name: ' ! DANGER ! ' },
      },
    ] as DropdownOption[];
  }, []);

  const selectedActionType = useMemo(() => {
    if (!inboxState?.settings?.autoDelete) return actionTypeOptions[0];
    return (
      actionTypeOptions.find(
        (ett) => ett.value === (inboxState?.settings?.autoDelete.actionType ?? 'none'),
      ) || actionTypeOptions[0]
    );
  }, [inboxState?.settings?.autoDelete?.actionType, actionTypeOptions]);

  return (
    <>
      <FormSection title={isCreation ? 'Inbox Settings' : 'Data'}>
        <FormInputField
          testId="inbox-name-input"
          required
          value={inboxState?.settings?.name}
          type="text"
          label={t('admin:inboxes.name')}
          description={t('admin:inboxes.nameDescription')}
          onChange={(val) => handleInput(val, 'settings.name')}
        />
        {!isCreation && (
          <>
            <FormInputField
              testId={'inbox-id-input'}
              hidden={isCreation}
              isCopyField
              value={inboxState?.id}
              type={'text'}
              label={t('admin:masterdata.id')}
              description={t('admin:masterdata.idDescription')}
            />
            <FormInputField
              hidden={isCreation}
              type={'text'}
              value={inboxState?.postEmail}
              label={t('admin:inboxes.uploadMail')}
              description={t('admin:inboxes.uploadMailDescription')}
              isCopyField
            />
            <FormInputField
              hidden={isCreation}
              value={inboxState?.config?.workflowVersion}
              type={'text'}
              label={t('admin:inboxes.workflow')}
              description={t('admin:inboxes.workflowDescription')}
              isPaperboxOnly
              isCopyField
            />
          </>
        )}
      </FormSection>

      <FormSection title={'Features'}>
        <FormInputField
          testId="mailroom-toggle"
          value={inboxState?.settings?.mailroom}
          type={'toggle'}
          label={t('admin:inboxes.settings.mailroom')}
          description={t('admin:inboxes.settings.mailroomDesc')}
          onChange={(val) => handleInput(val, 'settings.mailroom')}
          isPaperboxOnly
        />
        <FormInputField
          value={inboxState?.settings?.autoAdvance}
          type={'toggle'}
          label={t('admin:inboxes.settings.autoAdvance')}
          description={t('admin:inboxes.settings.autoAdvanceDesc')}
          onChange={(val) => handleInput(val, 'settings.autoAdvance')}
        />
        <FormInputField
          value={inboxState?.settings?.documentCopy}
          type={'toggle'}
          label={t('admin:inboxes.settings.documentCopy')}
          description={t('admin:inboxes.settings.documentCopyDesc')}
          onChange={(val) => handleInput(val, 'settings.documentCopy')}
        />
        <FormInputField
          value={inboxState?.settings?.documentDownload}
          type={'toggle'}
          label={t('admin:inboxes.settings.documentDownload')}
          description={t('admin:inboxes.settings.documentDownloadDesc')}
          onChange={(val) => handleInput(val, 'settings.documentDownload')}
        />
        <FormInputField
          value={inboxState?.settings?.documentTransform}
          type={'toggle'}
          label={t('admin:inboxes.settings.documentTransform')}
          description={t('admin:inboxes.settings.documentTransformDesc')}
          onChange={(val) => handleInput(val, 'settings.documentTransform')}
        />
        <FormInputField
          testId="file-upload-toggle"
          value={inboxState?.settings?.fileUpload}
          type={'toggle'}
          label={t('admin:inboxes.settings.fileUpload')}
          description={t('admin:inboxes.settings.fileUploadDesc')}
          onChange={(val) => handleInput(val, 'settings.fileUpload')}
        />
        {!isCreation && (
          <FormInputField
            value={inboxState?.settings?.bounce}
            type={'toggle'}
            label={t('admin:inboxes.settings.bounce')}
            description={t('admin:inboxes.settings.bounceDesc')}
            onChange={(val) => handleInput(val, 'settings.bounce')}
          />
        )}
        <FormInputField
          value={inboxState?.settings?.labelingMode}
          type={'toggle'}
          label={t('admin:inboxes.settings.labelingMode')}
          description={t('admin:inboxes.settings.labelingModeDesc')}
          onChange={(val) => handleInput(val, 'settings.labelingMode')}
        />
      </FormSection>

      <FormSection title={t('admin:inboxes.inboxMove.title')}>
        <FormInputField
          type={'toggle'}
          value={inboxState?.settings?.inboxMoveWhitelistActive}
          onChange={(val) => handleInput(val, 'settings.inboxMoveWhitelistActive')}
          label={t('admin:inboxes.inboxMove.enabled')}
          description={t('admin:inboxes.inboxMove.enabledDescription')}
        />
        <FormInputField
          hidden={!inboxState?.settings?.inboxMoveWhitelistActive}
          type={'list'}
          value={inboxState?.settings?.inboxMoveWhitelist ?? []}
          listInputOptions={{
            options: inboxOptions ?? [],
            selectedOptions: inboxState?.settings?.inboxMoveWhitelist ?? [],
            onChangeList: (newList) => {
              handleInput(newList, 'settings.inboxMoveWhitelist');
            },
          }}
          label={t('admin:inboxes.inboxMove.list')}
          description={t('admin:inboxes.inboxMove.listDescription')}
        />
      </FormSection>

      {!isCreation && tableColumnsMapping && Object.keys(tableColumnsMapping).length > 0 && (
        <FormSection title={'Inbox Columns'}>
          <div className={clsx(s.item, s.item__vertical)}>
            <div className={s.item_text}>
              <h4>{t('admin:inboxes.tableCols')}</h4>
              <p>{t('admin:inboxes.tableColsDescription')}</p>
            </div>
            {inboxState?.settings?.tableColumns && setState && (
              <div className={clsx(s.item_action, s.tables_grid)}>
                {Object.entries(inboxState?.settings?.tableColumns)
                  .sort((a, b) => a[0].localeCompare(b[0]))
                  .map(([k, v]) => {
                    const name = tableColumnsMapping[k];
                    if (!name) return null;
                    return (
                      <div
                        onClick={() => {
                          setState((state: any) => {
                            const clone = cloneDeep(state);
                            if (isCreation) {
                              clone.settings.tableColumns[k] = !v;
                            } else {
                              clone.settings.tableColumns[k] = !v;
                            }
                            return clone;
                          });
                        }}
                        key={k}
                        className={s.tables_grid_item}
                      >
                        <span>{name}</span>
                        <Checkbox
                          checked={Boolean(v)}
                          onClick={() => {
                            setState((state: any) => {
                              const clone = cloneDeep(state);
                              if (isCreation) {
                                clone.settings.tableColumns[k] = !v;
                              } else {
                                clone.settings.tableColumns[k] = !v;
                              }
                              return clone;
                            });
                          }}
                        />
                      </div>
                    );
                  })}
              </div>
            )}
          </div>
        </FormSection>
      )}

      <FormSection title={t('admin:inboxes.autoDelete.title')}>
        {inboxState?.settings?.autoDelete && (
          <>
            <FormInputField
              type={'toggle'}
              value={inboxState?.settings?.autoDelete.active}
              onChange={(val) => handleInput(val, 'settings.autoDelete.active')}
              label={t('admin:inboxes.autoDelete.enabled')}
              description={t('admin:inboxes.autoDelete.enabledDescription')}
            />
            <FormInputField
              type={'dropdown'}
              value={selectedActionType}
              dropdownOptions={actionTypeOptions}
              onChange={(option) => {
                if (option.value === inboxState?.settings?.autoDelete.actionType) return;
                handleInput(option.value, 'settings.autoDelete.actionType');
              }}
              label={t('admin:inboxes.autoDelete.actionType')}
              description={t('admin:inboxes.autoDelete.actionTypeDescription')}
            />
            <FormInputField
              type={'number'}
              value={inboxState?.settings?.autoDelete.timeField}
              onChange={(val) => handleInput(Number.parseInt(val), 'settings.autoDelete.timeField')}
              numberInputOptions={{
                label: t('admin:inboxes.days'),
                min: 1,
                max: 29,
                step: 1,
              }}
              label={t('admin:inboxes.autoDelete.time')}
              description={t('admin:inboxes.autoDelete.timeDescription')}
            />
          </>
        )}
      </FormSection>
    </>
  );
};

export default AdminInboxSettingsForm;
