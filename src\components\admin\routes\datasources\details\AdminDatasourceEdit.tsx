import AdminDatasourceDerivedMappingEditor from '@components/admin/components/AdminDatasourceDerivedMappingEditor.tsx';
import AdminDatasourceExamplesEditor from '@components/admin/components/AdminDatasourceExamplesEditor.tsx';
import AdminDatasourceMappingEditor from '@components/admin/components/AdminDatasourceMappingEditor.tsx';
import FormBodyHeader from '@components/admin/components/form/FormBodyHeader.tsx';
import FormInputField from '@components/admin/components/form/FormInputField.tsx';
import FormSection from '@components/admin/components/form/FormSection.tsx';
import ConfirmationDialog from '@components/shared/confirmation-dialog/ConfirmationDialog.tsx';

import {
  IClientDatasource,
  SourceType,
  datasourceClientToRaw,
} from '@shared/helpers/converters/datasource.ts';
import useChangeTracker, { ChangeSaveCallback } from '@shared/hooks/useChangeTracker.tsx';
import { useModal } from '@shared/hooks/useModal.tsx';
import {
  deleteDatasource,
  patchDatasource,
  postDatasource,
  postDatasourceSample,
} from '@shared/store/adminSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store.ts';
import s from '@shared/styles/component/admin/admin-section.module.scss';
import { AxiosError } from 'axios';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router';

const AdminDatasourceEdit: React.FC = () => {
  const datasources = useSelector((state) => state.admin.datasources);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [sampleUploaded, setSampleUploaded] = useState(false);
  const [uploadingSample, setUploadingSample] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { datasourceId } = useParams();
  const { t } = useTranslation();
  const { showDialog } = useModal();
  const dispatch = useDispatch();

  const activeDatasource = useMemo(() => {
    if (datasourceId !== 'new' && datasources) {
      const datasource = datasources.find((ds) => ds.id === datasourceId);
      return datasource || null;
    }
    return null;
  }, [datasources, datasourceId]);

  // Reset upload-related state when navigating between datasources
  useEffect(() => {
    if (datasourceId === 'new') {
      // Reset all upload-related state for new datasources
      setSampleUploaded(false);
      setUploadError(null);
      setUploadingSample(false);
      setErrorMessage(null);
    } else if (activeDatasource) {
      // For existing datasources, mark sample as uploaded
      setSampleUploaded(true);
      setUploadError(null);
      setUploadingSample(false);
      setErrorMessage(null);
    }
  }, [datasourceId, activeDatasource]);

  const handleDelete = () => {
    const dsId = activeDatasource.id;
    showDialog(
      <ConfirmationDialog
        confirmAction={() => {
          return dispatch(deleteDatasource(dsId)).then(() => {
            navigate('/admin/datasources');
          });
        }}
        text={t('admin:datasources.deleteConfirmation')}
      />,
    );
  };

  const handleSave: ChangeSaveCallback<IClientDatasource> = async () => {
    // For new datasources, require sample upload first
    if (datasourceId === 'new' && !sampleUploaded) {
      setErrorMessage(t('admin:datasources.sampleRequired'));
      return Promise.reject(new Error('Sample upload required'));
    }

    const payload = datasourceClientToRaw(state);
    const parseError = (errorCode) => {
      if (errorCode === 409) {
        setErrorMessage(t('admin:datasources.errorExists'));
      } else return setErrorMessage(t('admin:datasources.errorGeneric'));
    };
    let func;
    if (activeDatasource) {
      func = () => patchDatasource(activeDatasource.id, payload);
    } else {
      func = () => postDatasource(payload);
    }
    return func()
      .then((res) => {
        navigate('/admin/datasources');
        return res;
      })
      .catch((err: AxiosError) => parseError(err.code));
  };

  const initialState = useMemo((): IClientDatasource => {
    if (!activeDatasource) {
      return {
        id: '',
        name: '',
        recordIdField: '',
        mappings: {},
        derivedMappings: {},
        source: {
          type: SourceType.INTERNAL,
          config: {
            index_name: [],
          },
        },
        frequencyS: null,
        operationRetention: null,
        examples: null,
      };
    }
    return activeDatasource;
  }, [activeDatasource]);

  const { save, state, setState, saving, hasChanges, handleInput } = useChangeTracker(
    initialState,
    handleSave,
  );

  // Reset form state when switching between datasources
  useEffect(() => {
    setState(initialState);
  }, [initialState, setState]);

  const handleSampleUpload = async (file: File) => {
    setUploadingSample(true);
    setUploadError(null);

    try {
      const response = await postDatasourceSample(file);
      const { mappings, recordIdField, examples } = response.data;

      // Update the datasource state with auto-generated mappings
      handleInput(mappings, 'mappings');
      if (recordIdField) {
        handleInput(recordIdField, 'recordIdField');
      }
      if (examples) {
        handleInput(examples, 'examples');
      }

      setSampleUploaded(true);
    } catch (error) {
      console.error('Sample upload failed:', error);
      const errorMessage =
        (error as any)?.response?.data?.message || t('admin:datasources.sampleUploadError');
      setUploadError(errorMessage);
    } finally {
      setUploadingSample(false);
    }
  };

  return (
    <form onSubmit={save} className={s.form_body}>
      <FormBodyHeader
        hasChanges={hasChanges}
        saving={saving}
        errorMessage={errorMessage}
        title={activeDatasource ? activeDatasource.name : t('admin:datasources.newDatasource')}
      />
      <div className={s.sections}>
        {/* Upload Sample Section - Only for new datasources */}
        {datasourceId === 'new' && !sampleUploaded && (
          <FormSection title={t('admin:datasources.uploadSample')}>
            <FormInputField
              testId={'datasource-name-input'}
              value={state.name}
              description={t('admin:datasources.nameDescription')}
              type={'text'}
              label={t('admin:datasources.name')}
              onChange={(val) => handleInput(val, 'name')}
              required
            />

            <FormInputField
              testId={'datasource-sample-upload'}
              value={null}
              type={'upload'}
              label={t('admin:datasources.sampleFile')}
              description={t('admin:datasources.sampleFileDescription')}
              uploadOptions={{
                onUploadStatusChange: setUploadingSample,
                errorMessage: uploadError || t('admin:datasources.sampleUploadError'),
                handleUpload: handleSampleUpload,
                accept: '.json,.csv,.xlsx',
              }}
              disabled={uploadingSample}
            />
          </FormSection>
        )}

        {/* Basic Info Section - Show after sample upload or for existing datasources */}
        {(sampleUploaded || datasourceId !== 'new') && (
          <FormSection title={t('admin:datasources.basicInfo')}>
            <FormInputField
              isCopyField
              value={state.id}
              type={'text'}
              label={t('admin:datasources.id')}
              description={t('admin:datasources.idDescription')}
              hidden={datasourceId === 'new'}
            />

            <FormInputField
              testId={'datasource-name-input'}
              value={state.name}
              description={t('admin:datasources.nameDescription')}
              type={'text'}
              label={t('admin:datasources.name')}
              onChange={(val) => handleInput(val, 'name')}
              required
            />

            <FormInputField
              testId={'datasource-record-id-field-input'}
              value={state.recordIdField}
              description={t('admin:datasources.recordIdFieldDescription')}
              type={'text'}
              label={t('admin:datasources.recordIdField')}
              onChange={(val) => handleInput(val, 'recordIdField')}
              required
            />

            <FormInputField
              testId={'datasource-frequency-input'}
              value={state.frequencyS}
              description={t('admin:datasources.frequencyDescription')}
              type={'number'}
              label={t('admin:datasources.frequency')}
              numberInputOptions={{
                label: 'sec',
                min: 0,
                max: 2592000, // 30 days in seconds
                step: 1,
              }}
              onChange={(val) => {
                const value = val === '' ? null : Number.parseInt(val);
                handleInput(value, 'frequencyS');
              }}
              placeholder={'Optional - leave empty for manual updates only'}
            />

            <FormInputField
              testId={'datasource-operation-retention-input'}
              value={state.operationRetention}
              description={t('admin:datasources.operationRetentionDescription')}
              type={'number'}
              label={t('admin:datasources.operationRetention')}
              numberInputOptions={{
                label: 'ops',
                min: 1,
                step: 1,
              }}
              onChange={(val) => {
                const value = val === '' ? null : Number.parseInt(val);
                handleInput(value, 'operationRetention');
              }}
              placeholder={'0'}
            />
          </FormSection>
        )}

        {/* Mapping and Configuration Sections - Only show after sample upload or for existing datasources */}
        {(sampleUploaded || datasourceId !== 'new') && (
          <>
            {/* <FormSection title={t('admin:datasources.sourceConfiguration')}>
              <FormInputField
                testId={'datasource-source-type-input'}
                value={sourceTypeOptions.find((opt) => opt.value === state.source?.type)}
                label={t('admin:datasources.sourceType')}
                description={t('admin:datasources.sourceTypeDescription')}
                type="dropdown"
                dropdownOptions={sourceTypeOptions}
                defaultDropdownOption={sourceTypeOptions[0]}
                onChange={(val) => {
                  const newSource = {
                    type: val.value,
                    config: {
                      index_name: state.source?.config?.index_name || [],
                    },
                  };
                  handleInput(newSource, 'source');
                }}
              />

              <FormInputField
                testId={'datasource-index-names-input'}
                value={state.source?.config?.index_name || []}
                description={t('admin:datasources.indexNamesDescription')}
                type={'list'}
                label={t('admin:datasources.indexNames')}
                listInputOptions={{
                  onChangeList: (newList) => {
                    const newSource = {
                      ...state.source,
                      config: {
                        ...state.source.config,
                        index_name: newList,
                      },
                    };
                    handleInput(newSource, 'source');
                  },
                }}
              />
            </FormSection>*/}

            <FormSection title={t('admin:datasources.fieldMappings')}>
              <AdminDatasourceMappingEditor
                mappings={state.mappings}
                onChange={(mappings) => handleInput(mappings, 'mappings')}
                title={t('admin:datasources.mappings')}
                description={t('admin:datasources.mappingsDescription')}
              />
            </FormSection>

            <FormSection title={t('admin:datasources.derivedFields')}>
              <AdminDatasourceDerivedMappingEditor
                derivedMappings={state.derivedMappings}
                onChange={(derivedMappings) => handleInput(derivedMappings, 'derivedMappings')}
                title={t('admin:datasources.derivedMappings')}
                description={t('admin:datasources.derivedMappingsDescription')}
              />
            </FormSection>

            <FormSection title={t('admin:datasources.examples')}>
              <AdminDatasourceExamplesEditor
                examples={state.examples}
                onChange={(examples) => handleInput(examples, 'examples')}
                title={t('admin:datasources.exampleData')}
                description={t('admin:datasources.exampleDataDescription')}
              />
            </FormSection>
          </>
        )}

        {/* Delete Section - Only for existing datasources */}
        {datasourceId !== 'new' && (
          <FormSection title={t('admin:datasources.dangerZone')}>
            <FormInputField
              testId={'datasource-delete'}
              type={'button'}
              buttonOptions={{
                type: 'error',
                text: t('admin:datasources.delete'),
                onClick: handleDelete,
              }}
              label={t('admin:datasources.delete')}
              description={t('admin:datasources.deleteDescription')}
            />
          </FormSection>
        )}
      </div>
    </form>
  );
};

export default AdminDatasourceEdit;
