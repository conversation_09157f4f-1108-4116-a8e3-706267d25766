import AdminImportModal from '@components/admin/components/AdminImportModal.tsx';
import FormBodyHeader from '@components/admin/components/form/FormBodyHeader.tsx';
import FormInputField from '@components/admin/components/form/FormInputField.tsx';
import FormSection from '@components/admin/components/form/FormSection.tsx';
import SuspenseLoader from '@components/shared/suspense-loader/SuspenseLoader.tsx';
import { IClientInbox, defaultClientInbox, inboxClientToRaw } from '@shared/helpers/converters/inbox.ts';
import { getCurrentEnvCode, isPbxEmail } from '@shared/helpers/helpers.ts';
import useChangeTracker, { ChangeSaveCallback } from '@shared/hooks/useChangeTracker.tsx';
import { useModal } from '@shared/hooks/useModal.tsx';
import { exportInboxConfig, getInboxes, patchInbox, postInbox } from '@shared/store/adminSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store.ts';
import s from '@shared/styles/component/admin/admin-section.module.scss';
import { DotPulse } from '@uiball/loaders';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router';
import AdminInboxSettingsForm from '../shared/AdminInboxSettingsForm';

interface Props {}

const AdminInboxesSettings: React.FC<Props> = () => {
  const inboxes = useSelector((state) => state.admin.inboxes);
  const { email } = useSelector((state) => state.user.userAccount);
  const tenantDetails = useSelector((state) => state.admin.tenantDetails);
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { showModal } = useModal();
  const { inboxId } = useParams();
  const navigate = useNavigate();

  // We no longer need to redirect since the route structure has been updated
  // The 'new' inboxId is now handled by the AdminInboxes component

  const activeInbox = useMemo(() => {
    if (inboxes) return inboxes.find((inbox) => inbox.id === inboxId);
  }, [inboxId, inboxes]);

  const initialState = useMemo(() => {
    if (activeInbox) {
      return activeInbox;
    }
    return defaultClientInbox;
  }, [activeInbox]);

  const handleSave: ChangeSaveCallback<IClientInbox> = async () => {
    const mapped = inboxClientToRaw(state);
    if (activeInbox) {
      return patchInbox(mapped).then(() => {
        return dispatch(getInboxes());
      });
    }
    return postInbox(mapped).then((res) => {
      return dispatch(getInboxes()).then(() => {
        if (res.data.id) {
          navigate(`/admin/inboxes/${res.data.id}/settings`);
        }
      });
    });
  };
  const [isExportLoading, setIsExportLoading] = useState(false);

  const handleExport = async () => {
    setIsExportLoading(true);
    try {
      const { data: inboxData } = await exportInboxConfig(state.id);
      const blob = new Blob([JSON.stringify(inboxData)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      const env = getCurrentEnvCode();
      a.href = url;
      a.download = `${env}-${tenantDetails.settings.name}-${activeInbox.settings.name}.json`;
      a.click();
      window.URL.revokeObjectURL(url);
      a.remove();
    } finally {
      setIsExportLoading(false);
    }
  };

  const { save, hasChanges, saving, state, setState, error, handleInput } = useChangeTracker<IClientInbox>(
    initialState,
    handleSave,
  );

  const tableColumnsMapping = {
    approver: t('home:table.approved'),
    confidence: t('home:table.confidence'),
    digitizedDate: t('home:table.dateAdded'),
    docTypeId: t('home:table.docType'),
    lastUserUpdate: t('home:table.lastEdited'),
    name: t('home:table.docName'),
    tagTypeId: t('home:table.state'),
  };

  if (inboxId !== 'new' && !activeInbox) {
    return <SuspenseLoader name={'admin-inbox'} />;
  }

  return (
    <form onSubmit={save} className={s.form_body}>
      <FormBodyHeader
        hasChanges={hasChanges}
        saving={saving}
        errorMessage={t(`admin:errors.${error}`, { defaultValue: '' })}
        title={activeInbox?.settings.name || t('admin:inboxes.createInbox')}
      />
      <div className={s.sections}>
        <AdminInboxSettingsForm
          inboxState={state}
          handleInput={(val, path) => handleInput(val, `${path}`)}
          tableColumnsMapping={tableColumnsMapping}
          setState={setState}
        />
        <FormSection title={'Inbox Import/Export'} hidden={!isPbxEmail(email)}>
          <FormInputField
            label={'Export'}
            description={
              'Exports inbox settings and configurations (Document types, field types, metadata, action types and tags) to a JSON file.'
            }
            type={'button'}
            buttonOptions={{
              type: 'normal',
              onClick: handleExport,
              text: isExportLoading ? (
                <div style={{ width: 100, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <DotPulse color={'#0085FF'} size={20} />
                </div>
              ) : (
                'Export Inbox Configuration'
              ),
            }}
          />

          {/*<button*/}
          {/*  type="button"*/}
          {/*  onClick={() => {*/}
          {/*    apiClient.delete(`${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/${inboxId}`, {*/}
          {/*      params: { key: import.meta.env.VITE_FIREBASE_API_KEY },*/}
          {/*    });*/}
          {/*  }}*/}
          {/*>*/}
          {/*  Delete inbox*/}
          {/*</button>*/}
          <FormInputField
            label={'Import'}
            description={'Import Inbox Configuration from a JSON file.'}
            type={'button'}
            buttonOptions={{
              type: 'normal',
              onClick: () => showModal(<AdminImportModal inboxId={inboxId} />),
              text: 'Import Inbox Configuration',
            }}
          />
        </FormSection>
      </div>
    </form>
  );
};

export default AdminInboxesSettings;
