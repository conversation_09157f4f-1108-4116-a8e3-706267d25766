import { IClientMasterdataSearchResult } from '@shared/helpers/converters/masterdata-result.ts';
import s from '@shared/styles/component/document/sidebar/search.module.scss';
import React from 'react';
import SearchResultCard from './SearchResultCard';

interface Props {
  searchResults: IClientMasterdataSearchResult[];
}

const SearchResults: React.FC<Props> = ({ searchResults }) => {
  if (searchResults?.length === 0 || !searchResults) {
    return null;
  }
  return (
    <div className={s.cards}>
      {searchResults?.map((result) => {
        return <SearchResultCard key={result.id} result={result} />;
      })}
    </div>
  );
};

export default SearchResults;
