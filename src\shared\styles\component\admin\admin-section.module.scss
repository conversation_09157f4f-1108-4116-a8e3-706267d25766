@import "../../vars/_vars";


.container {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  width: calc(100%);
  height: calc(100%);
  padding-bottom: 10px;
  background: #FCFBFC;
  will-change: opacity;

  &__absolute {
    position: absolute;
    margin-right: -10px;
    margin-left: -10px;
    padding-right: 10px;
    padding-left: 10px;
  }
}


.loading {
  font-size: 18px;
  font-weight: 500;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 100%;
  gap: 20px;
}


.form_body {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 100%;
  max-width: 1400px;
  height: 100%;
  margin-top: 24px;
  padding: 40px;
  margin-inline: auto;

  &_scroll {
    overflow: auto;
    overflow-x: hidden;
  }
}


.save_button {
  font-size: 14px;
  display: flex;
  align-items: center;
  align-self: flex-end;
  justify-content: space-between;
  width: auto;
  padding: 8px 20px;
  transition: 0.2s ease-in-out;
  transition-property: background-color, color, transform;
  color: white;
  border: 1px solid transparent;
  border-radius: 5px;
  background: $paperbox-blue;
  gap: 8px;

  &:disabled {
    cursor: default;
    color: #BCBEBF;
    border: 1px solid #EEEEEE;
    background: white;
  }

  &:hover {
    transform: scale(0.95)

  }

  &__alt {
    color: black;
    border: 1px solid #EEEEEE;
    border-radius: 5px;
    background: white;
  }
}


.sections {
  overflow: auto;
  overflow-x: hidden;
  width: calc(100% + 40px);
  height: 100%;
  margin-left: -20px;
  padding-bottom: 50px;
  padding-inline: 20px;
}


.disabled {
  user-select: none;
  pointer-events: none;
  opacity: 0.7;
}


.hidden {
  display: none !important;
}


.section_overlay {
  font-weight: 500;
  line-height: 1.3;
  position: absolute;
  z-index: 10;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  color: $paperbox-blue;
  background: rgba(255, 255, 255, 0.7);
  inset: 0;
  gap: 16px;
}


.section {
  position: relative;
  overflow-x: hidden;
  width: 100%;
  height: auto;
  transition: opacity 0.3s ease-in-out;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: white;

  &__scroll {
    overflow: auto;
    //min-height: 100px;
    max-height: 100%;
  }

  &__scroll_small {
    overflow: auto;
  }

  & + & {
    margin-top: 20px;
  }

  &__appear {
    & > .section_workflow {
      animation: fadeIn 0.5s ease-in-out forwards;
    }
  }

  &:last-of-type {
    margin-bottom: 60px;
  }

  &__no_style {
    overflow-x: visible;
    width: calc(100% + 40px);
    margin-left: -20px;
    padding: 0 16px;
    border: unset;
    background: unset;
  }

  &__disabled {
    pointer-events: none;
    opacity: 0.5;
  }
}


@keyframes fadeIn {
  from {
    opacity: 0;

  }
  to {
    opacity: 1;

  }
}


.section_title {
  font-family: $headings-font;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 60px;
  margin-bottom: 20px;
  transition: opacity 0.3s ease-in-out;

  h3 {
    font-size: 18px;
    font-weight: 700;
  }

  .section_notice {
    font-size: 14px;
    margin-top: 1px;
    margin-right: auto;
    margin-left: 12px;
    color: $paperbox-blue;
  }
}


.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: auto;
  margin-bottom: 20px;
}


.search_tools {
  display: flex;
  margin-right: auto;
  margin-left: 8px;
  gap: 8px;
}


.search_wrapper {
  position: relative;
  width: 250px;

  .search_icon {
    position: absolute;
    right: 12px;
    bottom: 50%;
    transform: translateY(50%);
    color: #969FAD;
  }

  .search {
    font-family: $base-font;
    font-size: 14px;
    width: 100%;
    height: 40px;
    padding-right: 36px;
    padding-left: 12px;
    border: 1px solid #EEEEEE;
    border-radius: 5px;
    outline: none;
    background: white;

    &::placeholder {
      color: #969FAD;
    }
  }
}


.item {
  display: flex;
  overflow: auto;
  overflow-x: hidden;
  align-items: center;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  padding: 25px;
  transition: opacity 0.3s ease-in-out;

  gap: 20px;

  &__faded {
    user-select: none;
    pointer-events: none;
    opacity: 0.4;
  }

  &:not(:last-of-type) {
    border-bottom: 1px solid #EEEEEE;
  }

  &__parent {
    padding-bottom: 0;
    border-bottom: unset !important;
  }

  &__horizontal {
    position: relative;
    display: flex;
    overflow-x: hidden;
    overflow-y: visible;
    align-items: flex-start;
    flex-direction: column;
    width: 100%;
    gap: unset;

    .item_text {
      display: flex;
      align-items: center;
      flex-direction: row;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 20px;

      p {
        margin-top: 0;

        a {
          color: $paperbox-blue;

          &:visited {
            color: $paperbox-blue;
          }
        }
      }

    }

    .item_action {

      width: 100%;
      max-width: 100%;
    }
  }

  &__grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;

    .item_text {
      //take 4 cols
      grid-column: span 3 / span 3;
    }

  }

  &__vertical {
    position: relative;
    display: flex;
    overflow-x: hidden;
    align-items: flex-start;
    flex-direction: column;
    width: 100%;
    gap: unset;

    .item_text {
      width: 100%;
    }

    .item_action {
      width: 100%;
      max-width: 100%;
    }

    .item_add {
      position: absolute;
      top: 25px;
      right: 25px;
      background: #FAFAFA;
    }
  }
}


.item_head {
  display: flex;
  justify-content: flex-start;
  width: 100%;
}


.children {
  border-bottom: 1px solid #EEEEEE;

}


.no_results {
  width: 100%;
  padding: 20px 14px;
  animation: fadeIn 0.2s ease-in-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
  color: black;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: white;

}


.item_file_input {
  display: none;
}


.row_list {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 8px;

}


.tables_grid {
  display: grid;
  margin-top: 30px;
  margin-bottom: 20px;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(auto-fit, 1fr);
  gap: 20px;
  grid-row-gap: 10px;

}


.tables_grid_item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 20px 15px;
  transition: border 0.15s, background-color 0.15s ease-in-out;
  border: 1px solid #eeeeee;
  border-radius: 5px;
  gap: 8px;

  &:hover {
    cursor: pointer;
    background: $paperbox-blue--fade-extra;
  }

  span {
    font-size: 15px;
  }
}


.sortable_section {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  gap: 8px;
}


.item_text {
  display: flex;
  flex-direction: column;
  white-space: pre-wrap;

  h4 {
    font-size: 16px;
    font-weight: 500;

    svg {
      margin-left: 6px;
      color: $paperbox-blue

    }

  }

  p {
    font-size: 14px;
    line-height: 1.5;
    margin-top: 5px;
    color: #898B99;
  }

}


.item_action {
  max-width: 70%;

  &_flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px
  }
}


.field_type_header {
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
  padding: 0 14px;
  color: #898B99;
}


.field_type_row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  min-height: 35px;
  padding: 0 14px;
  transition: box-shadow 0.1s ease-in-out;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: #fafafa;
  gap: 10px;

  .left {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;
  }

  span {
    font-size: 14px;
    margin-right: auto;
  }

  &:first-of-type {
    margin-top: 20px;
  }
}


.field_type_row_inner {
  display: flex;
  align-items: center;
  justify-content: center;
}


.field_type_row_actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 90%;
  max-width: 525px;
  margin-right: 0;
  margin-left: auto;

  color: #979797;
  gap: 20px;

  .trash {
    width: auto;
    height: 14px;

    &:hover svg {
      color: $error;
    }
  }
}


.item_fields {
  display: grid;
  justify-content: end;
  margin-top: 20px;
  grid-template-columns: repeat(4, 1fr);
  //direction: rtl;
  gap: 8px;
}


.item_categories {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}


.item_field {
  font-size: 14px;

  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 42px;
  height: 42px;
  padding: 10px;
  cursor: pointer;
  user-select: none;
  border: 1px solid #EEEEEE;
  border-radius: 7px;
  direction: ltr; /* Flip the grid */
  gap: 6px;

  .field_cross {
  }

  .field_add {
    color: #969FAD;

    &:hover {
      color: $paperbox-blue;
    }
  }

  .field_content {
    @include flex-center;

    span {
      width: 100%;
      margin-right: -10px;
      margin-left: 10px;
      transition: all 0.2s;
    }

    svg {
      width: auto;
      height: 16px;
      opacity: 0.5;
    }
  }

  .field_cross {
    width: auto;
    max-width: 16px;
    height: 16px;
    margin-right: -2px;
    margin-left: 2px;
    cursor: pointer;
    transition: all 0.2s;
    opacity: 0;
    color: #969FAD;

    &:hover {
      color: $error;
    }
  }

  &:hover:not(&__draggable) {
    .field_content {
      span {
        width: 100%;
        margin-right: unset;
        margin-left: unset;
      }
    }

    .field_cross {
      opacity: 1;
    }
  }

  &__draggable {
    cursor: default;

    .field_cross {
      opacity: 1;
    }

    &:hover {

    }
  }
}


.item_category {
  font-size: 14px;
  height: 29px;
  padding: 5px 15px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: #FAFAFA;
}


.copy_button {
  position: absolute;
  top: 25%;
  right: 8px;
  width: 25px;
  cursor: pointer;

  &_icon {
    position: absolute;
    right: 1px;
    transition: all 0.2s ease-in-out;
    opacity: 1;
    color: #969FAD;

    &:hover {
      color: $paperbox-blue !important;
    }

  }

  &_check {
    position: absolute;
    top: -2px;
    right: -1px;
    width: 20px;
    height: auto;
    transition: all 0.3s ease-in-out;
    transform: scale(0);
    opacity: 0;
    color: $success !important;

  }

  &__clicked {

    .copy_button_icon {
      transform: scale(0);
      opacity: 0;
    }

    .copy_button_check {
      transform: scale(1);
      opacity: 1;
    }
  }
}

.top_select{
  [data-id="single-value"]{
    font-size: 12px !important;
  }
}

.top_button {
  font-family: 'Yantramanav', sans-serif;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 40px;
  padding: 8px 12px;
  cursor: pointer;
  transition: box-shadow 0.2s ease-in-out;
  transition-property: color, box-shadow, background-color;
  white-space: nowrap;
  color: black !important;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: white;
  gap: 8px;

  svg {
    margin-right: 3px;
    transition: box-shadow 0.2s ease-in-out;
    transition-property: color, box-shadow;
    color: #666A72;
  }

  &:hover:not(&__alt):not(:disabled) {
    color: $paperbox-blue !important;
    box-shadow: rgba(0, 0, 0, 0.1) 0 1px 4px;

    svg {
      color: $paperbox-blue;

    }
  }

  &__blue {
    color: white !important;
    background: $paperbox-blue;

    &:disabled {
      background: rgba($paperbox-blue, 0.5) !important;
    }

    &:hover:not(:disabled) {
      color: white !important;
      background: $paperbox-blue;
    }
  }

  &__delete {
    color: $error !important;
    border: 1px solid $error;
    background: white;

    &:hover:not(:disabled) {
      color: white !important;
      background: $error;
    }
  }

}


.action {
  width: 16px;

  svg {
    margin-top: 3px;
    color: black;
  }

  &:hover svg {
    color: $paperbox-blue;
  }

  &__delete:hover svg {
    color: $error;
  }
}


.item_button {

}


.input_wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 350px;
  gap: 8px;

  .input_slider {
    width: 100%;

    input[type=number] {

      border: 1px solid #EEEEEE;
      border-radius: 5px;
      background: #FAFAFA;

      &:hover:not(:disabled), &:focus:not(:disabled) {
        transition: 0.2s box-shadow ease-in-out;
        outline: none;
        box-shadow: 0 0 1px 2px $paperbox-blue--fade;
      }

      &:hover:not(.disabled) .arrow:not(.disabled) {
        svg path {
          fill: $paperbox-blue;
        }
      }
    }
  }

  .icon_input {
    padding-right: 40px;
  }

  .input_label {
    font-size: 14px;
    position: absolute;
  }

}


.input_label {
  font-size: 16px;
  top: 11px;
  right: 10px;
  user-select: none;
  pointer-events: none;
}


.check {
  color: $success !important;
}


.input {
  font-size: 13.333px;
  width: 100%;
  height: 35px;
  padding: 6px 10px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: #FAFAFA;

  &__error {
    background: rgba($error, 0.1);
    box-shadow: 0 0 1px 2px rgba($error, 0.5) !important;
  }

  &:focus:not(:disabled) {
    transition: 0.2s box-shadow ease-in-out;
    outline: none;
    box-shadow: 0 0 1px 2px $paperbox-blue--fade;
  }

  &:hover:not(:disabled):not(:focus) {
    transition: 0.2s box-shadow ease-in-out;
    outline: none;
    box-shadow: 0 0 2px 2px $paperbox-blue--fade-extra;
  }

  &:hover:not(.disabled) .arrow:not(.disabled) {
    svg path {
      fill: $paperbox-blue;
    }
  }

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    margin: 0;
    -webkit-appearance: none;
  }

  &:disabled {
    opacity: 0.5;
  }

  /* Firefox */
  &[type=number] {
    -moz-appearance: textfield;
  }

  &__number {
    width: 55px;
  }

  &__percent {
    width: 85px;
    padding-right: 40px;

  }

  &__days {
    width: 85px;
    padding-right: 40px;
  }

  &__short {
    width: 250px;
  }

  &__top {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background: white;
    gap: 4px;

    span {
      margin-top: 1px;
    }
  }

  &__large {
    line-height: 1.3;
    height: 120px;
    resize: none;
  }

  &__copy {
    padding-right: 40px;
  }

  &__secret_wrapper {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;

    svg {
      color: #969FAD;
    }
  }

}


.webhook_row {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 10px;
  gap: 10px;

  &_error {
    font-size: 14px;
    color: $error;

  }

  &_inner {
    display: flex;
    width: 100%;
    gap: 10px;
  }

  &_key {
    width: 25%
  }

  &_value {
    width: 75%;
  }

  &_button {

    display: flex;
    align-items: center;
    flex-shrink: 0;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-right: 0;
    margin-left: auto;
    border: 1px solid #EEEEEE;
    border-radius: 5px;
    background: #FAFAFA;

    svg {
      color: #969FAD;
    }

    &__active svg {
      color: $paperbox-blue;
    }

    &:disabled {
      opacity: 0.5;
    }

    &:hover:not(:disabled) {
      cursor: pointer;

      color: $paperbox-blue;

      svg {
        color: $paperbox-blue;

      }
    }
  }
}


.webhook_add {
  display: flex;
}


:global {
  .admin-section-anim-enter {
    z-index: 10;
    transition: transform 0.4s ease-in-out, opacity 0.2s ease-in-out;
    transform: translateY(-50px);
    transform-origin: top center;
    opacity: 0;
  }

  .admin-section-anim-enter-active {
    z-index: 10;
    transform: translateY(0px);
    opacity: 1;
  }

  .admin-section-anim-exit {
    z-index: 1;
    transition: transform 0.4s ease-in-out, opacity 0.2s ease-in-out;
    transform: translateY(0px);
    transform-origin: top center;
    opacity: 1;

  }

  .admin-section-anim-exit-active {
    z-index: 1;
    transform: translateY(50px);
    transform-origin: top center;
    opacity: 0;

  }
}


::-webkit-scrollbar-track {
  background: transparent !important;
}

// Dynamic mapping table styles - auto-sizing grid system
.dynamic_mapping_container {
  width: 100%;
  overflow: hidden; // Prevent container growth
}

.dynamic_mapping_grid {
  display: grid;
  width: 100%;
  max-width: 100%;
  overflow-x: auto; // Horizontal scroll if needed
  gap: 10px;
  row-gap:0;
  padding-right: 10px;
  margin-right: -10px;
  max-height: 500px;
  overflow-y: auto;

  // Grid template columns will be set dynamically via JavaScript
  // based on the actual number of columns being rendered
}

.mapping_header {
  position: sticky;
  z-index: 1;
  top: 0;
  display: flex;
  align-self: flex-start;
  height: 25px;
  background: white;
  gap: 8px;
  align-items: center;
  font-weight: 500;
  font-size: 13px;
  color: #333;

  &__center {
    justify-content: center;
  }

  &__right {
    justify-content: flex-end;
  }

  svg {
    width: 14px;
    min-width: 14px;
    transform: translateY(-1px) rotate(180deg);
    color: rgba(0, 0, 0, 0.23);
  }
}

.mapping_cell {
  display: flex;
  align-items: center;
  min-height: 40px;
  padding: 4px 0;

  &__center {
    justify-content: center;
  }

  &__right {
    justify-content: flex-end;
  }
}

// Switcher styles for toggle and action buttons
.switcher {
  display: flex;
  height: 35px;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  width: fit-content;
}

.switcher_option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  cursor: pointer;
  transition: color 0.2s ease;
  color: #cccccc;
  background: none;
  border: none;

  svg {
    width: 18px;
    height: auto;
  }

  & + & {
    border-left: 1px solid #EEEEEE;
  }

  &__active:not(&:disabled), &:hover:not(&:disabled) {
    color: $paperbox-blue;
  }

  &__delete:hover:not(&:disabled) {
    color: $error;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.3;
  }
}

// Examples editor styles - keeping compact
.examples_editor {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 16px;
}

.examples_empty {
  padding: 24px;
  text-align: center;
  color: #898B99;
  border: 2px dashed #EEEEEE;
  border-radius: 5px;
  background: #FAFAFA;
}

.example_item {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: white;
}

.example_editor {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 1px solid $paperbox-blue;
  border-radius: 5px;
  background: white;
}

.example_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #EEEEEE;
  background: #F8F9FA;

  span {
    font-weight: 500;
  }
}

.example_actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.example_edit,
.example_save,
.example_cancel {
  font-size: 12px;
  padding: 4px 8px;
  border: 1px solid #EEEEEE;
  border-radius: 3px;
  background: white;
  cursor: pointer;

  &:hover {
    background: $paperbox-blue--fade-extra;
  }
}

.example_save {
  background: $paperbox-blue;
  color: white;

  &:hover {
    background: darken($paperbox-blue, 10%);
  }
}

.example_remove {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: 1px solid #EEEEEE;
  border-radius: 3px;
  background: white;
  cursor: pointer;

  svg {
    color: #969FAD;
  }

  &:hover svg {
    color: $error;
  }
}

.example_preview {
  font-size: 12px;
  line-height: 1.4;
  padding: 12px 16px;
  margin: 0;
  color: #333;
  background: #F8F9FA;
  white-space: pre-wrap;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.example_textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  width: 100%;
  padding: 12px 16px;
  border: none;
  resize: vertical;
  outline: none;
  background: #F8F9FA;
  min-height: 120px;

  &:focus {
    background: white;
  }
}


