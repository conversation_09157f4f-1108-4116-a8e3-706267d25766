@import "@src/shared/styles/vars/_vars";


.page_grid {
  display: flex;
  overflow: auto;
  overflow-x: clip;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  max-height: 45vh;
  margin-top: 40px;
  flex-wrap: wrap;
  border-radius: 10px;
  gap: 30px;
  row-gap: 50px;
  padding: 50px 30px;
  background-color: #fcfcfc;
  border: 1px solid #EEEEEE;
}


.group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 10px;
  border: 1px solid $paperbox-blue--fade;
  gap: 20px;
}


.page {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 260px;
  padding: 15px 15px 5px 15px;
  user-select: none;
  aspect-ratio: 9/13;
  transition: aspect-ratio 400ms, transform 0.15s ease-in-out;

}


.page_image {
  position: absolute;
  display: inline-block;
  width: 85%;
  height: 85%;
  transform-origin: center;
  border-radius: 7px;
  border: 1px solid #EEEEEE;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &__faded {
    transform: scale(0.7) !important;
    box-shadow: none;
  }
}


.page_number {
  position: absolute;
  z-index: 110;
  top: -20px;
  left: calc(50% - 12px);
  font-size: 13px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out;
  border-radius: 5px;
  color: white;
  transform: translateX(-50%);
  background: $paperbox-blue;
  gap: 3px;

  svg {
    margin-right: 10px;
  }

  &_add {
    color: $success;
    background: $success-light;

  }

  &_discard {
    color: $error;
    background: $error-light;

  }
}


.split {
  width: 50px;
  height: 100%;
  position: absolute;
  left: -53px;
  top: 0;
  z-index: 100;

  &__after {
    left: unset;
    right: -27px;
  }

  .split_line {
    width: 0px;
    border-left: 2px dashed #EEEEEE;
    height: 80%;
    background: #EEEEEE;
    position: absolute;
    left: 50%;
    top: 10%;
    transform: translateX(-50%);
    z-index: 9;
    transition: border-left-color 0.15s ease-in-out;
  }

  .split_icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    width: 24px;
    height: 24px;
    background: $paperbox-blue;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    z-index: 10;
    transition: transform 0.15s ease-in-out, background 0.15s ease-in-out;

    svg {
      width: 14px;
      height: 14px;
    }
  }

  &.active {
    .split_line {
      border-color: $paperbox-blue;
    }

    &:hover {
      cursor: pointer;

      .split_icon {
        transform: translate(-50%, -50%) scale(1);
        background: $error;
      }

      .split_line {
        border-color: $error;
      }
    }
  }

  &:hover {
    cursor: pointer;

    .split_icon {
      transform: translate(-50%, -50%) scale(1);
    }

    .split_line {
      border-color: $paperbox-blue;
    }

  }
}


.icon {
  position: absolute;
  z-index: 101;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  color: white;
  border-radius: 5px;

  box-shadow: $shadow-light;

  &_add {
    background-color: $success;

  }

  &_discard {
    background-color: $error;

  }
}


.page_wrapper {
  padding: 20px;
  border: 2px solid transparent;

}


.sectionsContainer {
  margin-top: 20px;
  background-color: #fcfcfc;
  border-radius: 8px;
  border: 1px solid #EEEEEE;
  height: 0;
  min-height: 0;
  overflow: hidden;
  padding: 0px;
  opacity: 0;
  transition: height 0.3s ease, padding 0.3s ease, opacity 0.3s ease;

  &__visible {
    padding: 16px;
    opacity: 1;
    height: auto;
  }
}


.sectionTitle {
  font-size: 15px;
  font-weight: 500;
  color: $paperbox-blue;
  margin: 0 0 12px;
}


.sectionsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 10px;
}


.sectionItem {
  background: white;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #e0e9ff;
}


.sectionPages {
  font-size: 13px;
  color: #555;
}


