import InboxTableToolbar from '@components/inbox/table/InboxTableToolbar.tsx';
import {
  <PERSON><PERSON>ell,
  ActionTypeCell,
  ActorCell,
  ApprovalChecksCell,
  ConfidenceCell,
  DateCell,
  DocTypeCell,
  LockCell,
  NameCell,
  TagTypeCell,
  UserUpdateCell,
} from '@components/inbox/table/components/InboxTableCells.tsx';
import InboxTableLockableRow from '@components/inbox/table/components/InboxTableLockableRow.tsx';
import Checkbox from '@components/shared/checkbox/Checkbox';
import { IDocumentEnriched } from '@shared/helpers/converters/document.ts';
import { inboxTableColumnWidths } from '@shared/helpers/helpers';
import { useGetLockersListQuery } from '@shared/helpers/rtk-query/realtimeApi.ts';
import useKeyListener from '@shared/hooks/useKeyListener.ts';
import { InboxFilterState, updateFilters } from '@shared/store/documentListSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store';
import s from '@shared/styles/component/inbox/inbox-table.module.scss';
import { ReactComponent as ChevronDown } from '@svg/chevron-down.svg';
import { ReactComponent as ChevronUp } from '@svg/chevron-up.svg';
import clsx from 'clsx';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router';
import { Column, usePagination, useSortBy, useTable } from 'react-table';

interface Props {
  enabledColumns: Record<TableColumns, boolean>;
  pageCount: number;
  documents: IDocumentEnriched[];
  loading: boolean;
  goToDocument: (docId: string) => void;
  toolbarComponent?: React.ComponentType<{
    checkedRows: Set<string>;
    clearRows: () => void;
  }>;
}

export type TableColumns =
  | 'actor'
  | 'actionDate'
  | 'actionType'
  | 'name'
  | 'digitizedDate'
  | 'docTypeId'
  | 'tagTypeId'
  | 'confidence'
  | 'lastUserUpdate'
  | 'locker'
  | 'approvalChecks'
  | 'initialApprovalChecks';

const InboxTableNew: React.FC<Props> = ({
  pageCount,
  documents,
  goToDocument,
  enabledColumns,
  loading,
  toolbarComponent: ToolbarComponent = InboxTableToolbar
}) => {
  const { inboxId } = useParams();
  const dispatch = useDispatch();
  const { state } = useLocation();
  const { t } = useTranslation();

  const users = useSelector((state) => state.admin.users);
  const user = useSelector((state) => state.user.userAccount);
  const filters = useSelector((state) => state.documentList.filters);
  // const pagination = useSelector((state) => state.documentList.pagination);
  const { data: lockedList } = useGetLockersListQuery({ inboxId }, { skip: !inboxId });

  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [isShiftPressed, setIsShiftPressed] = useState<boolean>(false);
  const [lastCheckedRowIndex, setLastCheckedRowIndex] = useState<number | null>(null);

  useEffect(() => {
    setSelectedIds((prev) => {
      const newSet = new Set<string>();
      documents.forEach((doc) => {
        if (prev.has(doc.id)) newSet.add(doc.id);
      });
      return newSet;
    });
  }, [documents]);

  useKeyListener({
    key: 'Shift',
    onKeyDown: () => setIsShiftPressed(true),
    onKeyUp: () => setIsShiftPressed(false),
  });

  const handleRowCheck = useCallback(
    (rowIndex: number, rowData: IDocumentEnriched) => {
      setSelectedIds((prevSelected) => {
        const newSelected = new Set(prevSelected);
        const rowId = rowData.id;
        const alreadySelected = newSelected.has(rowId);

        if (isShiftPressed && lastCheckedRowIndex !== null) {
          const start = Math.min(lastCheckedRowIndex, rowIndex);
          const end = Math.max(lastCheckedRowIndex, rowIndex);
          const idsInRange = documents.slice(start, end + 1).map((doc) => doc.id);
          if (!alreadySelected) {
            idsInRange.forEach((id) => newSelected.add(id));
          } else {
            idsInRange.forEach((id) => newSelected.delete(id));
          }
        } else {
          // Toggle selection for the single row.
          if (alreadySelected) {
            newSelected.delete(rowId);
          } else {
            newSelected.add(rowId);
          }
        }
        return newSelected;
      });
      setLastCheckedRowIndex(rowIndex);
    },
    [isShiftPressed, lastCheckedRowIndex, documents],
  );

  // "Check All" / "Clear All" handler.
  const handleCheckAll = useCallback(() => {
    if (selectedIds.size === documents.length) {
      setSelectedIds(new Set());
    } else {
      setSelectedIds(new Set(documents.map((doc) => doc.id)));
    }
  }, [selectedIds, documents]);

  const checkIfLocked = useCallback(
    (docId: string) => (lockedList ? lockedList[docId] || null : null),
    [lockedList],
  );
  const getUserName = useCallback(
    (userId: string) => users.find((e) => e.id === userId)?.email ?? '',
    [users],
  );
  const columns = React.useMemo(() => {
    const columns: Column<any>[] = [];
    if (enabledColumns.locker)
      columns.push({
        id: 'locker',
        Header: '',
        accessor: 'locker',
        Cell: (c) => {
          return (
            <LockCell
              isSelected={selectedIds.has(c.row.original.id)}
              row={c.row}
              handleRowCheck={handleRowCheck}
              checkIfLocked={checkIfLocked}
              loading={loading}
            />
          );
        },
        disableSortBy: true,
      });
    if (enabledColumns.name)
      columns.push({
        id: 'name',
        Header: t('home:table.docName'),
        accessor: 'name',
        sortDescFirst: true,
        disableSortBy: false,
        Cell: (c) => <NameCell value={c.value} loading={loading} row={c.row} checkIfLocked={checkIfLocked} />,
      });
    if (enabledColumns.digitizedDate)
      columns.push({
        id: 'uploadTime',
        Header: t('home:table.dateAdded'),
        accessor: 'uploadTime',
        sortDescFirst: true,
        Cell: (c) => <DateCell value={c.value} row={c.row} checkIfLocked={checkIfLocked} loading={loading} />,
      });
    if (enabledColumns.docTypeId)
      columns.push({
        id: 'docTypeId',
        Header: t('home:table.docType'),
        accessor: 'docTypeId',
        Cell: (c) => (
          <DocTypeCell value={c.value} row={c.row} checkIfLocked={checkIfLocked} loading={loading} />
        ),
        disableSortBy: true,
      });

    if (enabledColumns.actor) {
      columns.push({
        id: 'actor',
        Header: t('home:table.approved'),
        accessor: 'action',
        Cell: (c) => (
          <ActorCell value={c.value} row={c.row} checkIfLocked={checkIfLocked} loading={loading} />
        ),
      });
    }
    if (enabledColumns.actionDate) {
      columns.push({
        id: 'actionDate',
        Header: t('home:table.approvalDate'),
        accessor: 'action.timestamp',
        Cell: (c) => (
          <ActionCell value={c.value} row={c.row} checkIfLocked={checkIfLocked} loading={loading} />
        ),
      });
    }
    if (enabledColumns.lastUserUpdate) {
      columns.push({
        id: 'lastUserUpdateTime',
        Header: t('home:table.lastEdited'),
        accessor: 'lastUserUpdateTime',
        Cell: (c) => (
          <UserUpdateCell
            getUserName={getUserName}
            value={c.value}
            row={c.row}
            checkIfLocked={checkIfLocked}
            loading={loading}
          />
        ),
      });
    }
    if (enabledColumns.tagTypeId) {
      columns.push({
        id: 'tagTypeId',
        Header: t('home:table.state'),
        accessor: 'tagTypeId',
        disableSortBy: filters.activeTagId != null,
        Cell: (c) => (
          <TagTypeCell value={c.value} row={c.row} checkIfLocked={checkIfLocked} loading={loading} />
        ),
      });
    }

    if (enabledColumns.actionType) {
      columns.push({
        id: 'actionType',
        Header: t('home:table.actionType'),
        accessor: 'action.type',
        disableSortBy: true,
        Cell: (c) => (
          <ActionTypeCell value={c.value} row={c.row} checkIfLocked={checkIfLocked} loading={loading} />
        ),
      });
    }

    if (enabledColumns.confidence)
      columns.push({
        id: 'confidence',
        Header: `${t('home:table.confidence')} (%)`,
        accessor: 'confidence',
        Cell: (c) => (
          <ConfidenceCell value={c.value} row={c.row} checkIfLocked={checkIfLocked} loading={loading} />
        ),
      });

    if (enabledColumns.approvalChecks)
      columns.push({
        Header: '',
        accessor: 'approval_checks',
        disableSortBy: true,
        Cell: (c) => (
          <ApprovalChecksCell value={c.value} loading={loading} row={c.row} checkIfLocked={checkIfLocked} />
        ),
      });
    if (enabledColumns.initialApprovalChecks)
      columns.push({
        Header: '',
        accessor: 'initial_approval_checks',
        disableSortBy: true,
        Cell: (c) => (
          <ApprovalChecksCell
            isInitialChecks
            value={c.value}
            loading={loading}
            row={c.row}
            checkIfLocked={checkIfLocked}
          />
        ),
      });

    return columns;
  }, [user, enabledColumns, t, loading, checkIfLocked, handleRowCheck, getUserName, selectedIds]);

  const {
    getTableProps,
    getTableBodyProps,
    headerGroups,
    prepareRow,
    page,
    state: { sortBy },
  } = useTable(
    {
      columns,
      data: documents || [],
      initialState: {
        pageIndex: filters.currentPageIndex,
        pageSize: filters.pageSize,
        sortBy: filters.sortBy ? [{ id: filters.sortBy, desc: filters.isSortDescending }] : [],
      },
      manualPagination: true,
      pageCount: pageCount,
      autoResetSortBy: false,
      autoResetPage: false,
      manualSortBy: true,
    },
    useSortBy,
    usePagination,
  );

  useEffect(() => {
    let update: Partial<InboxFilterState> = { navDirection: null, currentPageIndex: 0 };
    if (sortBy && sortBy.length > 0) {
      update = { ...update, sortBy: sortBy[0].id, isSortDescending: sortBy[0].desc };
    } else {
      update = { ...update, sortBy: null, isSortDescending: false };
    }
    dispatch(updateFilters(update));
  }, [dispatch, sortBy, state]);

  const makeStyle = (columnId: string, index) => {
    let style;

    if (columnId === 'locker') {
      style = {
        width: 60,
        minWidth: 60,
        maxWidth: 60,
        color: 'rgba(0, 0, 0, 0.54)',
      };
    } else {
      style = { width: inboxTableColumnWidths[columnId] || '12.5%' };
    }

    if (index === 0 && columnId !== 'locker') {
      style = { ...style, paddingLeft: 20 };
    }

    return style;
  };

  return (
    <>
      <ToolbarComponent checkedRows={selectedIds} clearRows={() => setSelectedIds(new Set())} />
      <div className={s.table_wrapper}>
        <table className={s.table} {...getTableProps()} data-testid="inbox-table">
          <thead className={s.thead}>
            {headerGroups.map((headerGroup) => (
              <tr className={s.head} key={headerGroup.id} {...headerGroup.getHeaderGroupProps()}>
                {headerGroup.headers.map((column, index) => {
                  const hasLockerCol = headerGroup.headers.find((e) => e.id === 'locker');
                  const isFirstRealCol = index === (hasLockerCol != null ? 1 : 0);
                  return (
                    <th
                      key={column.id}
                      data-testid={'inbox-table-th'}
                      {...column.getHeaderProps(column.getSortByToggleProps())}
                      style={{
                        ...makeStyle(column.id, index),
                        ...column.getHeaderProps(column.getSortByToggleProps()).style,
                      }}
                      className={s.cell}
                      align={isFirstRealCol ? 'left' : 'center'}
                    >
                      <div
                        className={clsx(s.sort_head, {
                          [s.sort_head__start]: isFirstRealCol,
                        })}
                      >
                        {column.id === 'locker' ? (
                          <Checkbox
                            indeterminate={selectedIds?.size !== documents?.length && selectedIds.size > 0}
                            onClick={handleCheckAll}
                            checked={selectedIds?.size === documents?.length && selectedIds.size > 0}
                          />
                        ) : (
                          column.render('Header')
                        )}
                        <span>
                          {column.isSorted &&
                            (column.isSortedDesc ? (
                              <ChevronUp className={s.sort_icon} />
                            ) : (
                              <ChevronDown className={s.sort_icon} />
                            ))}
                        </span>
                      </div>
                    </th>
                  );
                })}
              </tr>
            ))}
          </thead>
          <tbody {...getTableBodyProps()}>
            {page.map((row, i) => {
              prepareRow(row);
              const rowData = row.original as IDocumentEnriched;
              return (
                <InboxTableLockableRow
                  lockedList={lockedList}
                  loading={loading}
                  setIsRowChecked={(rowData) => handleRowCheck(i, rowData)}
                  key={row.id}
                  rowData={rowData}
                  isRowChecked={selectedIds.has(rowData.id)}
                  row={row}
                  goToDocument={goToDocument}
                />
              );
            })}
          </tbody>
        </table>
      </div>
    </>
  );
};

export default InboxTableNew;
