// 🔥 Centralized transformation function
import { DropdownOption } from '@components/shared/dropdown/StyledSelect.tsx';
import apiClient from '@shared/helpers/apiClient.ts';
import { IClientDocType } from '@shared/helpers/converters/doctype.ts';
import {
  IDocument,
  IDocumentEnriched,
  IDocumentEnrichedEntity,
  IDocumentEntity,
  IDocumentTopologyPartEnriched,
} from '@shared/helpers/converters/document.ts';
import { IClientFieldType } from '@shared/helpers/converters/fieldtype.ts';
import { IClientTag } from '@shared/helpers/converters/tag.ts';
import { isSameDate, normalizeEntity, unNormalizeEntity } from '@shared/helpers/helpers.ts';
import { InboxFilterState } from '@shared/store/documentListSlice.ts';
import { QueryConstraint, where } from 'firebase/firestore';
import { cloneDeep, isEqual } from 'lodash';
import { PendingOperation } from '../store/pendingOperationsSlice';

export const buildFilteredDocumentQuery = (inboxId: string, filters: InboxFilterState): QueryConstraint[] => {
  const constraints: QueryConstraint[] = [];

  // Base filters
  constraints.push(where('inbox_id', '==', inboxId));

  // Document type filtering
  if (filters.docTypeId) constraints.push(where('doc_type_id', '==', filters.docTypeId));
  if (filters.subTypeId) constraints.push(where('doc_subtype_id', '==', filters.subTypeId));

  // Tag filtering
  if (filters.activeTagId) {
    if (filters.activeTagId === '@NO_TAG') {
      constraints.push(where('tag_type_id', '==', null));
    } else {
      constraints.push(where('tag_type_id', '==', filters.activeTagId));
    }
  }

  return constraints;
};

export const sortEntities = (document: IDocument, entities: IDocumentEntity[]): IDocumentEntity[] => {
  const mapped = entities.map((entity) => {
    return unNormalizeEntity(entity, document);
  });
  return mapped
    .sort((a, b) => {
      if (!a.valueLocations[0] || !b.valueLocations[0]) return 0;
      return (
        a.valueLocations[0].y1 + a.valueLocations[0].y2 - b.valueLocations[0].y1 - b.valueLocations[0].y2
      );
    })
    .sort((a, b) => {
      return a.id.localeCompare(b.id);
    });
};
// TypeScript
export const reconcileEntities = (
  newDoc: IDocument,
  pendingOperations: Record<string, PendingOperation>,
): { reconciledDoc: IDocument; remainingOperations: Record<string, PendingOperation> } => {
  console.group('Reconcile Entities', newDoc);
  // Deep clone to avoid mutations
  const reconciledDoc: IDocument = cloneDeep(newDoc);
  const remainingOperations: Record<string, PendingOperation> = { ...pendingOperations };

  if (!reconciledDoc.entities) {
    reconciledDoc.entities = [];
  }

  // Create a map of existing entities for easier access
  const firestoreEntityMap = new Map<string, IDocumentEntity>(
    reconciledDoc.entities.map((entity) => [entity.id, entity]),
  );

  // Process each pending operation in timestamp order (oldest first)
  const sortedOperations = Object.values(pendingOperations).sort((a, b) => a.timestamp - b.timestamp);

  for (const operation of sortedOperations) {
    const { type, entityId, childId, data: operationData } = operation;
    // Find the operation key in the collection
    const operationKey = Object.keys(remainingOperations).find(
      (key) => remainingOperations[key] === operation,
    );
    if (!operationKey) continue;

    switch (type) {
      case 'add': {
        // For a pending add, if the entity doesn't exist in Firestore, add it to the reconciled doc
        if (!firestoreEntityMap.has(entityId) && operationData) {
          reconciledDoc.entities.push(operationData as IDocumentEntity);
          firestoreEntityMap.set(entityId, operationData as IDocumentEntity);
        } else if (firestoreEntityMap.has(entityId)) {
          // If the entity exists in Firestore, we can remove the pending operation
          delete remainingOperations[operationKey];
        }
        break;
      }

      case 'edit': {
        const entity = firestoreEntityMap.get(entityId);
        if (entity) {
          // For complex nested edits
          if (childId && entity.value && typeof entity.value === 'object' && 'complex' in entity.value) {
            const childValue = entity.value.complex[childId];
            const childData = operationData.value['complex'][childId];
            if (isEqual(childValue, childData)) {
              // Data matches, so remove the pending operation
              delete remainingOperations[operationKey];
            } else {
              // Apply the edit to the reconciled document
              entity.value.complex[childId] = {
                ...childValue,
                ...operationData.value['complex'][childId],
              };
            }
          } else {
            if (isEqual(entity, operationData)) {
              // Data matches, so remove the pending operation
              delete remainingOperations[operationKey];
            } else {
              // Apply the edit to the reconciled document
              const updatedEntity = operationData as IDocumentEntity;
              const index = reconciledDoc.entities.findIndex((e) => e.id === entityId);
              if (index !== -1) {
                reconciledDoc.entities[index] = updatedEntity;
                firestoreEntityMap.set(entityId, updatedEntity);
              }
            }
          }
        }
        break;
      }

      case 'delete': {
        if (childId) {
          // Handle complex field deletion
          const fsEntity = firestoreEntityMap.get(entityId);
          if (fsEntity?.value && typeof fsEntity.value === 'object' && 'complex' in fsEntity.value) {
            const childValue = fsEntity.value.complex[childId];
            if (
              childValue.value === null &&
              childValue.pageNo === null &&
              childValue.valueLocations.length === 0
            ) {
              // Child field is already cleared in Firestore, remove the pending operation
              delete remainingOperations[operationKey];
            } else {
              // Apply the deletion to the reconciled document
              fsEntity.value.complex[childId] = {
                ...fsEntity.value.complex[childId],
                value: null,
                pageNo: null,
                valueLocations: [],
              };
            }
          }
        } else {
          // For entity deletion
          const idx = reconciledDoc.entities.findIndex((e) => e.id === entityId);
          if (idx === -1) {
            // Entity is already gone from Firestore, remove the pending operation
            delete remainingOperations[operationKey];
          } else {
            // Apply the deletion to the reconciled document
            reconciledDoc.entities.splice(idx, 1);
            firestoreEntityMap.delete(entityId);
          }
        }
        break;
      }
    }
  }
  console.groupEnd();

  return { reconciledDoc, remainingOperations };
};

/*
 * Enriches a document with docType and fieldType details.
 */
export const enrichDocumentWithTypes = (
  document: IDocument,
  docTypes: IClientDocType[],
  fieldTypes: IClientFieldType[],
  tagTypes: IClientTag[],
): IDocumentEnriched => {
  console.log('Document:', document);
  const enriched: IDocumentEnriched = cloneDeep(document);

  // Set document type details
  const doctype = docTypes.find((d) => d.id === document?.docTypeId);
  if (doctype) {
    enriched.docTypeDetails = doctype;
    if (document.docSubtypeId) {
      const subtype = doctype?.subtypes.find((d) => d.id === document.docSubtypeId);
      if (subtype) enriched.docTypeDetails = { ...doctype, subTypeDetails: subtype };
    }
  }

  // Enrich topology parts
  if (document.topology) {
    enriched.topology.parts = document.topology.parts.map((part) => {
      const docType = docTypes.find((d) => d.id === part.docTypeId);
      const clone = { ...part } as IDocumentTopologyPartEnriched;
      if (docType) {
        clone.docTypeDetails = docType;
        if (part.docSubtypeId) {
          const subtype = docType?.subtypes.find((d) => d.id === part.docSubtypeId);
          if (subtype) clone.docTypeDetails = { ...docType, subTypeDetails: subtype };
        }
      }
      return clone;
    });
  }

  // Set tag details
  if (document.tagTypeId) {
    enriched.tagDetails = tagTypes.find((d) => d.id === document.tagTypeId);
  }

  // Enrich entities with field type details
  if (enriched.entities) {
    for (const entity of enriched.entities) {
      const fieldType = fieldTypes.find((f) => f.id === entity.type);
      entity.typeDetails = {
        id: 'UNKNOWN',
        name: 'UNKNOWN',
        type: null,
        options: [],
      };

      if (fieldType) entity.typeDetails = fieldType;

      // Handle complex entities
      if (entity.value && typeof entity.value === 'object' && 'complex' in entity.value) {
        const enrichedComplex = {};
        Object.entries(entity.value.complex).forEach(([key, value]) => {
          const enrichedValue = { ...value };
          const fieldType = fieldTypes.find((f) => f.id === value.type);
          if (fieldType) enrichedValue.typeDetails = fieldType;
          enrichedComplex[key] = enrichedValue;
        });
        entity.value.complex = enrichedComplex;
      }
    }
  }

  return enriched;
};

export const listToDropdownOptions = (
  list: any[],
  labelKey: string,
  valueKey: string,
  tagKey?: string,
): DropdownOption[] => {
  return list.map((item) => {
    return {
      label: item[labelKey],
      value: item[valueKey],
      tag: item[tagKey],
    };
  });
};

export const isDefaultDateRange = (startDate: Date, endDate: Date) => {
  const defaultStartDate = new Date();
  defaultStartDate.setDate(defaultStartDate.getDate() - 29);
  const defaultEndDate = new Date();
  return isSameDate(startDate, defaultStartDate) && isSameDate(endDate, defaultEndDate);
};

function pFileReader(data: Blob) {
  return new Promise((resolve, reject) => {
    const fr = new FileReader();
    fr.onloadend = () => resolve(fr.result); // CHANGE to whatever function you want which would eventually call resolve
    fr.onerror = reject;
    fr.readAsDataURL(data);
  });
}

export const getEntityCropThumb = async (
  docId: string,
  inboxId: string,
  entity: IDocumentEnrichedEntity,
  document: IDocumentEnriched,
) => {
  if (!document) return;

  // Normalizing the entity using its associated document details
  const normalizedEntity = normalizeEntity(entity, document);
  const loc = normalizedEntity.valueLocations[0];

  // Building the URL for the specific cropped thumbnail of the entity
  const url = `${import.meta.env.VITE_PAPERBOX_CDN_URL}/inboxes/${inboxId}/documents/${docId}/pages/${
    entity.pageNo
  }?x1=${loc.x1}&x2=${loc.x2}&y1=${loc.y1}&y2=${loc.y2}&w=150`;

  const e = await apiClient.get(url, {
    responseType: 'blob',
    headers: {
      accept: 'image/*',
    },
  });

  return e.data ? await pFileReader(e.data) : null;
};
