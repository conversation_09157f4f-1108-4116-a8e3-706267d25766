import { ReactComponent as Paper<PERSON><PERSON>ogo } from '@svg/paperbox-logo-small.svg';
import { RaceBy } from '@uiball/loaders';
import React from 'react';

const PageLoader: React.FC = () => {
  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          padding: 35,
          background: 'white',
          boxShadow: 'rgba(0, 0, 0, 0.10) 0 3px 8px',
          borderRadius: 10,
        }}
      >
        <PaperboxLogo style={{ width: 60, height: 'auto' }} />
      </div>
      <RaceBy size={100} lineWeight={5} color={'#0085FF'} />
    </>
  );
};

export default PageLoader;
