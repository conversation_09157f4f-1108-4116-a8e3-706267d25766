import OnboardingImage from '@shared/assets/onboarding-1.jpg';
import s from '@shared/styles/component/auth/auth.module.scss';
import clsx from 'clsx';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';
import Skeleton from 'react-loading-skeleton';
import { SystemOptions } from './onboarding-types';

interface Props {
  systemOptions: SystemOptions;
  activeSystem: string;
  setActiveSystem: (system: string) => void;
  isLoading?: boolean;
}

const TenantOnboardingStep1: React.FC<Props> = ({
  systemOptions,
  activeSystem,
  setActiveSystem,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  return (
    <div className={clsx(s.card_inner, s.card_inner__flex)}>
      <div className={s.card_inner_left}>
        <img
          src={OnboardingImage}
          alt={t('home:onboarding.title1')}
          loading="eager"
          style={{ width: '305px' }}
        />
      </div>
      <div className={s.card_inner_right}>
        <h2 className={s.card_inner_title}>{t('home:onboarding.subTitle1')}</h2>
        <div className={s.system_options}>
          {isLoading
            ? // Skeleton loader for system options
              Array(3)
                .fill(0)
                .map((_, index) => (
                  <div className={s.system_option} key={`skeleton-${index}`}>
                    <div className={s.system_option_content}>
                      <h3 className={s.system_option_title}>
                        <Skeleton width={120} height={16} />
                      </h3>
                      <p className={s.system_option_description}>
                        <Skeleton width={200} height={14} />
                      </p>
                    </div>
                    <Skeleton circle width={20} height={20} />
                  </div>
                ))
            : systemOptions.map((option) => {
                const active = option.value === activeSystem;
                return (
                  <div
                    onClick={() => setActiveSystem(option.value)}
                    className={clsx(s.system_option, { [s.system_option__active]: active })}
                    key={option.value}
                    data-testid={`system-option-${option.value}`}
                  >
                    <div className={s.system_option_content}>
                      <h3 className={s.system_option_title}>{option.label}</h3>
                      <p className={s.system_option_description}>{option.description}</p>
                    </div>
                    <div className={clsx(s.system_option_radio, { [s.system_option_radio__active]: active })}>
                      <div className={s.system_option_radio_inner} />
                    </div>
                  </div>
                );
              })}
        </div>
      </div>
    </div>
  );
};

// Memo the component since it only needs to re-render when activeSystem changes
export default memo(TenantOnboardingStep1);
