import React, { useCallback, useEffect } from 'react';
import { useTour } from '@reactour/tour';
import { useTranslation } from 'react-i18next';
import s from './labeler-tour.module.scss';

const LabelerTour: React.FC = () => {
  const { setSteps } = useTour();
  const { t } = useTranslation();

  const createSteps = useCallback(() => {
    const steps: any[] = [
      {
        selector: '[data-tour="fields"]',
        content: () => (
          <div className={s.wrapper}>
            <h3>{t('document:tour.intro.title')}</h3>
            <p>{t('document:tour.intro.content')}</p>
          </div>
        ),
      },
    ];

    setSteps(steps);
  }, [setSteps, t]);

  useEffect(() => {
    createSteps();
  }, [createSteps]);

  return null;
};

export default LabelerTour;
