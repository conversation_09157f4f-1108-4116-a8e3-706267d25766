import DynamicMappingTable, {
  ColumnDefinition,
  RowData,
} from '@components/admin/components/DynamicMappingTable.tsx';
import { Mapping, Property } from '@shared/helpers/converters/datasource.ts';
import { cloneDeep } from 'lodash';
import React from 'react';

interface Props {
  mappings: Mapping;
  onChange: (mappings: Mapping) => void;
  title: string;
  description: string;
}

const AdminDatasourceMappingEditor: React.FC<Props> = ({ mappings, onChange, title, description }) => {
  // Column definitions for the mapping table
  const columns: ColumnDefinition[] = [
    {
      type: 'title',
      key: 'name',
      label: 'Field Name',
      tooltip: 'The name of the field in the mapping',
      width: 'minmax(150px, 1fr)',
      placeholder: 'Field name',
    },
    {
      key: 'type',
      label: 'Type',
      tooltip: 'The data type for this field',
      type: 'dropdown',
      width: '150px',
      align: 'left',
      options: [
        { label: 'Text', value: 'text' },
        { label: 'Number', value: 'number' },
        { label: 'Boolean', value: 'boolean' },
        { label: 'Date', value: 'date' },
        { label: 'Object', value: 'object' },
        { label: 'ID', value: 'id' },
        { label: 'Nested', value: 'nested' },
      ],
    },
    {
      hideHeader: true,
      key: 'is_searchable',
      label: 'Searchable',
      tooltip: 'Whether this field can be searched',
      type: 'toggle',
      width: 'auto',
    },
    {
      hideHeader: true,
      label: 'Chars to filter',
      tooltip: 'test',
      key: 'chars_to_filter',
      type: 'chars-filter',
      width: 'auto',
    },
  ];

  const isProperty = (value: Property | Mapping): value is Property => {
    return value && typeof value === 'object' && 'type' in value;
  };

  // Convert mappings to row data for the dynamic table
  const convertMappingsToRows = (mapping: Mapping, parentPath = '', level = 0): RowData[] => {
    const result: RowData[] = [];

    Object.entries(mapping).forEach(([key, value]) => {
      const currentPath = parentPath ? `${parentPath}.${key}` : key;

      if (isProperty(value)) {
        result.push({
          id: currentPath,
          name: key,
          type: value.type,
          is_searchable: value.is_searchable || false,
          chars_to_filter: value.chars_to_filter || [],
          level,
        });
      } else {
        // For nested mappings, flatten recursively
        result.push(...convertMappingsToRows(value, currentPath, level + 1));
      }
    });

    return result;
  };

  const rows = convertMappingsToRows(mappings);

  const handleRowChange = (rowId: string, field: string, value: any) => {
    const newMappings = cloneDeep(mappings);
    const pathParts = rowId.split('.');
    const fieldName = pathParts.pop();

    let current = newMappings;
    for (const part of pathParts) {
      current = current[part] as Mapping;
    }

    if (field === 'name') {
      // Handle field renaming
      if (!value.trim() || value === fieldName) return;
      if (current[value]) return; // Name already exists

      current[value] = current[fieldName];
      delete current[fieldName];
    } else if (isProperty(current[fieldName])) {
      // Handle other field updates
      if (field === 'chars_to_filter') {
        (current[fieldName] as Property).chars_to_filter =
          Array.isArray(value) && value.length > 0 ? value : null;
      } else {
        const property = current[fieldName] as Property;
        (property as any)[field] = value;
      }
    }

    onChange(newMappings);
  };

  // const handleRowDelete = (rowId: string) => {
  //   const newMappings = cloneDeep(mappings);
  //   const pathParts = rowId.split('.');
  //   const fieldName = pathParts.pop();
  //
  //   let current = newMappings;
  //   for (const part of pathParts) {
  //     current = current[part] as Mapping;
  //   }
  //
  //   delete current[fieldName];
  //   onChange(newMappings);
  // };

  return (
    <DynamicMappingTable
      title={title}
      description={description}
      columns={columns}
      rows={rows}
      onRowChange={handleRowChange}
    />
  );
};

export default AdminDatasourceMappingEditor;
