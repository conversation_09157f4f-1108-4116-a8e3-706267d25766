import Recaptcha from '@components/auth/helpers/Recaptcha';
import AuthButton from '@components/auth/methods/AuthButton';
import GoogleButton from '@components/auth/methods/GoogleButton';
import SSOButton from '@components/auth/methods/SSOButton';
import Input from '@components/shared/input/Input';
import { TwoFactorCodeInput } from '@components/two-factor/TwoFactorCodeInput';
import { generateSignInEmail } from '@shared/store/adminSlice';
import { auth } from '@shared/store/setup/firebase-setup';
import { useDispatch, useSelector } from '@shared/store/store';
import { getTenantLoginInfo } from '@shared/store/tenantSlice';
import userSlice, { resetPassword } from '@shared/store/userSlice';
import s from '@shared/styles/component/auth/auth.module.scss';
import { ReactComponent as PaperboxLogo } from '@svg/paperbox-logo.svg';
import { Pulsar } from '@uiball/loaders';
import clsx from 'clsx';
import {
  PhoneAuthProvider,
  PhoneMultiFactorGenerator,
  getMultiFactorResolver,
  sendEmailVerification,
  signInWithEmailAndPassword,
} from 'firebase/auth';
import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { useResizeDetector } from 'react-resize-detector';
import { useLocation, useNavigate } from 'react-router';
import { CSSTransition } from 'react-transition-group';
import AuthOTPSent from './AuthOTPSent';

interface Props {}

const AuthLogin: React.FC<Props> = () => {
  const isPopupActive = useSelector((state) => state.user.isAuthPopupActive);
  const providers = useSelector((state) => state.tenant.providers);
  const allowedDomains = useSelector((state) => state.tenant.details.settings?.allowedDomains);
  const tenantConfig = useSelector((state) => state.tenant.details.config);
  const tenantId = useSelector((state) => state.tenant.tenantId);

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { state } = useLocation();

  const { ref } = useResizeDetector({
    refreshRate: 1,
    refreshMode: 'throttle',
  });

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errorCode, setErrorCode] = useState('');
  const [emailSent, setEmailSent] = useState(false);
  const [passwordReset, setPasswordReset] = useState(false);
  const [MFAResolver, setMFAResolver] = useState<any>();
  const [userHas2FA, setUserHas2FA] = useState(false);
  const [smsCode, setSmsCode] = useState('');
  const [verificationId, setVerificationId] = useState('');
  const [innerHeight, setInnerHeight] = useState(450);
  const [isRunning, setIsRunning] = useState(false);

  const calcHeight = (el) => {
    setInnerHeight(el.offsetHeight);
  };

  useEffect(() => {
    if (state === 'SIGNOUT') {
      auth.signOut().then(() => {
        let [path] = window.location.hostname.split('.');
        if (path === 'localhost') {
          path = import.meta.env.VITE_TEST ? 'tst-testing' : 'app';
        }
        getTenantLoginInfo(path, dispatch);
      });
    }
  }, [dispatch, state]);

  const handleSubmit = async (event: FormEvent) => {
    event.preventDefault();
    setErrorCode('');
    setIsRunning(true);
    let filteredDomains = [...allowedDomains];
    if (tenantId !== 'demo-tu27c' && tenantId !== 'tst-testing-7kmaf') {
      filteredDomains = filteredDomains.filter((domain) => domain !== 'paperbox.ai');
    }
    if (filteredDomains.includes(email.split('@')[1])) {
      if (password) {
        await signInWithEmailAndPassword(auth, email, password)
          .then((res) => {
            window['recaptchaVerifier'] = null;
            if (res.user.emailVerified) {
              navigate('/inbox', { replace: true });
            } else {
              sendEmailVerification(res.user);
              setErrorCode('auth/not-verified');
            }
          })
          .catch((err) => {
            handleError(err);
          })
          .finally(() => setIsRunning(false));
      } else {
        dispatch(generateSignInEmail(email))
          .then(() => {
            setEmailSent(true);
          })
          .catch(() => {
            setErrorCode('auth/user-not-found');
          })
          .finally(() => setIsRunning(false));
      }
    } else {
      handleError({ code: 'domain-not-allowed' });
      setIsRunning(false);
    }
  };

  const handleError = (err) => {
    console.log(err);
    setErrorCode(err.code);
    dispatch(userSlice.actions.setIsAuthPopupActive(false));
    if (err.code === 'auth/multi-factor-auth-required') {
      // The user is a multi-factor user. Second factor challenge is required.
      const resolver = getMultiFactorResolver(auth, err);
      const appVerifier = window['recaptchaVerifier'];
      setMFAResolver(resolver);
      setUserHas2FA(true);

      const phoneInfoOptions = {
        multiFactorHint: resolver.hints[0],
        session: resolver.session,
      };

      const phoneAuthProvider = new PhoneAuthProvider(auth);
      // Send SMS verification code.
      phoneAuthProvider.verifyPhoneNumber(phoneInfoOptions, appVerifier).then((verificationId) => {
        setVerificationId(verificationId);
      });
    } else if (err.code === 'auth/account-exists-with-different-credential') {
      // TODO: HANDLE FLOW
    } else if (err.code === 'auth/too-many-request') {
      setErrorCode(err.code);
    }
  };

  const confirmSMSCode = useCallback(
    (e) => {
      e.preventDefault();
      if (errorCode === 'auth/invalid-verification-code') setErrorCode('');
      const cred = PhoneAuthProvider.credential(verificationId, smsCode);
      const multiFactorAssertion = PhoneMultiFactorGenerator.assertion(cred);
      MFAResolver.resolveSignIn(multiFactorAssertion)
        .then((res) => {
          let allowed = false;

          allowedDomains.forEach((item) => {
            if (res.user.email.endsWith(`@${item}`)) {
              allowed = true;
            }
          });
          if (allowed) {
            navigate('inbox');
          } else {
            auth.signOut();
            navigate('login');
          }
        })
        .catch((err) => {
          setErrorCode(err.code);
        });
    },
    [MFAResolver, errorCode, navigate, allowedDomains, smsCode, verificationId],
  );

  useEffect(() => {
    if (ref.current && passwordReset != null) {
      calcHeight(ref.current);
    }
  }, [ref, passwordReset]);

  const errorMessage = useMemo(() => {
    if (errorCode === 'auth/user-not-found') {
      return 'User not found';
    }
    if (errorCode === 'auth/wrong-password') {
      return 'Wrong password';
    }
    if (errorCode === 'auth/too-many-requests') {
      return 'Too many requests';
    }
    if (errorCode === 'domain-not-allowed') {
      return 'Domain not allowed';
    }
    return 'Something went wrong, please try again';
  }, [errorCode]);

  const isDividerVisible = useMemo(() => {
    // Check if there are any actual rendered login methods above the separator
    const hasGoogleLogin =
      tenantConfig.googleLogin &&
      providers.some((provider) => provider?.name.includes('defaultSupportedIdpConfigs/google.com'));

    const hasSSOLogin = providers.some(
      (provider) => provider.type === 'inboundSamlConfigs' || provider.type === 'oauthIdpConfigs',
    );
    const hasEmailPasswordLogin = providers.some((provider) => provider.type === 'EmailPassword');

    // Only show divider if there are actual login methods above AND email/password login below
    return (hasGoogleLogin || hasSSOLogin) && hasEmailPasswordLogin;
  }, [providers, tenantConfig]);

  return (
    <div className={s.container}>
      <div className={s.card}>
        <Recaptcha />
        <PaperboxLogo className={s.logo} />
        <h2 className={s.title}>
          {passwordReset
            ? 'Please enter your email to reset your password'
            : 'Hello, Welcome back to Paperbox'}
        </h2>
        {userHas2FA ? (
          <>
            <div className={s.mfa}>
              <TwoFactorCodeInput
                setValue={setSmsCode}
                error={errorCode === 'auth/invalid-verification-code' ? errorCode : null}
              />
            </div>
            <button
              onClick={confirmSMSCode}
              className={clsx(s.button, s.button_main, s.button_continue, {
                [s.button_continue_active]: smsCode.length === 6,
              })}
            >
              Confirm
            </button>
          </>
        ) : (
          <>
            {isPopupActive && (
              <div className={s.loading}>
                <Pulsar size={50} color={'#0085FF'} /> Authenticating
              </div>
            )}
            {!isPopupActive && (
              <div className={s.card_inner} style={{ height: innerHeight }}>
                <CSSTransition
                  unmountOnExit
                  mountOnEnter
                  classNames={'auth-slide'}
                  timeout={500}
                  onEnter={calcHeight}
                  in={!emailSent}
                >
                  <>
                    {!passwordReset && (
                      <div ref={ref} className={s.buttons}>
                        {providers.map((provider) => {
                          if (provider.type === 'inboundSamlConfigs') {
                            return (
                              <SSOButton
                                text={`Log in with ${provider.displayName}`}
                                handleError={handleError}
                                type="inboundSamlConfigs"
                              />
                            );
                          }
                          if (provider.type === 'oauthIdpConfigs') {
                            return (
                              <SSOButton
                                text={`Log in with ${provider.displayName}`}
                                handleError={handleError}
                                type="oauthIdpConfigs"
                              />
                            );
                          }
                          if (
                            tenantConfig.googleLogin &&
                            provider?.name.includes('defaultSupportedIdpConfigs/google.com')
                          ) {
                            return (
                              <GoogleButton
                                key={provider.name}
                                text={'Log in with Google'}
                                handleError={handleError}
                              />
                            );
                          }
                          return null;
                        })}

                        {isDividerVisible && (
                          <div className={s.divider}>
                            <div className={s.divider_text}>OR</div>
                          </div>
                        )}

                        {providers.filter((item) => item.type === 'EmailPassword').length >= 1 && (
                          <form onSubmit={handleSubmit} autoComplete="on">
                            <div className={s.input}>
                              <Input
                                errorText={errorMessage}
                                hasError={
                                  errorCode === 'auth/user-not-found' ||
                                  errorCode === 'auth/not-verified' ||
                                  errorCode === 'auth/empty-email' ||
                                  errorCode === 'domain-not-allowed'
                                }
                                setValue={(v) => {
                                  setErrorCode(null);
                                  setEmail(v);
                                }}
                                value={email}
                                id="email"
                                type="email"
                                placeholder={'Enter your email'}
                              />
                            </div>
                            <div className={s.input}>
                              <Input
                                errorText={
                                  errorCode === 'auth/wrong-password'
                                    ? 'Password incorrect, please try again.'
                                    : 'Too many failed attempts, try again later'
                                }
                                hasError={
                                  errorCode === 'auth/wrong-password' ||
                                  errorCode === 'auth/too-many-requests'
                                }
                                required={false}
                                setValue={setPassword}
                                value={password}
                                id="password"
                                type="password"
                                placeholder={'Password (Optional)'}
                              />
                            </div>
                            <AuthButton data-testid="login-button" isActive={!!email} isLoading={isRunning}>
                              {password ? 'Log in' : 'Log in by email'}
                            </AuthButton>
                            <div onClick={() => setPasswordReset(true)} className={s.reset_password}>
                              Forgot password?
                            </div>
                          </form>
                        )}
                      </div>
                    )}

                    {passwordReset && (
                      <form
                        onSubmit={(e) => {
                          e.preventDefault();
                          setIsRunning(true);
                          dispatch(resetPassword(email)).then(() => {
                            setEmailSent(true);
                          });
                        }}
                        ref={ref}
                        className={s.buttons}
                      >
                        <div className={s.input}>
                          <Input
                            setValue={(v) => {
                              setErrorCode(null);
                              setEmail(v);
                            }}
                            value={email}
                            id="email"
                            type="email"
                            placeholder={'Enter your email'}
                          />
                        </div>
                        <AuthButton
                          disabled={!email}
                          data-testid="reset-button"
                          isActive={!!email}
                          isLoading={isRunning}
                        >
                          Reset Password
                        </AuthButton>
                      </form>
                    )}
                  </>
                </CSSTransition>

                <CSSTransition
                  onEnter={calcHeight}
                  unmountOnExit
                  mountOnEnter
                  classNames={'auth-slide'}
                  timeout={500}
                  in={emailSent}
                >
                  <AuthOTPSent email={email} type={passwordReset ? 'password' : 'login'} />
                </CSSTransition>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default AuthLogin;
