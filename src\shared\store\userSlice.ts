import { PayloadAction, createSelector, createSlice } from '@reduxjs/toolkit';
import { getUserToken, isPbxEmail } from '@shared/helpers/helpers';
import { Inbox } from '@shared/models/inbox';
import { UserAccount, UserPreferences } from '@shared/models/user';
import camelcaseKeys from 'camelcase-keys';
import { collection, doc, getDoc, getDocs, onSnapshot } from 'firebase/firestore';
import { api, auth, db } from './setup/firebase-setup';
import { AppThunk } from './store';

interface UserState {
  userAccount?: UserAccount;
  intercomHash?: string;
  inboxes?: Inbox[];
  isAuthPopupActive?: boolean;
  emailPreferenceSet?: boolean;
  isTenantInvalid?: boolean;
  isInboxesLoading?: boolean;
}

const initialState: UserState = {
  userAccount: {
    inboxes: [],
    email: null,
    isAdmin: false,
  },
  inboxes: [],
  isInboxesLoading: true,
};

export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    clearStore: (state) => Object.assign(state, initialState),

    setIsAuthPopupActive: (state, action: PayloadAction<boolean>) => {
      state.isAuthPopupActive = action.payload;
    },

    setIsTenantInvalid: (state, action: PayloadAction<boolean>) => {
      state.isTenantInvalid = action.payload;
    },

    setEmailPreferenceSet: (state, action: PayloadAction<boolean>) => {
      state.emailPreferenceSet = action.payload;
    },

    setUserAccount: (state, action: PayloadAction<UserAccount>) => {
      state.userAccount = action.payload;
    },

    setInboxes: (state, action: PayloadAction<Inbox[]>) => {
      state.inboxes = action.payload;
    },
    setIntercomHash: (state, action: PayloadAction<string>) => {
      state.intercomHash = action.payload;
    },
    setInboxesLoading: (state, action: PayloadAction<boolean>) => {
      state.isInboxesLoading = action.payload;
    },
  },
});

export const userInboxesSelector = createSelector(
  [(state) => state.user.inboxes, (state) => state.user.userAccount],
  (inboxes, user) => {
    return inboxes
      ?.filter((e) => user.inboxes.includes(e.id))
      .sort((a, b) => a.settings.name.localeCompare(b.settings.name));
  },
);

export const isPBXEmailSelector = createSelector([(state) => state.userAccount], (userAccount) => {
  if (!userAccount) return false;
  const { email } = userAccount;

  return email.endsWith('@paperbox.ai') || email.endsWith('@intern.paperbox.ai');
});

export const getInboxes = (inboxIds?: string[]): AppThunk => {
  return async (dispatch, getState) => {
    const tenantId = getState().tenant.tenantId;
    const currentInboxes = getState().user.inboxes || [];

    if (currentInboxes.length === 0) {
      dispatch(userSlice.actions.setInboxesLoading(true));
    }

    try {
      if (inboxIds) {
        const refs = inboxIds.map((id) => doc(db, `tenants/${tenantId}/inboxes/${id}`));
        const docs = await Promise.all(refs.map((ref) => getDoc(ref)));
        const localInboxes = docs
          .map((res, index) => {
            const inbox = camelcaseKeys(res.data(), { deep: true }) as Inbox;
            if (inbox) {
              inbox.id = inboxIds[index];
              return inbox;
            }
            return null;
          })
          .filter((inbox) => inbox !== null) as Inbox[];

        console.log('ererer');
        dispatch(userSlice.actions.setInboxes(localInboxes));
        dispatch(userSlice.actions.setInboxesLoading(false));
      } else {
        const inboxesRef = collection(db, `tenants/${tenantId}/inboxes`);
        const inboxesSnapshot = await getDocs(inboxesRef);
        const inboxes = inboxesSnapshot.docs.map((doc) => {
          const inbox = camelcaseKeys(doc.data(), { deep: true }) as Inbox;
          inbox.id = doc.id;
          return inbox;
        });
        dispatch(userSlice.actions.setInboxes(inboxes));
        dispatch(userSlice.actions.setInboxesLoading(false));
      }
    } catch (error) {
      console.error('Error fetching inboxes:', error);
    }
  };
};

export const patchUserPreferences = (preferences: UserPreferences) => async () => {
  const b = await getUserToken();
  if (!b) return;
  return await api.patch(`${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/users/me/preferences`, preferences, {
    headers: {
      accept: 'application/json',
      'content-type': 'application/json',
      authorization: `Bearer ${b}`,
    },
  });
};

export const getUserAccount =
  (userId: string): AppThunk =>
  (dispatch, getState) => {
    const tenantId = getState().tenant.tenantId;
    const ref = doc(db, `tenants/${tenantId}/users/${userId}`);
    let userExists = false;

    const unsubscribe = onSnapshot(ref, (res) => {
      if (res.exists()) {
        userExists = true;
        dispatch(userSlice.actions.setUserAccount({ id: res.id, ...res.data() } as UserAccount));
        dispatch(userSlice.actions.setEmailPreferenceSet(res.data().preferences.emailPreferenceSet));
        if (!isPbxEmail(res.data().email) && process.env.NODE_ENV === 'production') {
          console.log = () => {};
        }
      } else {
        userExists = false;
      }

      // Setup timeout and check mechanism
      let timeoutTimer: ReturnType<typeof setTimeout>;
      let checkTimer: ReturnType<typeof setInterval>;

      const startCheckMechanism = () => {
        checkTimer = setInterval(() => {
          console.log(userExists);
          if (userExists) {
            clearInterval(checkTimer);
            clearTimeout(timeoutTimer);
          }
        }, 100);

        timeoutTimer = setTimeout(() => {
          clearInterval(checkTimer);
          unsubscribe();
          auth.signOut();
        }, 7000);
      };

      startCheckMechanism();
    });
  };

export const getUserHash = () => async (dispatch) => {
  const b = await getUserToken();

  await api
    .get(
      `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/users/me`,

      {
        headers: {
          accept: 'application/json',
          authorization: `Bearer ${b}`,
          'content-type': 'application/json',
        },
      },
    )
    .then((res) => {
      dispatch(userSlice.actions.setIntercomHash(res?.data.identity?._intercom_hash));
    });
};
export const resetPassword = (userEmail: string) => async (_, getState) => {
  const tenantId = getState().tenant.tenantId;
  if (!tenantId) return;

  return api.post(
    `${import.meta.env.VITE_PAPERBOX_LOGIN_URL}/tenants/${tenantId}/users/${userEmail}/reset_password`,
    null,
    {
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
      },
    },
  );
};

export default userSlice;
