// Define shared types for inbox creation components

export interface InboxSettings {
  name: string;
  mailroom?: boolean;
  autoAdvance?: boolean;
  fileUpload?: boolean;
  documentDownload?: boolean;
  documentCopy?: boolean;
  documentTransform?: boolean;
  labelingMode?: boolean;
  tableColumns: {
    name: boolean;
    confidence: boolean;
    digitizedDate: boolean;
    docTypeId: boolean;
    lastUserUpdate: boolean;
    tagTypeId: boolean;
  };
  autoDelete?: {
    actionType: 'approve' | 'delete' | null | 'bounce';
    active: boolean;
    timeField: number;
    timeFormat: string;
  };
  inboxMoveWhitelist: string[];
  inboxMoveWhitelistActive: boolean;
}

export interface ConnectorSettings {
  // Brio fields
  office_id: string;
  sub_office_id: string;
  pi2_key: string;
  environment: string;
  // CCS fields
  url: string;
  username: string;
  password: string;
  account: string;
}

export interface InboxCreationState {
  templateType: string;
  connectorType: string;
  connectorId: string;
  connectorSettings: ConnectorSettings;
  inboxSettings: InboxSettings;
}

export interface TemplateInfo {
  languages: string[];
  connector: string;
  description?: string;
}

export interface SystemTemplates {
  [key: string]: {
    [key: string]: TemplateInfo;
  };
}

export type TemplateOption = {
  label: string;
  value: string;
  description: string;
  system?: string;
  type?: string;
};

// Pre-defined template options
export const TEMPLATE_OPTIONS: TemplateOption[] = [
  {
    label: 'Custom Inbox',
    value: 'custom',
    description: 'Create a custom inbox with your own settings',
  }
];
