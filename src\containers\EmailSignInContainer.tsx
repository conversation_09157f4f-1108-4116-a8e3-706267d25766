import Recaptcha from '@components/auth/helpers/Recaptcha';
import Button from '@components/shared/button/Button';
import Input from '@components/shared/input/Input';
import { ReactComponent as PaperboxLogoLight } from '@svg/paperbox-logo-light.svg';
import clsx from 'clsx';
import {
  EmailAuthProvider,
  fetchSignInMethodsForEmail,
  isSignInWithEmailLink,
  linkWithCredential,
  signInWithEmailLink,
  unlink,
} from 'firebase/auth';
import queryString from 'query-string';
import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';
import { auth } from '../shared/store/setup/firebase-setup';
import { useSelector } from '../shared/store/store';
import s from '../shared/styles/component/two-factor/two-factor.module.scss';

enum SignInState {
  init = 0,
  verified = 1,
  pwEntry = 2,
  pwConfirmed = 3,
  complete = 4,
}
interface Props {}

const EmailSignInContainer: React.FC<Props> = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const isMultiFactor = useSelector((state) => state.tenant.details.config?.multiFactor);
  const storeTenantId = useSelector((state) => state.tenant.tenantId);

  const tenantId = queryString.parse(location.search).tenantId as string;
  const [isInitialised, setIsInitialised] = useState(false);
  const [confirmationEmail, setConfirmationEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [error, setError] = useState<string>(null);

  const [signInState, setSignInState] = useState<SignInState>(SignInState.init);
  const handleSignIn = (e) => {
    e.preventDefault();
    signInWithEmailLink(auth, confirmationEmail, window.location.href).then(() => {
      setSignInState(SignInState.verified);
    });
  };

  const handleConfirmPassword = async (e) => {
    e.preventDefault();

    const signInMethods = await fetchSignInMethodsForEmail(auth, confirmationEmail);

    const credential = EmailAuthProvider.credential(confirmationEmail, password);
    if (signInMethods.length > 0) {
      await unlink(auth.currentUser, EmailAuthProvider.PROVIDER_ID);
    }
    linkWithCredential(auth.currentUser, credential)
      .then((usercred) => {
        const user = usercred.user;
        console.log('Account linking success', user);
        navigate('/');
      })
      .catch((error) => {
        if (error.code === 'auth/weak-password') {
          setError('Password too weak, must contain more than 6 characters.');
        }
        console.log('Account linking error', error);
      });
  };
  const handleContinueWithoutPassword = (e) => {
    e.preventDefault();
    if (isMultiFactor) return;
    navigate('/');
  };

  useEffect(() => {
    if (tenantId) {
      if (storeTenantId !== tenantId) {
        const currentLocation = window.location.origin;
        const tenantParts = tenantId.lastIndexOf('-');
        const tenantName = tenantId.substring(0, tenantParts);
        const redirect = currentLocation.replace(
          /(?:http:\/\/)?(?:([^.]+)\.)?paperbox\.ai/,
          `https://${tenantName}.paperbox.ai`,
        );
        window.location.replace(redirect + location.pathname + location.search);
        auth.tenantId = tenantId;
        if (isSignInWithEmailLink(auth, window.location.href)) {
          setIsInitialised(true);
        } else {
          navigate('/login');
        }
      } else {
        if (isSignInWithEmailLink(auth, window.location.href)) {
          setIsInitialised(true);
        } else {
          navigate('/login');
        }
      }
    }
  }, [tenantId, navigate, location, storeTenantId]);
  if (!isInitialised) return <div />;

  return (
    <div className={s.container}>
      <Recaptcha />
      <PaperboxLogoLight onClick={() => navigate('/inbox')} className={s.logo} />
      <div className={s.card}>
        {signInState === SignInState.init && (
          <form onSubmit={handleSignIn}>
            <h1 className={s.title}>Verify email</h1>
            <h2 className={s.sub}>Please verify your email address.</h2>
            <div className={s.input_container}>
              <Input type="email" setValue={setConfirmationEmail} value={confirmationEmail} id={'email'} />
            </div>
            <Button text="Confirm" type="submit" className={clsx(s.button)} id="email_sign_in_btn" />
          </form>
        )}
        {signInState === SignInState.verified && (
          <form onSubmit={handleConfirmPassword}>
            <h1 className={s.title}>Please enter a password</h1>
            <h2 className={s.sub}>
              {isMultiFactor
                ? ''
                : 'If you choose to skip this step you will not be able to access your account again without a new invite.'}
            </h2>
            {error && (
              <span style={{ display: 'block', textAlign: 'start', fontSize: 14 }} className={s.error}>
                {error}
              </span>
            )}
            <div className={s.input_container}>
              <Input type="password" setValue={setPassword} value={password} id={'password'} />
            </div>
            <div className={s.buttons}>
              <Button
                text="Skip"
                onClick={handleContinueWithoutPassword}
                type="button"
                disabled={isMultiFactor}
                className={clsx(s.button, s.button__alt)}
                id="email_sign_in_btn"
              />
              <Button text="Save password" type="submit" className={clsx(s.button)} id="email_sign_in_btn" />
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default EmailSignInContainer;
