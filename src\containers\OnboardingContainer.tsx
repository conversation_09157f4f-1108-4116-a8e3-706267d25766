import SuspenseLoader from '@components/shared/suspense-loader/SuspenseLoader.tsx';
import TenantOnboarding from '@components/tenant/TenantOnboarding';
import { useSelector } from '@shared/store/store.ts';
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router';

const OnboardingContainer: React.FC = () => {
  const userAccount = useSelector((state) => state.user.userAccount);
  const navigate = useNavigate();

  useEffect(() => {
    if (userAccount.id && !userAccount.isAdmin) {
      navigate('/');
    }
  }, [userAccount]);
  if (!userAccount.id) return <SuspenseLoader name={'onboarding-loader'} fullPage />;
  return <TenantOnboarding />;
};

export default OnboardingContainer;
