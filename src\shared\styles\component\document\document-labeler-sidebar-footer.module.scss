@import "../../vars/_vars";


.container {
  @include flex-center;
  flex-direction: column;
  flex-shrink: 0;
  margin: 0 20px;
  border-top: 1px solid $medium-light-gray;
}


.sidebar_title {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  justify-content: flex-start;
  width: 100%;
  padding: 12px 0px;
  text-transform: uppercase;

}


.buttons {
  display: flex;
  width: 100%;
  @include flex-center;
  min-height: 65px;
  max-height: 65px;
  padding: 0 12px;

}


.button_tags {
  width: 100%;
  height: 100%;
  max-height: 100px !important;

  & > button {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 100%;
    max-height: 100px !important;
    border-radius: 0;
  }
}


.button_main {
  @include flex-center;
  font-size: rem(14);
  font-weight: 400;
  width: 100%;
  height: 35px !important;
  border-radius: 0 !important;

  svg {
    height: 16px;

  }

  :disabled {
    transition: all 0.2s ease-in-out !important;
    border-radius: 50px;
    background: rgba($dark-gray, 0.3) !important;
  }
}


.checks {
  display: flex;
  flex-direction: column;
  width: 100%;

}


.checks_title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  text-align: left;
}


.checks_rows {
  display: flex;
  flex-direction: column;
  gap: 6px;
}


.checks_row {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 8px 12px;
  color: black;

  &__head {
    border-bottom: 1px solid #EEEEEE;
  }

  &.selectable {
    &:hover {
      cursor: pointer;
      background: $paperbox-blue--fade-extra;
    }
  }

  &__checked {
    position: relative;

    span {
      opacity: 0.5;
    }

    &::before {
      position: absolute;
      top: 50%;
      left: 5px;
      width: calc(100% - 60px);
      height: 1px;
      content: ' ';
      background: rgba(0, 0, 0, 0.5);
    }
  }

}


.checks_row_wrapper {
  display: flex;
  overflow: hidden;
  flex-direction: row;
  border-radius: 5px;

  .checks_button {
    display: flex;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    width: 44px;
    max-width: 0;
    cursor: pointer;
    transition: max-width 0.2s ease-in-out;
    color: white;
    background: $success;

    svg {
      width: 18px;
      height: auto;
      transition: transform 0.1s;
    }

    &:hover {
      svg {
        transform: scale(1.2)
      }
    }
  }

  &.interactive {
    overflow: hidden;

    &:hover {
      .checks_row {
        border-radius: 5px 0 0 5px;
      }

      .checks_button {
        max-width: 44px;

        box-shadow: $shadow-light inset;

      }
    }
  }
}


.checks_row_group {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
}


.checks_label {
  @include flex-center;
  font-size: rem(12);
  font-weight: 800;
  height: 16px;
  padding: 3px 8px 1px 8px;
  color: white;
  border-radius: 5px;
  background: $error;

  &__warning {
    background: $warning;
  }

  &__success {
    background: $success;
  }
}


.checks_info {
  width: 16px;
  height: auto;
  @include flex-center;
  margin-top: -1px;
  margin-right: auto;
  margin-left: 8px;
  color: lightgray;
}


.checks_icon {
  @include flex-center;
  width: 20px;
  height: 20px;
  color: white;

  svg {
    width: 16px;
    height: 16px;
  }
}


.button_nav {
  width: 35px;
  min-width: 35px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: 5px;
  background: $paperbox-blue;

  &:focus:not(:disabled), &:hover:not(:disabled), {
    transition: box-shadow 0.2s ease-in-out;
    outline: none;
    box-shadow: 0 2px 8px rgba($paperbox-blue, 0.4);
  }

  &:active:not(:disabled) {
    background: #30488F;
  }

  &:disabled {
    transition: all 0.2s ease-in-out;
    color: $dark-gray;
    border-radius: 50px;
    background: rgba($dark-gray, 0.3) !important;

  }

  svg {
    width: 50%;
    margin-top: 0;
    stroke-width: 3px;
    stroke: $white;
  }
}


.button__delete {
  background-color: $error;

  &:hover:not(:disabled), &:focus:not(:disabled) {
    transition: box-shadow 0.2s ease-in-out;
    outline: none;
    box-shadow: 0 2px 8px rgba($error, 0.4);
  }

  &:active:not(:disabled) {
    background: #8F3030;
  }

}


.button_main__delete {
  transition: all 0.2s ease-in-out;
  color: $white;
  border-radius: 0 !important;
  background-color: $error !important;

  svg {
    height: 90%;
    margin-top: -1;
  }
}


.button_main__validate {
  transition: all 0.2s ease-in-out;
  color: $white;
  border-radius: 0 !important;

  svg {
    height: 90%;
    margin-top: 0;
    margin-right: -2px;
    margin-left: -2px;

  }
}


.button_nav__left {
  margin-left: 1px;
  border-radius: 0 5px 5px 0 !important;

  svg {
    transform: rotateY(180deg);
  }
}


.button_nav__right {
  margin-right: 1px;
  border-radius: 5px 0 0 5px !important;

}

