import { SearchResult } from '@shared/models/document';
import { useSelector } from '@shared/store/store';
import s from '@shared/styles/component/document/sidebar/search.module.scss';
import clsx from 'clsx';
import React, { useCallback, useMemo, useRef } from 'react';
import Tooltip from '@components/shared/tooltip/Tooltip';

interface Props {
  result: SearchResult;
}

const SearchResultCardSimple: React.FC<Props> = ({ result }) => {
  const entityTypes = useSelector((state) => state.admin.inboxEntityTypes);
  const metadataTypes = useSelector((state) => state.admin.inboxMetadataTypes);
  const rowsRef = useRef();

  const fieldTypeName = useCallback(
    (field) => {
      if (field.mapping?.referenceType) {
        if (field.mapping.referenceType.includes('metadata')) {
          const mdResult = metadataTypes.find((e) => e.id === field.mapping.reference);
          if (mdResult) return mdResult.name;
        } else {
          const etResult = entityTypes.find((e) => e.id === field.mapping.reference);
          if (etResult) return etResult.name;
        }
      } else {
        if (field.mapping?.displayName) return field.mapping?.displayName;
      }
      return field.name;
    },
    [entityTypes, metadataTypes],
  );

  const sortedResults = useMemo(() => {
    const original = result.fields;
    const filtered = original.filter((e) => e.value);

    let sortedMapped = filtered.sort((a, b) => {
      return (
        Number(b.match) - Number(a.match) ||
        Number(b?.mapping?.isPinned || false) - Number(a?.mapping?.isPinned || false) ||
        fieldTypeName(a).localeCompare(fieldTypeName(b))
      );
    });
    sortedMapped = sortedMapped.filter((e) => {
      const mapped = metadataTypes.find((m) => m.id === e.mapping?.reference);
      return !mapped?.isHidden;
    });

    return [...sortedMapped].filter(
      (field) => field.mapping.displayName != null || field.mapping.reference != null,
    );
  }, [fieldTypeName, metadataTypes, result]);

  const style = useMemo(() => {
    const el = rowsRef.current as HTMLDivElement;
    if (el) {
      let totalHeight = 0;
      const childArr = Array.from(el.children);
      childArr.forEach((child) => {
        totalHeight += child.clientHeight + 2;
      });
      return { maxHeight: totalHeight };
    }
    return {};
  }, []);

  return (
    <div className={s.card} data-testid={'masterdata-result-card'}>
      {result.index?.tableName && <div className={s.header}>{result.index.tableName}</div>}
      <div ref={rowsRef} style={style} className={clsx(s.rows)}>
        {sortedResults.map((field) => {
          let valueRender;
          if (typeof field.value === 'string') {
            valueRender = (
              <span className={s.value}>
                {field.highlight !== null ? (
                  // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                  <div dangerouslySetInnerHTML={{ __html: field.highlight }} />
                ) : (
                  field.value
                )}
              </span>
            );
          } else {
            const value = field.value as string[];
            const highlight = field.highlight as string[] | null;
            const hasHighlightArray = Array.isArray(highlight) && highlight.length === value.length;

            if (value.length >= 2) {
              // Show only first item with "+X more" tooltip for arrays with 2+ items
              const remainingItems = value.slice(1);
              const remainingHighlights = hasHighlightArray ? highlight.slice(1) : null;

              const tooltipContent = (
                <div>
                  {remainingItems.map((item, index) => (
                    <div key={index}>
                      {remainingHighlights && remainingHighlights[index] ? (
                        // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                        <span dangerouslySetInnerHTML={{ __html: remainingHighlights[index] }} />
                      ) : (
                        <span>{item}</span>
                      )}
                    </div>
                  ))}
                </div>
              );

              valueRender = (
                <div className={s.value_list}>
                  <div className={s.item} key={value[0]}>
                    {hasHighlightArray && highlight[0] ? (
                      // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                      <div dangerouslySetInnerHTML={{ __html: highlight[0] }} />
                    ) : (
                      <span>{value[0]}</span>
                    )}
                  </div>
                  <Tooltip
                    content={tooltipContent}
                    position="right"
                    lightTheme={true}
                  >
                    <div className={s.item}>
                      <span>+{remainingItems.length} more</span>
                    </div>
                  </Tooltip>
                </div>
              );
            } else {
              // Show single item normally
              valueRender = (
                <div className={s.value_list}>
                  {value.map((v, index) => (
                    <div className={s.item} key={v + index}>
                      {hasHighlightArray && highlight[index] ? (
                        // biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
                        <div dangerouslySetInnerHTML={{ __html: highlight[index] }} />
                      ) : (
                        <span>{v}</span>
                      )}
                    </div>
                  ))}
                </div>
              );
            }
          }

          return (
            <div
              key={field.value + field.name}
              className={clsx(
                s.row,
                { [s.row__matched]: field.match },
                { [s.row__field]: field.mapping?.referenceType },
              )}
            >
              <div className={s.info}>
                {valueRender}
                <span className={s.type}>{fieldTypeName(field)}</span>
              </div>
              {field.mapping?.referenceType && <div className={s.tag} />}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default SearchResultCardSimple;
