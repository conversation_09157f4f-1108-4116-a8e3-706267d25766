import { writeFileSync, readFileSync, existsSync } from 'fs';
import TOML from '@iarna/toml';
import builder from 'content-security-policy-builder';
import dotenv from 'dotenv';


const mode = process.argv[2];
const branch = process.argv[3];

// First load the .env file which contains the release version
dotenv.config();

// Then load environment-specific variables
if (mode) {
  console.log("Generating netlify.toml for mode: " + mode);
  dotenv.config({ path: `.env.${mode}.local` });
} else {
  console.log("Generating netlify.toml for mode: default");
}

// Log the release version
console.log(`release:${process.env.VITE_PAPERBOX_RELEASE}`);



let frameSrc = [];
let scriptSrc = [];
let workerSrc = [];
let fontSrc = [];

if (process.env.NODE_ENV !== 'production') {
  frameSrc = ['https://paperbox-dev.firebaseapp.com/'];
  scriptSrc = ['https://www.googletagmanager.com/gtag/js'];
  fontSrc = ['https://fonts.gstatic.com'];
}
console.log('release:' + process.env.VITE_PAPERBOX_RELEASE);

const googlePolicies = {
  scriptSrc: [
    'https://www.googletagmanager.com/gtag/js',
    'https://storage.googleapis.com/workbox-cdn/releases/',
    'https://www.google.com/recaptcha/',
    'https://www.gstatic.com/recaptcha/',
    'https://apis.google.com/',
  ],
  frameSrc: ['https://www.google.com'],
  connectSrc: [
    'https://*.googleapis.com',
    'https://www.google.com',
    'https://www.gstatic.com/recaptcha/releases/',
    'https://www.google-analytics.com/g/collect',

  ],
  imgSrc: [
    'https://lh3.googleusercontent.com',
    'https://storage.googleapis.com',
    'https://www.google.com/images/cleardot.gif',
  ],
  styleSrc: ['https://fonts.googleapis.com/'],
  fontSrc: ['https://fonts.googleapis.com/', 'https://fonts.gstatic.com/'],
};
const microsoftPolicies = {
    connectSrc:[
        "https://login.microsoftonline.com/common/"
    ]
}

const intercomPolicies = {
  scriptSrc: ['https://widget.intercom.io/widget/', 'https://*.intercomcdn.com/'],
  fontSrc: ['https://*.intercomcdn.com/'],
  frameSrc: ['https://intercom-sheets.com/', 'https://www.youtube.com/'],
  connectSrc: [
    'https://api-iam.intercom.io/messenger/',
    'wss://nexus-websocket-a.intercom.io/pubsub/',
    'https://uploads.intercomcdn.com/',
  ],
  imgSrc: ['https://static.intercomassets.com/', 'https://*.intercomcdn.com/'],
  defaultSrc: ['https://js.intercomcdn.com/'],
  formAction: ['https://app.intercom.com/video_call/video'],
};

const firebasePolicies = {
  scriptSrc: ['https://*.firebasedatabase.app'],
  frameSrc: ['https://*.firebasedatabase.app', process.env.VITE_FIREBASE_AUTH_DOMAIN],
  connectSrc: ['https://*.firebasedatabase.app', 'wss://*.firebasedatabase.app'],
};

const sentryPolicies = {
  connectSrc: [
    'https://sentry-relay-esp-wjyzi23wxa-ew.a.run.app/api/',
    'https://*.ingest.sentry.io/api/',
  ],
};

const paperboxPolicies = {
  connectSrc: [
    process.env.VITE_PAPERBOX_BACKEND_URL,
    process.env.VITE_PAPERBOX_MASTERDATA_URL,
    process.env.VITE_PAPERBOX_LOGIN_URL,
    process.env.VITE_PAPERBOX_CDN_URL,
    process.env.VITE_PAPERBOX_ANALYTICS_URL,
  ],
};

const cspConfigPolicy = {
  'default-src': ["'self'", ...intercomPolicies.defaultSrc],
  'base-uri': "'self'",
  'object-src': "'none'",
  'worker-src': ["'self'", 'blob:', ...workerSrc],
  'manifest-src': "'self'",
  'script-src': [
    "'self'",
    'blob:',
    ...firebasePolicies.scriptSrc,
    ...googlePolicies.scriptSrc,
    ...intercomPolicies.scriptSrc,
    ...scriptSrc,
  ],
  'form-action': ["'self'", ...intercomPolicies.formAction],
  'frame-src': [
    "'self'",
    ...firebasePolicies.frameSrc,
    ...googlePolicies.frameSrc,
    ...intercomPolicies.frameSrc,
    ...frameSrc,
  ],
  'frame-ancestors': [
    "'self'",
    ...firebasePolicies.frameSrc,
    ...googlePolicies.frameSrc,
    ...frameSrc,
  ],
  'connect-src': [
    "'self'",
    'blob:',
    ...sentryPolicies.connectSrc,
    ...firebasePolicies.connectSrc,
    ...googlePolicies.connectSrc,
    ...intercomPolicies.connectSrc,
    ...paperboxPolicies.connectSrc,
    ...microsoftPolicies.connectSrc
  ],
  'style-src': ["'self'", "'unsafe-inline'", ...googlePolicies.styleSrc],
  'font-src': [
    "'self'",
    'data:',
    ...googlePolicies.fontSrc,
    ...intercomPolicies.fontSrc,
    ...fontSrc,
  ],
  'img-src': ["'self'", 'data:', 'blob:', ...googlePolicies.imgSrc, ...intercomPolicies.imgSrc],
};

const builtPolicy = builder({
  directives: cspConfigPolicy,
});

const config = {
  build: {
    command: 'yarn run build',
    publish: 'build',
  },
  redirects: [
    {
      from: '/*',
      to: '/',
      status: 200,
    },
  ],
  headers: [
    {
      for: '/*',
      values: {
        'Content-Security-Policy': builtPolicy + `; block-all-mixed-content;`,
        'Referrer-Policy': 'no-referrer-when-downgrade',
        'X-Frame-Options': 'DENY',
        'X-Content-Type-Options': 'nosniff',
        'Feature-Policy': "microphone 'self'; camera 'self'",
        'X-XSS-Protection': '1; mode=block',
      },
    },
  ],
  // Default context configurations
  context: {
    // Production context
    production: {
      environment: { NODE_ENV: 'production' }
    },
    // Deploy Preview context
    'deploy-preview': {
      environment: { NODE_ENV: 'production' }
    },
    // Beta branch context for split deployments
    beta: {
      environment: { NODE_ENV: 'production' }
    },
    // Release branch context
    release: {
      environment: { NODE_ENV: 'production' }
    },
    // Pre-release branch context
    'pre-release': {
      environment: { NODE_ENV: 'production' }
    }
  }
};

// Add branch deploy configuration if branch is specified
if (branch) {
  console.log(`Adding branch deploy configuration for branch: ${branch}`);
  config.context[branch] = {
    publish: 'build',
    environment: {
      BRANCH: branch,
      NODE_ENV: mode || 'production'
    }
  };
}

const tomlFile = TOML.stringify(config);

writeFileSync('netlify.toml', tomlFile);
