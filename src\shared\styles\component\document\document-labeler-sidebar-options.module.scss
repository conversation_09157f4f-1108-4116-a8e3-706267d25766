@import "../../vars/_vars";


.container {
  position: relative;
}


.dropdown {
  font-family: $base-font;
  font-weight: 400;
  position: fixed;
  z-index: 1000;

  overflow: hidden;
  width: 100px;
  pointer-events: visible;
  color: $font-color-black;
  border-radius: 5px;
  background: rgba(white, 1);
  box-shadow: 0 2px 10px rgba(0, 13, 33, 0.10);
}


.item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 6px 8px 6px 5px;
  cursor: pointer;
  white-space: nowrap;
  text-wrap: none;

  border-left: 2px solid transparent;


  &:hover {
    background: $light-gray;


    .icon__edit {
      opacity: 1;
      color: $paperbox-blue;

    }


    .icon__delete {
      opacity: 1;
      color: $error;

    }
  }
}


.text {
  font-family: $base-font;
  font-size: rem(14);
  margin-left: 3px;
  text-transform: capitalize;
}


.icon {
  width: 16px;
  height: 16px;
  color: black;


  &__delete {
    color: $error;

  }


  &__edit {
    transform: scale(0.8);
    color: $paperbox-blue;

  }
}
