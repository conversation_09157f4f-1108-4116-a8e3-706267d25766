@import "../../vars/_vars";


.container {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 400px;
  height: 100%;
  padding: 20px;
  border-radius: 5px 0 0 5px;
  background: white;
  box-shadow: rgba(0, 0, 0, 0.1) 0 4px 12px;
}


.content {
  display: flex;
  overflow: auto;
  flex-direction: column;
}


.segment {
  padding: 25px 0;
  border-top: 1px solid $medium-gray;

  .title {
    font-size: 20px;
    margin-bottom: 20px;
  }
}


.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-bottom: 15px;

  .title {
    font-size: 24px;
    font-weight: 500;
  }

  button {
    padding: 10px;
    transition: box-shadow 0.150s ease-in-out;
    border: 1px solid $medium-gray;
    border-radius: 5px;
    outline: none;
    will-change: box-shadow;

    &:hover {
      transition: all 0.150s ease-in-out;
      border: 1px solid $paperbox-blue--fade;
      box-shadow: rgba(99, 99, 99, 0.1) 0 2px 8px 0;

    }
  }
}


.tools {
  display: flex;
  flex-direction: column;

}


.tool {
  display: flex;
  align-items: center;
  height: 50px;

  & + & {
    margin-top: 40px;

  }
}


.tool_text {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  margin-left: 12px;
}


.tool_title {
  font-size: 16px;
  font-weight: bold;
}


.tool_description {
  font-size: 14px;

}


.tool_icon {
  @include flex-center;
  width: 30px;
  min-width: 30px;
  height: 30px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 7px;
  background: $dark-blue;
  box-shadow: 0 1px 3px rgba(0, 13, 33, 0.1);

  svg {
    width: auto;
    height: 45%;
    color: $white;
  }

}


.keybind {
  display: flex;
  align-items: center;

  & + & {
    margin-top: 20px;
  }
}


.keybind_name {
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  width: 50%;

}


.key {
  line-height: 1.3;
  width: auto;
  margin-right: 3px;
  padding: 3px 5px;
  vertical-align: center;
  color: black;
  border: 1px solid $medium-gray;
  border-radius: 5px;

}


.keybind_description {
  font-size: 14px;
}


.wrapper {
  height: 100%;
  margin-right: 0;
  margin-left: auto;
}


.button {
  @include flex-center;
  position: relative;
  height: 100%;
  padding-right: 24px;

  .icon {
    position: absolute;
    right: 0;
    width: 18px;
    transition: all 0.2s;
    transform: scale(1);
    opacity: 1;
    color: $dark-blue;

    &__hidden {
      transition: all 0.2s;
      transform: scale(0);
      opacity: 0;
    }
  }

  .button__active {
    color: $medium-dark-gray;

    .icon {
      color: $medium-dark-gray;
    }
  }

  &:focus {
    outline: none;
  }
}


:global {
  .help-anim-enter {
    transform: translateX(300px);
    transform-origin: right center;
    opacity: 0;
  }

  .help-anim-enter-active {
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
    transform: translateX(0);
    transform-origin: right center;
    opacity: 1;
  }

  .help-anim-exit {
    transition: opacity 0.25s ease-in-out, transform 0.25s ease-in-out;
    transform: translateX(0);
    transform-origin: right center;
    opacity: 1;
  }

  .help-anim-exit-active {
    transform: translateX(400px);
    transform-origin: right center;
    opacity: 0.3;
  }
}
