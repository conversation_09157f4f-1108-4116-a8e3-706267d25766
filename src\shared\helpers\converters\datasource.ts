/** Operation states for datasource operations */
export enum OperationState {
  CREATED = 'created',
  PLANNING = 'planning',
  WAITING = 'waiting',
  RUNNING = 'running',
  FINISHED = 'finished',
  FAILED = 'failed',
  ARCHIVED = 'archived',
}

/** Types of datasource operations */
export enum OperationType {
  UPDATE = 'update',
  UPDATE_INVALIDATE = 'update_invalidate',
  REINDEX = 'reindex',
}

/** Property mapping types for field definitions */
export enum MappingType {
  TEXT = 'text',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  DATE = 'date',
  OBJECT = 'object',
  ID = 'id',
  NESTED = 'nested',
}

/** Evaluation engines for derived fields */
export enum EvaluationEngine {
  JEXL = 'jexl',
  JINJA = 'jinja',
}

export interface Property {
  type: MappingType;
  is_searchable?: boolean;
  chars_to_filter?: string[] | null;
}

/** Recursive mapping structure for field definitions */
export interface Mapping {
  [key: string]: Property | Mapping;
}

/** Recursive derived mapping structure */
export interface IDerivedMapping {
  [key: string]: IDerivedProperty | IDerivedMapping;
} /** Individual derived property definition */

export interface IDerivedProperty {
  type: MappingType;
  evaluation: string;
  evaluation_engine: EvaluationEngine;
  is_searchable?: boolean;
  chars_to_filter?: string[] | null;
}

/** Internal source configuration */
export interface InternalSourceConfig {
  index_name: string[];
}

/** Source types for datasources */
export enum SourceType {
  INTERNAL = 'internal',
}

/** Source model for datasource data sources */
export interface SourceModel {
  type: SourceType;
  config: InternalSourceConfig;
}

export interface IRawDatasource {
  id?: string;
  name: string;
  record_id_field: string;
  mappings: Mapping;
  derived_mappings: IDerivedMapping;
  source: SourceModel;
  frequency_s?: number | null; // Update frequency in seconds (max 30 days)
  operation_retention?: number | null;
  examples?: Record<string, any>[] | null;
}

export interface IClientDatasource {
  id?: string;
  name: string;
  recordIdField: string;
  mappings: Mapping;
  derivedMappings: IDerivedMapping;
  source: SourceModel;
  frequencyS?: number | null;
  operationRetention?: number | null;
  examples?: Record<string, any>[] | null;
}

export const datasourceRawToClient = (raw: IRawDatasource): IClientDatasource => {
  return {
    id: raw.id,
    name: raw.name,
    recordIdField: raw.record_id_field,
    mappings: raw.mappings,
    derivedMappings: raw.derived_mappings,
    source: raw.source,
    frequencyS: raw.frequency_s,
    operationRetention: raw.operation_retention,
    examples: raw.examples,
  };
};

export const datasourceClientToRaw = (client: IClientDatasource): IRawDatasource => {
  return {
    id: client.id,
    name: client.name,
    record_id_field: client.recordIdField,
    mappings: client.mappings,
    derived_mappings: client.derivedMappings,
    source: client.source,
    frequency_s: client.frequencyS,
    operation_retention: client.operationRetention,
    examples: client.examples,
  };
};
