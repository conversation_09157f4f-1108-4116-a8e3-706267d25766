import TenantOnboardingFinish from '@components/tenant/TenantOnboardingFinish';
import TenantOnboardingStep1 from '@components/tenant/TenantOnboardingStep1';
import TenantOnboardingStep2 from '@components/tenant/TenantOnboardingStep2';
import TenantOnboardingStep3 from '@components/tenant/TenantOnboardingStep3';
import apiClient from '@shared/helpers/apiClient';
import { isPbxEmail } from '@shared/helpers/helpers.ts';
import { dashboardSlice } from '@shared/store/dashboardSlice';
import { useDispatch, useSelector } from '@shared/store/store';
import s from '@shared/styles/component/auth/auth.module.scss';
import { postInbox } from '@src/shared/store/adminSlice';
import { DotPulse } from '@uiball/loaders';
import clsx from 'clsx';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';
import { CSSTransition, SwitchTransition } from 'react-transition-group';
import {
  OnboardingState,
  SystemOptions,
  SystemTemplates,
  createConnectorPayload,
  createEndpointPayload,
  createTemplateType,
} from './onboarding-types';

// UI Components
const LoadingOverlay: React.FC<{
  progress: { current: number; total: number };
}> = ({ progress }) => {
  const { t } = useTranslation();
  return (
    <div className={s.loading_overlay} data-testid="loading-overlay">
      <DotPulse size={60} color="#0085FF" />
      <div className={s.loading_message}>
        {progress.total > 1 ? (
          <>
            <div>{t('home:onboarding.processing')}</div>
            <div className={s.loading_progress}>
              {t('home:onboarding.progressCount', { current: progress.current, total: progress.total })}
            </div>
          </>
        ) : (
          t('home:onboarding.creatingInbox')
        )}
      </div>
    </div>
  );
};

const StepIndicator: React.FC<{
  stepNumber: number;
  isActive: boolean;
  isCompleted: boolean;
  onClick: () => void;
}> = ({ stepNumber, isActive, isCompleted, onClick }) => (
  <div
    key={stepNumber}
    className={clsx(s.step_indicator, {
      [s.step_indicator__active]: isActive,
      [s.step_indicator__completed]: isCompleted,
    })}
    onClick={onClick}
    data-testid={`step-indicator-${stepNumber}`}
  >
    {stepNumber}
  </div>
);

const TenantOnboarding: React.FC = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [state, setState] = useState<OnboardingState>({});
  const [step, setStep] = useState(1);
  const [isStep2Valid, setIsStep2Valid] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [creationProgress, setCreationProgress] = useState({ current: 0, total: 0 });
  const [templates, setTemplates] = useState<SystemTemplates>();
  const [isTemplatesLoading, setIsTemplatesLoading] = useState(true);
  const nodeRef = useRef(null);
  const user = useSelector((state) => state.user.userAccount);

  // Calculate step titles based on selected system
  // const stepTitles = useMemo(() => {
  //   if (state.system === 'other') return [t('home:onboarding.title1'), t('home:onboarding.titleInboxes')];
  //   return [t('home:onboarding.title1'), t('home:onboarding.title2'), t('home:onboarding.titleInboxes')];
  // }, [state.system, t]);

  // Determine total steps based on selected system
  const totalSteps = useMemo(() => (state.system === 'other' ? 2 : 3), [state.system]);

  // Load inbox templates from the backend
  useEffect(() => {
    const fetchTemplates = async () => {
      setIsTemplatesLoading(true);
      try {
        const response = await apiClient.get(
          `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/templates/inboxes`,
        );
        setTemplates(response.data);
      } catch (error) {
        console.error('Failed to fetch templates:', error);
      } finally {
        setIsTemplatesLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  const handleSystemSelect = useCallback((system: string) => {
    setState({
      system,
      inboxes: [],
    });
  }, []);

  const createInbox = useCallback(
    async (
      inboxName: string,
      templateType: string,
      connectorPayload: Record<string, any>,
      connectorKey = '_CONNECTOR_ID_',
      endpointPayload?: Record<string, any>,
      endpointKey = '_ENDPOINT_ID_',
    ) => {
      let payload: any = {
        settings: {
          settings: { name: inboxName },
        },
      };

      if (connectorPayload) {
        payload = {
          ...payload,
          connectors: {
            [connectorKey]: connectorPayload,
          },
        };
      }
      if (endpointPayload) {
        payload = {
          ...payload,
          endpoints: {
            [endpointKey]: endpointPayload,
          },
        };
      }
      if (connectorPayload || endpointPayload) {
        return apiClient.post(
          `${import.meta.env.VITE_PAPERBOX_BACKEND_URL}/inboxes/import?inbox_template_type=${templateType}`,
          payload,
        );
      }

      return postInbox(payload.settings);
    },
    [],
  );

  const handleSubmit = useCallback(async () => {
    if (!state.inboxes || state.inboxes.length === 0) return;

    setIsSubmitting(true);
    setCreationProgress({ current: 0, total: state.inboxes.length });

    try {
      const connectorType = state.system;
      let connectorPayload = null;
      let endpointPayload = null;
      if (connectorType !== 'other') {
        connectorPayload = createConnectorPayload(connectorType, state.additionalInfo);
        endpointPayload = createEndpointPayload(state.additionalInfo);
      }

      console.log(connectorType);

      const firstInbox = state.inboxes[0];
      const templateType = createTemplateType(connectorType, firstInbox.type, firstInbox.language);

      const firstResponse = await createInbox(
        firstInbox.name,
        templateType,
        connectorPayload,
        undefined,
        endpointPayload,
        undefined,
      );
      setCreationProgress((prev) => ({ ...prev, current: 1 }));

      let connectorId;

      if (connectorType !== 'other') {
        connectorId = firstResponse.data.connectors[0];
      }

      const remainingInboxes = state.inboxes.slice(1);

      for (let i = 0; i < remainingInboxes.length; i++) {
        const inbox = remainingInboxes[i];
        const inboxTemplateType = createTemplateType(connectorType, inbox.type, inbox.language);
        await createInbox(inbox.name, inboxTemplateType, connectorPayload, connectorId);
        setCreationProgress((prev) => ({ ...prev, current: prev.current + 1 }));
      }

      setTimeout(() => {
        setIsSubmitting(false);
        setStep(4); // Move to finish screen
      }, 500);
    } catch (error) {
      console.error('Failed to create inbox(es):', error);
      setIsSubmitting(false);
    }
  }, [state, dispatch, createInbox]);

  const handleNextStep = useCallback(() => {
    if (step === 1 && state.system === 'other') {
      setStep(3);
    } else if (step === 3) {
      handleSubmit();
    } else if (step === 4) {
      dispatch(dashboardSlice.actions.setShowTenantOnboarding(false));
      navigate('/inbox');
    } else {
      setStep(step + 1);
    }
  }, [step, state.system, handleSubmit, dispatch, navigate]);

  const handlePrevStep = useCallback(() => {
    if (state.system === 'other' && step === 3) {
      setStep(1);
    } else if (step === 4) {
      // Don't allow going back from finish screen
      return;
    } else {
      setStep(step - 1);
    }
  }, [step, state.system]);

  const isStepActive = useCallback(
    (stepNumber: number) => {
      return state.system === 'other' && step === 3 ? stepNumber === 2 : step === stepNumber;
    },
    [step, state.system],
  );

  const isStepCompleted = useCallback(
    (stepNumber: number) => {
      if (state.system === 'other') {
        return step > stepNumber || (step === 3 && stepNumber === 2);
      }
      return step > stepNumber;
    },
    [step, state.system],
  );

  const handleStepClick = useCallback(
    (stepNumber: number) => {
      if (stepNumber < step || (state.system === 'other' && step === 3 && stepNumber === 2)) {
        const targetStep = state.system === 'other' && stepNumber === 2 ? 3 : stepNumber;
        setStep(targetStep);
      }
    },
    [step, state.system],
  );

  const systemOptions: SystemOptions = useMemo(() => {
    let list = [
      {
        label: 'CCS',
        value: 'ccs',
        description: t('admin:inboxes.templates.ccsDescription'),
      },
      {
        label: 'Portima Brio',
        value: 'brio',
        description: t('admin:inboxes.templates.brioDescription'),
      },
    ];
    // Filter list according to available templates
    if (templates) {
      list = list.filter((option) => Object.keys(templates).includes(option.value));
    }
    list.push({
      label: t('admin:inboxes.templates.custom'),
      value: 'other',
      description: t('admin:inboxes.templates.customDescription'),
    });

    return list;
  }, [templates, t]);

  const isNextButtonDisabled = useMemo(() => {
    if (isSubmitting) return true;
    if (step === 1) return !state.system;
    if (step === 2) return !isStep2Valid;
    if (step === 3) return !state.inboxes || state.inboxes.length === 0;
    if (step === 4) return false; // Finish screen button is always enabled
    return false;
  }, [step, state.system, state.inboxes, isStep2Valid, isSubmitting]);

  return (
    <div className={s.container} data-testid="tenant-onboarding">
      <div className={clsx(s.card, s.card__wide)}>
        {isSubmitting && <LoadingOverlay progress={creationProgress} />}
        {isPbxEmail(user.email) && (
          <button
            onClick={() => {
              dispatch(dashboardSlice.actions.setShowTenantOnboarding(false));
              dispatch(dashboardSlice.actions.setOnboardingDismissedThisSession(true));
              navigate('/inbox');
            }}
            className={s.close_onboarding}
            aria-label="Close onboarding"
            data-testid="close-onboarding"
          >
            ✕
          </button>
        )}

        {step !== 4 && (
          <div className={s.step_indicators}>
            {Array.from({ length: totalSteps }, (_, i) => i + 1).map((stepNumber) => (
              <StepIndicator
                key={stepNumber}
                stepNumber={stepNumber}
                isActive={isStepActive(stepNumber)}
                isCompleted={isStepCompleted(stepNumber)}
                onClick={() => handleStepClick(stepNumber)}
              />
            ))}
            <div className={s.step_connector}>
              <div
                className={s.step_connector_progress}
                style={{
                  width:
                    state.system === 'other' && step === 3
                      ? '100%'
                      : `${(step - 1) * (100 / (totalSteps - 1))}%`,
                }}
              />
            </div>
          </div>
        )}

        <div className={s.steps_container}>
          <SwitchTransition mode="out-in">
            <CSSTransition key={step} nodeRef={nodeRef} timeout={300} classNames="fade" unmountOnExit>
              <div ref={nodeRef}>
                {step === 1 && (
                  <TenantOnboardingStep1
                    systemOptions={systemOptions}
                    setActiveSystem={handleSystemSelect}
                    activeSystem={state.system}
                    isLoading={isTemplatesLoading}
                  />
                )}
                {step === 2 && state.system !== 'other' && (
                  <TenantOnboardingStep2 state={state} setState={setState} setFormValid={setIsStep2Valid} />
                )}
                {step === 3 && (
                  <TenantOnboardingStep3 state={state} setState={setState} templates={templates} />
                )}
                {step === 4 && <TenantOnboardingFinish createdInboxCount={state.inboxes?.length || 0} />}
              </div>
            </CSSTransition>
          </SwitchTransition>
        </div>

        <div className={clsx(s.onboarding_buttons, { [s.onboarding_buttons__centered]: step === 4 })}>
          {step !== 4 && (
            <button
              disabled={isSubmitting}
              onClick={handlePrevStep}
              className={clsx(s.nav_button, s.nav_button__prev)}
              data-testid="prev-button"
            >
              {step !== 1 && (
                <>
                  <span className={s.nav_button_icon}>←</span>
                  <span>{t('home:onboarding.prevStep')}</span>
                </>
              )}
            </button>
          )}

          <button
            disabled={isNextButtonDisabled}
            onClick={handleNextStep}
            className={clsx(s.nav_button, s.nav_button__next, { [s.nav_button__finish]: step === 4 })}
            data-testid="next-button"
          >
            <span>
              {step === 4
                ? t('home:onboarding.closeOnboarding')
                : step === 3
                  ? isSubmitting
                    ? t('home:onboarding.processing')
                    : t('home:onboarding.finish')
                  : t('home:onboarding.nextStep')}
            </span>
            {step !== 3 && step !== 4 && <span className={s.nav_button_icon}>→</span>}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TenantOnboarding;
