@import "./src/shared/styles/vars/_vars";


.modal {
  display: flex;
  flex-direction: column;
  width: 100vw;
  min-width: 400px;
  margin: 0 auto 15vh;
  color: $font-color-black;
  border-radius: 5px;
  background-color: $white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);

}


.title {
  font-size: 18px;
  font-weight: 500;
}


.header {
  display: flex;
  justify-content: space-between;
  height: 55px;
  padding: 20px;
  border-bottom: 1px solid $medium-gray;
}


.content {
  padding: 15px 25px 25px 25px;

}


.description {
  font-size: 14px;
  line-height: 1.35;
  color: #95A4C2;
}


.title {
  font-size: rem(18);
  font-weight: 400;
  line-height: 1.2;
}


.close {
  cursor: pointer;
  color: $font-color-black;

  &:hover {
    color: $error;
  }

}


.center {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
  gap: 20px;
}
