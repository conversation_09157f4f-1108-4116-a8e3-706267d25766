@import "@shared/styles/vars/_vars";


.container {
  @include flex-center;
  font-family: $base-font;
  position: fixed;
  z-index: 9998;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  animation: fadein 0.4s;
  background: rgba(0, 0, 0, 0.4);
  will-change: opacity;

}


.content {
  position: relative;
  animation: grow 0.4s cubic-bezier(0.25, 0.8, 0.5, 1);
  opacity: 1;
  will-change: opacity;

}


@keyframes fadein {
  from {
    background: rgba(0, 0, 0, 0);
  }
  to {
    background: rgba(0, 0, 0, 0.4);
  }
}


@keyframes grow {
  0% {
    transform: scale(0.7) translateY(30%);
    opacity: 0;
  }
  99% {
    transform: scale(1) translateX(0);
    opacity: 1;
  }
  100% {
    transform: unset;
    opacity: 1;
  }
}


:global {

  .modal-enter {
    opacity: 0;

    #modal-content:first-child {
      transform: translateY(100px);
      opacity: 0;

    }
  }

  .modal-enter-active {
    transition: opacity 200ms;
    opacity: 1;

    #modal-content:first-child {
      transition: all 300ms;
      transform: translateY(0);
      opacity: 1;
    }
  }

  .modal-exit {
    opacity: 1;

    #modal-content:first-child {
      transform: translateY(0);
    }
  }

  .modal-exit-active {
    transition: opacity 200ms ease;

    opacity: 0;

    #modal-content:first-child {
      transition: all 300ms;
      transform: translateY(100px);
      opacity: 0;

    }
  }
}
