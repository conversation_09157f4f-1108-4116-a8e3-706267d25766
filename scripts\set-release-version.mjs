// This script sets the VITE_PAPERBOX_RELEASE environment variable
import { execSync } from 'child_process';
import * as fs from 'fs';

try {
  let version;

  // Check if we're running in Netlify
  if (process.env.NETLIFY === 'true') {
    console.log('Running in Netlify environment');

    // Use Netlify environment variables
    const branch = process.env.BRANCH;
    const commitRef = process.env.COMMIT_REF;
    const context = process.env.CONTEXT;
    const deployPrimeUrl = process.env.DEPLOY_PRIME_URL || '';

    console.log(`Branch.: ${branch}`);
    console.log(`Commit: ${commitRef}`);
    console.log(`Context: ${context}`);

    // Try to get the Git tag in Netlify environment
    console.log('Attempting to fetch Git tag in Netlify environment...');

    try {
      // Fetch tags (Netlify might not have them by default)
      console.log('Fetching Git tags...');
      execSync('git fetch --tags', { stdio: 'inherit' });

      // Try to get tag directly on this commit
      console.log('Checking for tag on current commit...');
      version = execSync('git describe --tags --exact-match 2> /dev/null').toString().trim();
      console.log(`Found exact tag on current commit: ${version}`);
    } catch (e) {
      console.log('No exact tag found on current commit. Trying alternative approaches...');

      try {
        // Try to get the most recent tag
        console.log('Looking for most recent tag...');
        version = execSync('git describe --tags --abbrev=0').toString().trim();
        console.log(`Found most recent tag: ${version}`);
      } catch (e) {
        console.log('No tags found. Falling back to branch-based naming...');

        // Extract PR number if this is a deploy preview
        let prNumber = '';
        if (context === 'deploy-preview') {
          const prMatch = deployPrimeUrl.match(/deploy-preview-(\d+)/);
          prNumber = prMatch ? prMatch[1] : '';
        }

        // Determine version based on branch and context
        if (branch === 'release') {
          version = `release-${commitRef.substring(0, 7)}`;
        } else if (branch === 'pre-release') {
          version = `pre-release-${commitRef.substring(0, 7)}`;
        } else if (branch === 'beta') {
          version = `beta-${commitRef.substring(0, 7)}`;
        } else if (context === 'deploy-preview' && prNumber) {
          version = `pr-${prNumber}`;
        } else {
          version = `${branch}-${commitRef.substring(0, 7)}`;
        }

        console.log(`Using branch-based version: ${version}`);
      }
    }
  } else {
    console.log('Running in local environment');

    // When running locally, try to use git commands
    try {
      // First, try to get the tag directly from the current commit
      version = execSync('git describe --tags --exact-match 2> /dev/null').toString().trim();
      console.log(`Found exact tag: ${version}`);
    } catch (e) {
      try {
        // If there's no tag on the current commit, get the branch name
        const branch = execSync('git rev-parse --abbrev-ref HEAD').toString().trim();
        const commitSha = execSync('git rev-parse --short HEAD').toString().trim();

        console.log(`On branch: ${branch}`);

        if (branch === 'pre-release' || branch === 'release') {
          // For release branches, try to get the most recent tag
          try {
            version = execSync('git describe --tags --abbrev=0').toString().trim();
            console.log(`Using most recent tag: ${version}`);
          } catch (e) {
            // If no tags are available
            version = `${branch}-${commitSha}`;
          }
        } else if (branch === 'beta') {
          // For beta branch
          version = `beta-${new Date().toISOString().slice(0, 10)}`;
        } else {
          // For other branches
          version = `${branch}-${commitSha}`;
        }
      } catch (e) {
        // If all else fails, use a timestamp
        version = `build-${new Date().toISOString().replace(/[:.]/g, '-')}`;
        console.log('Could not determine git info, using timestamp');
      }
    }
  }

  console.log(`Setting VITE_PAPERBOX_RELEASE to: ${version}`);

  // Create or update the .env file with the version
  const envPath = '.env';
  let envContent = '';

  // Read existing .env file if it exists
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');

    // Remove any existing VITE_PAPERBOX_RELEASE line
    envContent = envContent.replace(/^VITE_PAPERBOX_RELEASE=.*$/m, '');

    // Ensure there's a newline at the end
    if (!envContent.endsWith('\n')) {
      envContent += '\n';
    }
  }

  // Add the new VITE_PAPERBOX_RELEASE line
  envContent += `VITE_PAPERBOX_RELEASE=${version}\n`;

  // Write the updated .env file
  fs.writeFileSync(envPath, envContent);

  // Also set it as an environment variable for the current process
  process.env.VITE_PAPERBOX_RELEASE = version;

  console.log('Successfully set VITE_PAPERBOX_RELEASE in .env file');
} catch (error) {
  console.error('Error setting release version:', error);
  console.error(error.stack);
  // Don't exit with error, just continue with build
  console.log('Continuing build process...');
}
