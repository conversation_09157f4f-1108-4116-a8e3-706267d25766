@import "../../vars/_vars";


.bar {
  z-index: 9;
  display: flex;
  align-items: center;
  align-self: stretch;
  flex-shrink: 0;
  height: 50px;
  min-height: 50px;
  padding: 0 15px 0 25px;
  transition: opacity 0.4s ease-in-out;
  white-space: nowrap;
  color: $dark-blue;
  background: #FFF;
  box-shadow: 0 2px 10px rgba(0, 13, 33, .1);

  //span {
  //  font-size: 12px;
  //  font-weight: 800;
  //  margin-top: 2px;
  //  letter-spacing: 1px;
  //  text-transform: uppercase;
  //
  //}
}


.tool {
  font-size: 16px;
  display: flex;
  align-items: center;
  flex-grow: 1;
  justify-content: flex-start;
  height: 50px;
  border-left: 1px solid $medium-gray;


  :global(.rs-picker-default .rs-picker-toggle) {
    height:35px !important;
    border: 1px solid $medium-gray;
    border-radius: 8px;
  }
}


.label{
  font-size: 12px;
  font-weight: 800;
  letter-spacing: 1px;
  text-transform: uppercase;
}
.text {
  font-size: 12px;
  font-weight: 800;
  margin: 0 15px 0 25px;
  letter-spacing: 1px;
  text-transform: uppercase;
}


.validation_icon {
  position: absolute;
  top: 8px;
  left: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: white;
  border-radius: 10px;
  background: $error;

  svg {
    width: auto;

    height: 12px;
    color: white;
    stroke-width: 1;
  }

  @include flex-center();

  &__valid {
    background: $success;
  }
}


.selector {
  @include flex-center;
  font-size: 16px;
  position: relative;
  width: 145px;
  height: 35px;
  padding: 0;
  cursor: pointer;
  transition: box-shadow 0.1s ease-in-out, width .2s ease-in-out;
  text-wrap: none;
  text-overflow: ellipsis;
  border: 1px solid $medium-gray;
  border-radius: 7px;
  will-change: box-shadow;

  .pending {
    display: none;
  }

  .pending_icon {
    color: $medium-gray;
  }

  &__active, &:hover {
    //box-shadow: 0 0 1px 2px $paperbox-blue--fade;
    transition: box-shadow .1s ease-in-out, width 0.2s ease-in-out;
  }

  &__active {
    .pending_icon {
      display: none;
    }

    .pending {
      font-size: 14px;
      display: block;
      text-align: center;
      color: $dark-gray;

    }
  }

  &__selected {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 7px;

    .pending_icon {
      display: none;
    }

    //span {
    //  font-size: 16px;
    //  display: block;
    //  overflow: hidden;
    //  white-space: nowrap;
    //  text-overflow: ellipsis;
    //
    //}
  }

  .input {
    font-family: $base-font;
    font-size: 14px;
    width: 100%;
    height: 100%;
    padding: 6px 5px 5px 5px;
    transition: padding 0.4s ease-in-out;
    text-align: center;
    border: none;
    border-radius: 5px;
    outline: none;

    &::placeholder {
      color: $dark-gray;

    }

    &__invalid {
      box-shadow: 0 0 1px 2px rgba($error, 0.8);

    }

    &__valid {
      box-shadow: 0 0 1px 2px rgba($success, 1);

    }

    &:focus:not(.input__invalid):not(.input__valid) {
      box-shadow: 0 0 1px 2px $paperbox-blue--fade;
    }
  }
}



.confirm {
  @include flex-center;
  width: 34px;
  height: 34px;
  margin-left: 25px;
  transition: background-color 0.2s ease-in-out;
  color: grey;
  border: 1px solid $medium-gray;
  border-radius: 5px;
  background: white;
  will-change: background-color;

  svg {
    height: 20px;
  }

  &:focus {
    outline: none;
  }

  &__active:not(:disabled) {
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
    color: white;
    border-color: transparent;
    background: $paperbox-blue;
  }
}


.tool_wrapper {
  & + & {
    margin-left: 10px;
  }
}


.tools {
  display: flex;
  align-items: center;
  margin-right: 30px;
  margin-left: 10px;


  .tool_selector {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 32px;
    color: rgba($dark-blue, 0.3);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 7px;
    background: $white;
    will-change: opacity;

    svg {
      width: auto;
      height: 55%;
    }

    &:hover, &__active {
      cursor: pointer;
      opacity: 1;

      svg {
        color: black;
      }
    }
  }

}


.image_hover {
  position: absolute;
  z-index: 1000;
  top: 0;
  right: 0;
  width: auto;
  max-width: 350px;
  max-height: 250px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 13, 33, 0.3);

  &_wrapper {
    position: relative;
    z-index: 1000;
    top: 30px;
    left: 100%;
    max-width: 0;
    max-height: 0;
  }
}


.image {
  overflow: hidden;
  max-width: 100%;
  height: 25px;
  max-height: 27px;

  &_wrapper {
    display: flex;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    max-width: 100%;
    height: 27px;
    background: white;
    object-fit: scale-down;
    object-position: center;

    img {
      max-width: 100%;
    }
  }
}


.undo {
  margin-right: 0;
  margin-left: auto;

  button {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 32px;
    padding: 8px 16px;
    color: rgba($dark-blue, 1);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 7px;
    background: $white;
    gap: 4px;
    will-change: opacity;

    svg {
      width: 18px;
      height: 18px;
      transform: translateY(-1px);
    }
  }
}



