@import "../../vars/_vars";


.container {
  position: relative;
  width: 75%;
}


.icon {
  position: absolute;
  top: 6px;
  right: 8px;
  width: 20px;
  height: auto;
}


.input_wrapper {
  width: 100%;
  height: 32px;
  cursor: pointer;
  transition: 0.2s box-shadow ease-in-out;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: #FAFAFA;

  &__focussed:not(:disabled) {
    transition: 0.2s box-shadow ease-in-out;
    outline: none;
    box-shadow: 0 0 1px 2px $paperbox-blue--fade;
  }

  &:hover:not(:disabled):not(&__focussed) {
    transition: 0.2s box-shadow ease-in-out;
    outline: none;
    box-shadow: 0 0 1px 2px $paperbox-blue--fade-extra;
  }

  &:hover:not(.disabled) .arrow:not(.disabled) {
    svg path {
      fill: $paperbox-blue;
    }
  }
}


.input {
  width: 100%;
  height: 100%;
  padding: 6px 15px;
  border: none;
  border-radius: 5px;
  background: none;

  &:focus {
    outline: none;
  }
}


.special {
  font-size: 14px;
  position: absolute;
  top: 5px;
  left: 5px;
  display: flex;
  justify-content: space-between;

  padding: 3px 16px;

  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: white;

  &:hover {

    svg {
      color: $error;
    }
  }

  svg {
    width: auto;
    height: 14px;
    margin-right: -6px;
    margin-left: 8px;
    color: #969FAD;
  }
}


.bottom {
  position: fixed;
  z-index: 100;
  top: -10px;
  overflow: auto;
  width: 100%;
  padding: 10px;
  user-select: none;
  transition: opacity 0.2s ease-in-out;
  transition-property: opacity, max-height, transform;
  pointer-events: none;
  opacity: 0;
  border-radius: 5px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  user-focus: none;

  &__active {
    height: auto;

    max-height: 280px;
    transform: translateY(10px);
    pointer-events: auto;
    opacity: 1;
    user-focus: unset;
  }
}


.bottom_header {
  font-weight: 500;
  display: block;
  margin-bottom: 10px;
  padding: 10px;
  border-bottom: 1px solid #EEEEEE;
}


.bottom_row {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 10px 10px;
  border-radius: 5px;

  & + & {
    border-top: 1px solid #EEEEEE;
  }

  &:focus {
    outline: none;
  }

  &:hover:not(.option__placeholder), &:focus {
    cursor: pointer;
    color: $paperbox-blue;
    outline: none;
    background-color: #E9EFFF;
  }
}


.bottom_row_name {
  font-weight: bold;
}


.bottom_row_description {
  margin-top: 12px;
  color: #898B99;
}
