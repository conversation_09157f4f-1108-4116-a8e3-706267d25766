{"name": "paperbox-frontend", "version": "1.0.1", "private": true, "type": "module", "dependencies": {"@azure/msal-browser": "^3.2.0", "@azure/msal-react": "^2.0.4", "@dnd-kit/core": "^6.0.5", "@dnd-kit/sortable": "^7.0.1", "@floating-ui/react-dom": "^1.0.1", "@reactour/tour": "^3.5.0", "@reduxjs/toolkit": "^1.9.0", "@sentry/react": "^7.64.0", "@sentry/replay": "^7.64.0", "@sentry/tracing": "^7.64.0", "@sentry/vite-plugin": "^2.7.0", "@uiball/loaders": "^1.2.1", "axios": "^1.7.9", "axios-retry": "^4.0.0", "camelcase-keys": "8.0.2", "chrono-node": "^2.7.8", "clsx": "^2.0.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "firebase": "^11.3.1", "focus-trap-react": "^10.2.1", "fuse.js": "^6.4.1", "history": "^5.3.0", "i18next": "^23.4.4", "i18next-browser-languagedetector": "^7.1.0", "lodash": "^4.17.21", "nyc": "^15.1.0", "playwright": "^1.42.1", "playwright-test-coverage": "^1.2.12", "query-string": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-firebase-hooks": "^5.1.1", "react-i18next": "^13.1.2", "react-loading-skeleton": "^3.0.1", "react-phone-input-2": "^2.15.1", "react-redux": "^8.1.2", "react-resize-detector": "^9.1.0", "react-router": "^7.1.4", "react-select": "^5.7.4", "react-slider": "^2.0.6", "react-table": "^7.3.2", "react-topbar-progress-indicator": "^4.1.0", "react-transition-group": "^4.4.5", "react-use-intercom": "^5.1.4", "rsuite": "^5.41.0", "snakecase-keys": "^5.4.6", "use-async-effect": "^2.2.3", "use-deep-compare": "^1.1.0", "uuid": "^9.0.0", "vite-plugin-pwa": "^0.21.1", "vite-plugin-svgr": "^3.2.0", "workbox-core": "^7.3.0", "workbox-precaching": "^7.3.0"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@iarna/toml": "^2.2.5", "@playwright/test": "^1.42.1", "@simbathesailor/use-what-changed": "^2.0.0", "@types/lodash": "^4.14.197", "@types/node": "^20.11.30", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-redux": "^7.1.7", "@types/react-router-dom": "^5.1.5", "@types/react-slider": "^1.3.1", "@types/react-table": "^7.0.29", "@types/react-transition-group": "4.4.6", "@types/uuid": "^9.0.2", "@vitejs/plugin-react-swc": "^3.4.0", "content-security-policy-builder": "^2.1.0", "cross-env": "^7.0.2", "customize-cra": "^1.0.0", "dotenv": "^16.3.1", "env-cmd": "^10.1.0", "fs-extra": "^11.1.1", "html-webpack-plugin": "^5.5.3", "license-checker": "^25.0.1", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "preload-webpack-plugin": "^3.0.0-beta.4", "prettier": "3.0.3", "prop-types": "^15.8.1", "sass": "^1.68.0", "typescript": "^5.5.4", "vite": "5.4.14", "vite-plugin-istanbul": "^6.0.2"}, "scripts": {"e2e": "npx playwright test", "start-tst": "cross-env VITE_TEST=true nyc npm run start", "start-tst-ci": "cross-env VITE_TEST=true vite", "start": "env-cmd -f .env.tst.local vite", "set-version": "node scripts/set-release-version.mjs", "build": "cross-env NODE_ENV=production NODE_OPTIONS=\"--max-old-space-size=8192\" npm run set-version && npm run create-netlify-toml && license-checker --failOn AGPL --summary && tsc && vite build", "preview": "vite preview", "postinstall": "patch-package", "build-and-deploy-tst": "env-cmd -f .env.tst.local npm run build && npm run create-netlify-toml tst && npm run deploy-tst", "build-and-deploy-acc": "env-cmd -f .env.acc.local npm run build && npm run create-netlify-toml acc && npm run deploy-acc", "build-and-deploy-prd": "env-cmd -f .env.prd.local npm run build && npm run create-netlify-toml prd && npm run deploy-prd", "create-netlify-toml": "node scripts/netlify-toml-builder.mjs", "deploy-tst": "netlify deploy --prod --site paperbox-tst", "deploy-acc": "netlify deploy --prod --site paperbox-acc", "deploy-prd": "netlify deploy --prod --site paperbox-prd", "create-netlify-toml-branch": "node scripts/netlify-toml-builder.mjs production"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}