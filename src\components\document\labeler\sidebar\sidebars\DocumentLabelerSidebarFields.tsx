import s from '@components/document/labeler/sidebar/sidebar.module.scss';
import DocumentLabelerSidebarField from '@components/document/labeler/sidebar/sidebars/items/DocumentLabelerSidebarField.tsx';
import { IDocumentEnrichedEntity } from '@shared/helpers/converters/document.ts';
import { uuid4hex } from '@shared/helpers/helpers.ts';
import { useGetDoctypesQuery, useGetFieldtypesQuery } from '@shared/helpers/rtk-query/firestoreApi.ts';
import { selectActiveDocument, setFailedCheckTargets } from '@shared/store/documentSlice';
import { useDispatch, useSelector } from '@shared/store/store';
import { ReactComponent as ChrevronDown } from '@svg/chevron-down.svg';
import clsx from 'clsx';
import React, { useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router';
import { useDeepCompareMemo } from 'use-deep-compare';

interface Props {}

const DocumentLabelerSidebarFields: React.FC<Props> = () => {
  const { inboxId } = useParams();
  const dispatch = useDispatch();
  // Get state from Redux
  const activeDocument = useSelector(selectActiveDocument);
  const { all: inboxDocTypes } = useGetDoctypesQuery({ inboxId }).data ?? {};
  const inboxFieldTypes = useGetFieldtypesQuery({ inboxId }).data;
  const historical = location.pathname.includes('historical');
  const [isOpen, setIsOpen] = useState(true);

  // Find entity types that are targets of failed checks and track counter requirements
  const { failedEntityTypes, entityTypeCounters } = useMemo(() => {
    if (!activeDocument?.approvalChecks) return { failedEntityTypes: new Set(), entityTypeCounters: {} };

    const failedTypes = new Set();
    const counters = {};

    // Loop through all approval checks
    activeDocument.approvalChecks.forEach((check) => {
      // Only process failed checks with type 'user'
      if (check.status === 'failed' && check.type === 'user') {
        // Check main check target element
        if (check.details?.targetElement?.type === 'entity_type') {
          const entityTypeId = check.details.targetElement.id;

          // If there's a counter, store it for later use
          if (check.details.counter) {
            counters[entityTypeId] = {
              count: check.details.counter.count,
              requirement: check.details.counter.requirement,
            };

            if (check.details.counter.count >= check.details.counter.requirement) {
              failedTypes.add(entityTypeId);
            }
          } else {
            // If no counter, add to failedTypes as before
            failedTypes.add(entityTypeId);
          }
        }

        // Check child items of the check
        if (check.details?.items) {
          check.details.items.forEach((item) => {
            if (item.status === 'failed' && item.targetElement?.type === 'entity_type') {
              const entityTypeId = item.targetElement.id;

              // If there's a counter, store it for later use
              if (item.counter) {
                counters[entityTypeId] = {
                  count: item.counter.count,
                  requirement: item.counter.requirement,
                };

                // Only add to failedTypes if counter meets or exceeds requirement
                if (item.counter.count >= item.counter.requirement) {
                  failedTypes.add(entityTypeId);
                }
              } else {
                // If no counter, add to failedTypes as before
                failedTypes.add(entityTypeId);
              }
            }
          });
        }
      }
    });

    return { failedEntityTypes: failedTypes, entityTypeCounters: counters };
  }, [activeDocument?.approvalChecks]);

  // Find entities that are targets of failed checks and update the Redux store
  // We use a separate variable for the local targets to avoid modifying the useMemo return value
  const localFailedTargets = Object.create(null);

  // Use useMemo to compute the targets and update Redux
  const { failedCheckTargets, targets } = useMemo(() => {
    if (!activeDocument?.approvalChecks) return { failedCheckTargets: {}, targets: {} };

    // Create a new object that is guaranteed to be extensible
    const targets = Object.create(null);

    // Clear the local targets object and copy properties from the new targets
    Object.keys(localFailedTargets).forEach((key) => {
      delete localFailedTargets[key];
    });

    // Loop through all approval checks
    activeDocument.approvalChecks.forEach((check) => {
      // Only process failed checks with type 'user'
      if (check.status === 'failed' && check.type === 'user') {
        // Check main check target element
        if (check.details?.targetElement?.type === 'entity') {
          const targetId = check.details.targetElement.id;
          targets[targetId] = true;
        } else if (check.details?.targetElement?.type === 'entity_type') {
          const entityTypeId = check.details.targetElement.id;
          const childId = check.details.targetElement.childId;
          // For entity_occurrences checks, we need to mark the entire complex field as failed
          // and let the UI handle highlighting of mandatory fields
          if (check.id === 'entity_occurrences') {
            // Mark the entity type as having missing mandatory fields
            targets[`${entityTypeId}:hasMissingMandatoryFields`] = true;
          } else if (childId) {
            // For other check types, if there's a childId, store it for highlighting specific fields
            // We'll use a special format to store both the entity type and the child ID
            targets[`${entityTypeId}:childType:${childId}`] = true;
          }
        }

        // Check child items of the check
        if (check.details?.items) {
          check.details.items.forEach((item) => {
            if (item.status === 'failed' && item.targetElement?.type === 'entity') {
              const targetId = item.targetElement.id;
              targets[targetId] = true;
            }
          });
        }
      }
    });

    // Mark all entities of the failed entity types
    // For entity types with counters, only mark existing entities as failed if count >= requirement
    if (
      (failedEntityTypes.size > 0 || Object.keys(entityTypeCounters).length > 0) &&
      activeDocument?.entities
    ) {
      activeDocument.entities.forEach((entity) => {
        // Check if entity type is in failedEntityTypes
        if (failedEntityTypes.has(entity.type)) {
          console.log(
            `Marking entity ID ${entity.id} as failed because its type ${entity.type} is targeted by a user check`,
          );
          if (entity?.id) {
            localFailedTargets[entity.id] = true;
          }
        }

        // Special handling for complex fields
        if (entity.typeDetails?.complexDefinition && entity.value) {
          // Check if this complex field has mandatory child fields that are missing
          const complexValue = entity.value as { complex: Record<string, IDocumentEnrichedEntity> };
          const complexDefinition = entity.typeDetails.complexDefinition;
          console.log(`Checking complex entity ID ${entity.id} for missing mandatory fields`);
          console.log(complexDefinition);

          if (complexDefinition?.entityTypes) {
            const mandatoryTypes = complexDefinition.entityTypes.filter((det) => det.mandatory);
            console.log(mandatoryTypes);

            // Check if any mandatory fields are missing or if their types are in failedEntityTypes
            const hasMissingMandatoryFields = mandatoryTypes.some((det) => {
              const match = Object.values(complexValue.complex).find(
                (child) => child.typeDetails.id === det.id,
              );
              // Mark as failed if mandatory field is missing or its value is null
              return (
                !match ||
                match.value === null ||
                // Also mark as failed if the field type is in failedEntityTypes
                (match && failedEntityTypes.has(match.type))
              );
            });

            // Check if this entity type is marked as having missing mandatory fields
            const entityTypeHasMissingMandatoryFields = targets[`${entity.type}:hasMissingMandatoryFields`];

            if (hasMissingMandatoryFields || entityTypeHasMissingMandatoryFields) {
              console.log(
                `Marking complex entity ID ${entity.id} as failed because it has missing mandatory fields`,
              );
              if (entity?.id) {
                localFailedTargets[entity.id] = true;

                // Only mark mandatory fields that are actually missing as failed
                mandatoryTypes.forEach((mandatoryType) => {
                  const matches = Object.entries(complexValue.complex).filter(
                    ([_, childEntity]) => childEntity.typeDetails.id === mandatoryType.id,
                  );

                  // If no matches or all matches have null values, mark them as failed
                  if (
                    matches.length === 0 ||
                    matches.every(([_, childEntity]) => childEntity.value === null)
                  ) {
                    // Find all child entities of this type and mark them as failed
                    Object.entries(complexValue.complex).forEach(([childKey, childEntity]) => {
                      if (childEntity.typeDetails.id === mandatoryType.id && childEntity.value === null) {
                        console.log(
                          `Marking child field ${childKey} as failed because it's a missing mandatory field`,
                        );
                        localFailedTargets[`${entity.id}:child:${childKey}`] = true;
                      }
                    });
                  }
                });
              }
            }
          }
        }
        // Note: We don't need to check entityTypeCounters here because failedEntityTypes already
        // contains only the entity types where count >= requirement
      });
    }

    // Copy all properties from localFailedTargets to targets
    Object.keys(localFailedTargets).forEach((key) => {
      targets[key] = true;
    });

    // Update the failedCheckTargets in the Redux store
    dispatch(setFailedCheckTargets(targets));
    return { failedCheckTargets: targets, targets };
  }, [
    activeDocument?.approvalChecks,
    activeDocument?.entities,
    failedEntityTypes,
    entityTypeCounters,
    dispatch,
  ]);

  // Store stable IDs for placeholders
  const stableIds = useRef(new Map());

  /**
   * Creates a temporary complex field placeholder.
   * This function builds a complex value based on the complexDefinition provided in the field type details.
   */
  const createTemporaryComplexField = (
    entityType: { id: string; minOccurrences?: number; maxOccurrences?: number },
    fieldTypeDetails: any,
    index: number,
  ): IDocumentEnrichedEntity => {
    const complexDefinition = fieldTypeDetails.complexDefinition;
    const complexTemplate: Record<string, IDocumentEnrichedEntity> = {};

    if (complexDefinition?.entityTypes) {
      complexDefinition.entityTypes.forEach((det, i) => {
        const details = inboxFieldTypes?.find((e) => e?.id === det?.id);
        complexTemplate[i] = {
          id: `placeholder-${i}`,
          type: det.id,
          typeDetails: details,
          value: null,
          rawValue: null,
          confidence: 0,
          source: 'user',
          valueLocations: [],
          isMandatory: !!det.mandatory,
        };
      });
    }

    // Create a stable ID key based on entity type and index
    const idKey = `complex-${entityType.id}-${index}`;

    // Use existing ID if available, otherwise generate a new one
    if (!stableIds.current.has(idKey)) {
      stableIds.current.set(idKey, `placeholder-${uuid4hex()}`);
    }

    return {
      id: stableIds.current.get(idKey),
      type: entityType.id,
      typeDetails: fieldTypeDetails,
      value: { complex: complexTemplate },
      rawValue: null,
      confidence: 0,
      source: 'user',
      valueLocations: [],
      isMandatory: true,
    };
  };

  /**
   * Generates placeholder entities for an entity type.
   * Shows only ONE placeholder per entity type:
   * - If mandatory fields are missing: shows one mandatory placeholder
   * - If no mandatory fields missing but max allows more: shows one optional placeholder
   * - If no min/max occurrences defined: always shows one unlimited placeholder
   */
  const generatePlaceholders = (
    entityType: { id: string; minOccurrences?: number; maxOccurrences?: number },
    fieldTypeDetails: any,
    existingCount: number,
  ): IDocumentEnrichedEntity[] => {
    const placeholders: IDocumentEnrichedEntity[] = [];
    const missingMandatory = entityType.minOccurrences
      ? Math.max(entityType.minOccurrences - existingCount, 0)
      : 0;

    // Check if this field type should always have a placeholder (no min/max occurrences defined)
    const shouldShowPlaceholderWithoutOccurrences = !entityType.minOccurrences && !entityType.maxOccurrences;

    if (fieldTypeDetails.complexDefinition) {
      // Show only ONE placeholder for missing mandatory fields (if any are missing)
      if (missingMandatory > 0) {
        placeholders.push(createTemporaryComplexField(entityType, fieldTypeDetails, 0));
      }
      // Show optional placeholder if max occurrences allows and no mandatory fields are missing
      else if (entityType.maxOccurrences && existingCount < entityType.maxOccurrences) {
        placeholders.push(createTemporaryComplexField(entityType, fieldTypeDetails, 999));
      }
      // Always show placeholder for fields without min/max occurrences (unlimited fields)
      else if (shouldShowPlaceholderWithoutOccurrences) {
        placeholders.push(createTemporaryComplexField(entityType, fieldTypeDetails, 1000));
      }
    } else {
      // Show only ONE placeholder for missing mandatory fields (if any are missing)
      if (missingMandatory > 0) {
        // Create a stable ID key for mandatory field
        const idKey = `simple-${entityType.id}-mandatory`;

        // Use existing ID if available, otherwise generate a new one
        if (!stableIds.current.has(idKey)) {
          stableIds.current.set(idKey, `placeholder-${uuid4hex()}`);
        }

        placeholders.push({
          id: stableIds.current.get(idKey),
          type: entityType.id,
          typeDetails: fieldTypeDetails,
          value: null,
          rawValue: null,
          confidence: 0,
          source: 'user',
          valueLocations: [],
          isMandatory: true,
        });
      }
      // Show optional placeholder if max occurrences allows and no mandatory fields are missing
      else if (entityType.maxOccurrences && existingCount < entityType.maxOccurrences) {
        // Create a stable ID key for the optional field
        const idKey = `simple-${entityType.id}-optional`;

        // Use existing ID if available, otherwise generate a new one
        if (!stableIds.current.has(idKey)) {
          stableIds.current.set(idKey, `placeholder-${uuid4hex()}`);
        }

        placeholders.push({
          id: stableIds.current.get(idKey),
          type: entityType.id,
          typeDetails: fieldTypeDetails,
          value: null,
          rawValue: null,
          confidence: 0,
          source: 'user',
          valueLocations: [],
          isMandatory: false,
        });
      }
      // Always show placeholder for fields without min/max occurrences (unlimited fields)
      else if (shouldShowPlaceholderWithoutOccurrences) {
        // Create a stable ID key for the field without occurrences
        const idKey = `simple-${entityType.id}-no-occurrences`;

        // Use existing ID if available, otherwise generate a new one
        if (!stableIds.current.has(idKey)) {
          stableIds.current.set(idKey, `placeholder-${uuid4hex()}`);
        }

        placeholders.push({
          id: stableIds.current.get(idKey),
          type: entityType.id,
          typeDetails: fieldTypeDetails,
          value: null,
          rawValue: null,
          confidence: 0,
          source: 'user',
          valueLocations: [],
          isMandatory: false,
        });
      }
    }
    return placeholders;
  };

  // Calculate missing fields data structure (without IDs)
  const missingFieldsData = useMemo(() => {
    if (!activeDocument || !inboxDocTypes || !inboxFieldTypes) return [];

    const currentDocType = inboxDocTypes.find((dt) => dt?.id === activeDocument?.docTypeId);
    if (!currentDocType || !currentDocType.entityTypes) return [];

    // Return a data structure that represents the missing fields without including the IDs
    // This will be used to determine if we need to regenerate the fields
    return currentDocType.entityTypes
      .map((entityType) => {
        if (!entityType || !entityType.id) return null;

        const fieldTypeDetails = inboxFieldTypes.find((type) => type?.id === entityType.id);
        if (!fieldTypeDetails) return null;

        const entities = Array.isArray(activeDocument.entities) ? activeDocument.entities : [];
        const existingEntities = entities.filter((entity) => entity?.type === entityType.id);

        // Just return the data needed to determine if fields have changed
        return {
          entityTypeId: entityType.id,
          existingCount: existingEntities.length,
          minOccurrences: entityType.minOccurrences || 0,
          maxOccurrences: entityType.maxOccurrences,
          hasNoOccurrences: !entityType.minOccurrences && !entityType.maxOccurrences,
        };
      })
      .filter(Boolean);
  }, [activeDocument, inboxDocTypes, inboxFieldTypes]);

  // Use deep comparison to only regenerate when the underlying data changes
  const stableMissingFieldsData = useDeepCompareMemo(() => missingFieldsData, [missingFieldsData]);

  // Generate the actual missing fields with stable IDs
  const missingFields = useMemo(() => {
    if (!activeDocument || !inboxDocTypes || !inboxFieldTypes) return [];

    const currentDocType = inboxDocTypes.find((dt) => dt?.id === activeDocument?.docTypeId);
    if (!currentDocType || !currentDocType.entityTypes) return [];

    return currentDocType.entityTypes
      .map((entityType) => {
        if (!entityType || !entityType.id) return null;

        const fieldTypeDetails = inboxFieldTypes.find((type) => type?.id === entityType.id);
        if (!fieldTypeDetails) return null;

        const entities = Array.isArray(activeDocument.entities) ? activeDocument.entities : [];
        const existingEntities = entities.filter((entity) => entity?.type === entityType.id);

        const placeholders = generatePlaceholders(entityType, fieldTypeDetails, existingEntities.length);

        // Mark placeholders as failed if their entity type is targeted
        // For entity types with counters, only mark placeholders as failed if count < requirement
        // For entity types without counters or with count >= requirement, mark all entities as failed
        if (entityTypeCounters[entityType.id]) {
          const counter = entityTypeCounters[entityType.id];
          if (counter.count < counter.requirement) {
            // Only mark placeholders as failed when count < requirement
            placeholders.forEach((placeholder) => {
              console.log(
                `Marking placeholder entity ID ${placeholder.id} as failed because its type ${entityType.id} has insufficient count (${counter.count}/${counter.requirement})`,
              );
              if (placeholder?.id) {
                localFailedTargets[placeholder.id] = true;
              }
            });
          } else {
            // If count >= requirement, don't mark placeholders as failed
            console.log(
              `Not marking placeholders of type ${entityType.id} as failed because count meets requirement (${counter.count}/${counter.requirement})`,
            );

            // Special handling for complex field placeholders
            // Even if count >= requirement, we still need to mark complex fields as failed if they have mandatory fields
            placeholders.forEach((placeholder) => {
              if (placeholder.typeDetails?.complexDefinition) {
                const complexDefinition = placeholder.typeDetails.complexDefinition;
                // Check if this entity type is marked as having missing mandatory fields from entity_occurrences check
                const entityTypeHasMissingMandatoryFields =
                  targets[`${placeholder.type}:hasMissingMandatoryFields`];

                if (
                  complexDefinition?.entityTypes?.some((det) => det.mandatory) ||
                  entityTypeHasMissingMandatoryFields
                ) {
                  console.log(
                    `Marking complex placeholder entity ID ${placeholder.id} as failed because it has mandatory fields`,
                  );
                  if (placeholder?.id) {
                    localFailedTargets[placeholder.id] = true;

                    // If it's a complex field with a value, mark all mandatory fields as failed
                    if (placeholder.value && entityTypeHasMissingMandatoryFields) {
                      const complexValue = placeholder.value as {
                        complex: Record<string, IDocumentEnrichedEntity>;
                      };
                      const mandatoryTypes = complexDefinition.entityTypes.filter((det) => det.mandatory);

                      mandatoryTypes.forEach((mandatoryType) => {
                        Object.entries(complexValue.complex).forEach(([childKey, childEntity]) => {
                          if (childEntity.typeDetails.id === mandatoryType.id && childEntity.value === null) {
                            console.log(
                              `Marking child field ${childKey} as failed because it's a mandatory field in a placeholder`,
                            );
                            localFailedTargets[`${placeholder.id}:child:${childKey}`] = true;
                          }
                        });
                      });
                    }
                  }
                }
              }
            });
          }
        } else if (failedEntityTypes.has(entityType.id)) {
          // For entity types without counters, mark all placeholders as failed
          placeholders.forEach((placeholder) => {
            console.log(
              `Marking placeholder entity ID ${placeholder.id} as failed because its type ${entityType.id} is targeted by a user check`,
            );
            if (placeholder?.id) {
              localFailedTargets[placeholder.id] = true;
            }
          });
        } else {
          // Special handling for complex field placeholders
          // Even if the entity type is not targeted, we still need to mark complex fields as failed if they have mandatory fields
          placeholders.forEach((placeholder) => {
            if (placeholder.typeDetails?.complexDefinition) {
              const complexDefinition = placeholder.typeDetails.complexDefinition;
              // Check if this entity type is marked as having missing mandatory fields from entity_occurrences check
              const entityTypeHasMissingMandatoryFields =
                targets[`${placeholder.type}:hasMissingMandatoryFields`];

              if (complexDefinition?.entityTypes) {
                const mandatoryTypes = complexDefinition.entityTypes.filter((det) => det.mandatory);
                if (mandatoryTypes.length > 0) {
                  // Check if any mandatory field types are in failedEntityTypes or if entity type has missing mandatory fields
                  const hasMandatoryFailedTypes = mandatoryTypes.some((det) => failedEntityTypes.has(det.id));

                  if (hasMandatoryFailedTypes || entityTypeHasMissingMandatoryFields) {
                    console.log(
                      `Marking complex placeholder entity ID ${placeholder.id} as failed because it has mandatory fields with failed types`,
                    );
                    if (placeholder?.id) {
                      localFailedTargets[placeholder.id] = true;

                      // If it's a complex field with a value, mark all mandatory fields as failed
                      if (placeholder.value && entityTypeHasMissingMandatoryFields) {
                        const complexValue = placeholder.value as {
                          complex: Record<string, IDocumentEnrichedEntity>;
                        };

                        mandatoryTypes.forEach((mandatoryType) => {
                          Object.entries(complexValue.complex).forEach(([childKey, childEntity]) => {
                            if (
                              childEntity.typeDetails.id === mandatoryType.id &&
                              childEntity.value === null
                            ) {
                              console.log(
                                `Marking child field ${childKey} as failed because it's a mandatory field in a placeholder`,
                              );
                              localFailedTargets[`${placeholder.id}:child:${childKey}`] = true;
                            }
                          });
                        });
                      }
                    }
                  }
                }
              }
            }
          });
        }

        return placeholders.length ? placeholders : null;
      })
      .filter(Boolean)
      .flat();
  }, [
    activeDocument,
    inboxDocTypes,
    inboxFieldTypes,
    stableMissingFieldsData,
    failedEntityTypes,
    entityTypeCounters,
    failedCheckTargets,
  ]); // Include dependencies for failed checks

  return (
    <>
      {missingFields?.length > 0 && !historical && (
        <div className={s.section_wrapper} style={{ flexShrink: 0 }}>
          <div className={s.section}>
            <div className={s.header}>
              <h2>Fields to add</h2>
            </div>
            <div className={s.section_content}>
              {missingFields.map((placeholderEntity) => (
                <DocumentLabelerSidebarField
                  key={placeholderEntity.id}
                  entity={placeholderEntity}
                  isFailedCheckTarget={failedCheckTargets[placeholderEntity.id] || false}
                />
              ))}
            </div>
          </div>
        </div>
      )}
      <div className={s.section_wrapper}>
        <div className={clsx(s.section, { [s.section__closed]: !isOpen })}>
          <div
            className={s.header}
            onClick={() => {
              if (historical) setIsOpen(!isOpen);
            }}
          >
            <h2>Fields</h2>
            {historical && <ChrevronDown />}
          </div>
          <div className={s.section_content}>
            {activeDocument?.entities.length === 0 && (
              <div className={s.section_empty}>
                <p>No fields in document</p>
              </div>
            )}
            {activeDocument?.entities.map((entity) => (
              <DocumentLabelerSidebarField
                key={entity.id}
                entity={entity}
                isFailedCheckTarget={failedCheckTargets[entity.id] || false}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default DocumentLabelerSidebarFields;
