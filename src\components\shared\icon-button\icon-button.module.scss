@import "src/shared/styles/vars/_vars";

.button {
  @include flex-center;
  height: 35px;
  padding: 10px;
  cursor: pointer;
  color: $white;
  background: $paperbox-blue;
  border-radius: 3px;
  transition: box-shadow 0.2s ease-in-out;

  &:hover:not(.disabled), &:focus:not(.disabled) {
    outline: none;
    box-shadow: 0 2px 8px rgba($paperbox-blue, 0.4);
    transition: box-shadow 0.1s ease-in-out;
  }

  &:active:not(.disabled) {
    background: #005DB3;
  }
}

.disabled {
  color: $dark-gray;
  background: rgba($dark-gray, 0.3);

  &:hover, &:focus {
    cursor: not-allowed;
    outline: none;
  }
}
