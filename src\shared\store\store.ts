import {
  Action,
  AnyAction,
  ThunkAction,
  ThunkDispatch,
  combineReducers,
  configureStore,
} from '@reduxjs/toolkit';
import * as Sentry from '@sentry/react';
import { analyticsApi } from '@shared/helpers/rtk-query/analyticsApi.ts';
import { backendApi } from '@shared/helpers/rtk-query/backendApi.ts';
import { firestoreApi } from '@shared/helpers/rtk-query/firestoreApi.ts';
import { realtimeApi } from '@shared/helpers/rtk-query/realtimeApi.ts';
import checksSlice from '@shared/store/checksSlice.ts';
import { documentSlice } from '@shared/store/documentSlice.ts';
import { labelerSlice } from '@shared/store/labelerSlice.ts';
import pendingOperationsSlice from '@shared/store/pendingOperationsSlice.ts';
import * as RR from 'react-redux';
import { TypedUseSelectorHook, useSelector as useStoreSelector } from 'react-redux';
import { adminSlice } from './adminSlice';
import { dashboardSlice } from './dashboardSlice';
import { documentListSlice } from './documentListSlice.ts';
import { notificationSlice } from './notificationSlice';
import { settingsSlice } from './settingsSlice';
import { subsSlice } from './subsSlice';
import { tenantSlice } from './tenantSlice';
import { userSlice } from './userSlice';

const sentryReduxEnhancer = Sentry.createReduxEnhancer({});

// ✅ Combine reducers and add RTK Query's reducer
const appReducer = combineReducers({
  user: userSlice.reducer,
  tenant: tenantSlice.reducer,
  documentList: documentListSlice.reducer,
  document: documentSlice.reducer,
  settings: settingsSlice.reducer,
  admin: adminSlice.reducer,
  labeler: labelerSlice.reducer,
  subs: subsSlice.reducer,
  dashboard: dashboardSlice.reducer,
  notification: notificationSlice.reducer,
  checks: checksSlice.reducer,
  pendingOperations: pendingOperationsSlice.reducer,
  [firestoreApi.reducerPath]: firestoreApi.reducer,
  [analyticsApi.reducerPath]: analyticsApi.reducer,
  [realtimeApi.reducerPath]: realtimeApi.reducer,
  [backendApi.reducerPath]: backendApi.reducer,
});

const rootReducer = (state, action) => {
  if (action.type === 'CLEAR_STORE') return appReducer(undefined, action);
  return appReducer(state, action);
};

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
      immutableCheck: false,
    })
      .concat(firestoreApi.middleware)
      .concat(analyticsApi.middleware)
      .concat(realtimeApi.middleware)
      .concat(backendApi.middleware),

  enhancers: [sentryReduxEnhancer],
});

// ✅ Update RootState & Dispatch types
export type RootState = ReturnType<typeof appReducer>;
export type AppThunk<ReturnType = void> = ThunkAction<ReturnType, RootState, unknown, Action<string>>;
type AppDispatch = ThunkDispatch<RootState, any, AnyAction> | any;

// ✅ Custom hooks for Redux usage
export const useSelector: TypedUseSelectorHook<RootState> = useStoreSelector;
export const useDispatch = () => RR.useDispatch<AppDispatch>();
