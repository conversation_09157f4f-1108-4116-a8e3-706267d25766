import { loginWithPopup } from '@components/auth/helpers/helpers.ts';
import { isPbxEmail } from '@shared/helpers/helpers.ts';
import { auth } from '@shared/store/setup/firebase-setup';
import { useDispatch, useSelector } from '@shared/store/store';
import s from '@shared/styles/component/auth/auth.module.scss';
import { ReactComponent as GoogleLogo } from '@svg/google.svg';
import clsx from 'clsx';
import { GoogleAuthProvider, signInWithPopup } from 'firebase/auth';
import React from 'react';
import { useNavigate } from 'react-router';

interface Props {
  handleError: (err) => void;
  isPrivate?: boolean;
  text: string;
}

const GoogleButton: React.FC<Props> = ({ text, handleError, isPrivate }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const allowedDomains = useSelector((state) => state.tenant.details.settings?.allowedDomains);
  const tenantId = useSelector((state) => state.tenant.tenantId);

  const handleGoogle = () => {
    const provider = new GoogleAuthProvider();

    if (isPrivate) {
      provider.addScope('https://www.googleapis.com/auth/cloud-identity.groups.readonly');
      provider.setCustomParameters({ hd: 'paperbox.ai' });
      signInWithPopup(auth, provider)
        .then((res) => {
          if (isPbxEmail(res.user.email)) {
            navigate('/inbox');
          } else {
            auth.signOut();
          }
        })
        .catch((err) => {
          handleError(err);
        });
    } else {
      let filteredDomains = [...allowedDomains];
      if (tenantId !== 'demo-tu27c') {
        filteredDomains = filteredDomains.filter((domain) => domain !== 'paperbox.ai');
      }
      loginWithPopup(provider, dispatch, filteredDomains, navigate, handleError);
    }
  };

  return (
    <button onClick={handleGoogle} className={clsx(s.button, s.button_main, s.button_google)}>
      <div className={s.button_main__left}>
        <GoogleLogo className={s.icon} />
      </div>
      <div className={s.button_main__right}>{text}</div>
    </button>
  );
};

export default GoogleButton;
