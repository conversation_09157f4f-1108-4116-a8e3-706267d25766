@import "@src/shared/styles/vars/_vars";



.page_grid {
  display: flex;
  overflow: auto;
  overflow-x: clip;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  max-height: 55vh;
  margin-top: 40px;
  flex-wrap: wrap;
  border-radius: 10px;
  gap: 30px;
  row-gap: 50px;
  padding: 50px 30px;
  background-color: #fcfcfc;
  border: 1px solid #EEEEEE;
}


.group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding: 10px;
  border: 1px solid $paperbox-blue--fade;
  gap: 20px;
}


.page {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 260px;
  padding: 15px 15px 5px 15px;
  cursor: pointer;
  user-select: none;
  aspect-ratio: 9/13;
  transition: aspect-ratio 400ms, transform 0.15s ease-in-out;

  &:hover {
    transform: scale(1.05);
  }
}


.page_image {
  position: absolute;
  display: inline-block;
  width: 85%;
  height: 85%;
  transform-origin: center;
  border-radius: 7px;
  border: 1px solid #EEEEEE;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &__faded {
    transform: scale(0.7) !important;
    box-shadow: none;
  }
}


.page_number {
  position: absolute;
  z-index: 110;
  top: -20px;
  left: calc(50% - 12px);
  font-size: 13px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  transition: background-color 0.15s ease-in-out, color 0.15s ease-in-out;
  border-radius: 5px;
  color: white;
  transform: translateX(-50%);
  background: #EEEEEE;
  gap: 3px;

  svg {
    margin-right: 10px;
  }

  &_add {
    color: $success;
    background: $success-light;

  }

  &_discard {
    color: $error;
    background: $error-light;

  }
}



.icon {
  position: absolute;
  z-index: 101;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  color: white;
  border-radius: 5px;

  box-shadow: $shadow-light;

  &_add {
    background-color: $success;

  }

  &_discard {
    background-color: $error;

  }
}


.page_wrapper {
  padding: 20px;
  cursor: pointer;
  border: 2px solid transparent;

}

