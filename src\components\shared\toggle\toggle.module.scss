@import "@shared/styles/vars/_vars";


.wrapper {
  font-family: $base-font;
  position: relative;
  display: flex;
  width: 34px;
  height: 18px;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  border-radius: 10px;
  background: rgba(45, 51, 87, 1);
  background: $medium-gray;

  &__active {
    transition: all 0.25s ease-in-out;
    background: $paperbox-blue;
  }

  &__disabled {
    cursor: default;
    user-select: none;
    opacity: 0.3;

    .slider {
      cursor: default;
    }
  }
}


.slider {
  position: absolute;
  z-index: 8;
  width: 18px;
  height: 18px;
  cursor: pointer;

  transition: all 0.25s ease-in-out;
  transform: translateX(0px);
  border: 2px solid $medium-gray;
  border-radius: 10px;
  background: white;

  &__active {
    transition: all 0.25s ease-in-out;
    transform: translateX(16px);
    border: 2px solid $paperbox-blue;

  }

}


.option {
  @include flex-center;
  position: absolute;
  z-index: 9;
  top: 0;
  width: 18px;
  height: 18px;
  cursor: pointer;

  &__left {
    left: 0;
  }

  &__right {
    right: 0;
  }

  span {
    color: white;
  }

}
