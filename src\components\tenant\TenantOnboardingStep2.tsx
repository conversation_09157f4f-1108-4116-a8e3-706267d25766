import StyledSelect, { DropdownOption } from '@components/shared/dropdown/StyledSelect';
import Tooltip from '@components/shared/tooltip/Tooltip';
import OnboardingImage from '@shared/assets/onboarding-3.jpg';
import s from '@shared/styles/component/auth/auth.module.scss';
import { ReactComponent as EyeIcon } from '@svg/eye-open.svg';
import { ReactComponent as EyeOffIcon } from '@svg/eye.svg';
import { ReactComponent as InfoIcon } from '@svg/info-icon.svg';
import clsx from 'clsx';
import React, { useEffect, useState, useMemo, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { AdditionalInfo, OnboardingState } from './onboarding-types';

interface Props {
  state: OnboardingState;
  setState: (state: OnboardingState) => void;
  setFormValid?: (isValid: boolean) => void;
}

// Environment options for Brio
const ENVIRONMENT_OPTIONS: DropdownOption[] = [
  { label: 'Production', value: 'production' },
  { label: 'Staging', value: 'acceptance' },
];

const TenantOnboardingStep2: React.FC<Props> = ({ state, setState, setFormValid }) => {
  const { t, i18n } = useTranslation();
  // Initialize form data based on the selected system and existing state
  const [formData, setFormData] = useState<AdditionalInfo>({
    // Brio fields
    office_id: state.additionalInfo?.office_id || '',
    sub_office_id: state.additionalInfo?.sub_office_id || '',
    pi2_key: state.additionalInfo?.pi2_key || '',
    environment: state.additionalInfo?.environment || 'production',
    recipient_default_service: state.additionalInfo?.recipient_default_service || '',

    // CCS fields
    url: state.additionalInfo?.url || '',
    username: state.additionalInfo?.username || '',
    password: state.additionalInfo?.password || '',
    account: state.additionalInfo?.account || '',
  });

  // State for password visibility
  const [showPi2Key, setShowPi2Key] = useState(false);
  const [showCCSPassword, setShowCCSPassword] = useState(false);

  // Initialize selected environment from state or default to production
  const [selectedEnvironment, setSelectedEnvironment] = useState<DropdownOption>(
    state.additionalInfo?.environment === 'acceptance' ? ENVIRONMENT_OPTIONS[1] : ENVIRONMENT_OPTIONS[0],
  );

  const lang = i18n?.resolvedLanguage;

  // Update formData when environment dropdown changes
  useEffect(() => {
    if (selectedEnvironment) {
      handleInputChange('environment', selectedEnvironment.value);
    }
  }, [selectedEnvironment]);

  // Check if all required fields are filled based on the selected system
  const isFormValid = useMemo(() => {
    if (state.system === 'brio') {
      return Boolean(formData.office_id && formData.sub_office_id && formData.pi2_key);
    }
    return Boolean(formData.url && formData.username && formData.password && formData.account);
  }, [formData, state.system]);

  // Update parent component with form validity
  useEffect(() => {
    if (setFormValid) {
      setFormValid(isFormValid);
    }
  }, [isFormValid, setFormValid]);

  const handleInputChange = (field: keyof AdditionalInfo, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Update parent state
    setState({
      ...state,
      additionalInfo: {
        ...state.additionalInfo,
        [field]: value,
      },
    });
  };

  // Render Brio fields
  const renderBrioFields = () => (
    <>
      <div className={s.form_group}>
        <label htmlFor="office_id" className={s.form_label}>
          {t('admin:connectors.officeId')} <span className={s.required}>*</span>
          <Tooltip content={t('admin:connectors.officeIdDescription')} position="top">
            <span className={s.info_icon}>
              <InfoIcon />
            </span>
          </Tooltip>
        </label>
        <input
          type="text"
          value={formData.office_id}
          onChange={(e) => handleInputChange('office_id', e.target.value)}
          className={s.form_input}
          placeholder={t('admin:connectors.officeId')}
          required
          autoComplete="off"
          data-testid="brio-office-id"
        />
      </div>

      <div className={s.form_group}>
        <label htmlFor="sub_office_id" className={s.form_label}>
          {t('admin:connectors.subOfficeId')} <span className={s.required}>*</span>
          <Tooltip content={t('admin:connectors.subOfficeIdDescription')} position="top">
            <span className={s.info_icon}>
              <InfoIcon />
            </span>
          </Tooltip>
        </label>
        <input
          type="text"
          value={formData.sub_office_id}
          onChange={(e) => handleInputChange('sub_office_id', e.target.value)}
          className={s.form_input}
          placeholder={t('admin:connectors.subOfficeId')}
          required
          autoComplete="off"
          data-testid="brio-sub-office-id"
        />
      </div>
      <div className={s.form_group}>
        <label htmlFor="brio-rds" className={s.form_label}>
          {t('admin:webhooks.brio.activitySettings.rds')} <span className={s.required}>*</span>
          <Tooltip content={t('admin:webhooks.brio.activitySettings.rdsDescription')} position="top">
            <span className={s.info_icon}>
              <InfoIcon />
            </span>
          </Tooltip>
        </label>
        <input
          type="text"
          value={formData.recipient_default_service}
          onChange={(e) => handleInputChange('recipient_default_service', e.target.value)}
          className={s.form_input}
          placeholder={t('admin:webhooks.brio.activitySettings.rds')}
          required
          autoComplete="off"
          data-testid="brio-rds"
        />
      </div>

      <div className={s.form_group}>
        <label htmlFor="pi2_key" className={s.form_label}>
          {t('admin:connectors.pi2Key')} <span className={s.required}>*</span>
          <Tooltip content={t('admin:connectors.pi2KeyDescription')} position="top">
            <span className={s.info_icon}>
              <InfoIcon />
            </span>
          </Tooltip>
        </label>
        <div className={s.password_wrapper}>
          <input
            type={showPi2Key ? 'text' : 'password'}
            value={formData.pi2_key}
            onChange={(e) => handleInputChange('pi2_key', e.target.value)}
            className={s.form_input}
            placeholder={t('admin:connectors.pi2Key')}
            required
            autoComplete="new-password"
            data-lpignore="true"
            data-testid="brio-pi2-key"
          />
          <button
            type="button"
            className={s.password_toggle}
            onClick={() => setShowPi2Key(!showPi2Key)}
            aria-label={showPi2Key ? t('admin:connectors.hideKey') : t('admin:connectors.showKey')}
          >
            {showPi2Key ? <EyeOffIcon /> : <EyeIcon />}
          </button>
        </div>
      </div>

      <div className={s.form_group}>
        <label htmlFor="environment" className={s.form_label}>
          {t('admin:connectors.environment')} <span className={s.required}>*</span>
          <Tooltip content={t('admin:connectors.environmentDescription')} position="top">
            <span className={s.info_icon}>
              <InfoIcon />
            </span>
          </Tooltip>
        </label>
        <StyledSelect
          options={ENVIRONMENT_OPTIONS}
          value={selectedEnvironment}
          style={{
            borderRadius: '5px',
            height: '48px',
            minHeight: '48px',
            maxHeight: '48px',
          }}
          onChange={(option) => setSelectedEnvironment(option as DropdownOption)}
          data-testid="brio-environment"
        />
      </div>
    </>
  );

  // Render CCS fields
  const renderCCSFields = () => (
    <>
      <div className={s.form_group}>
        <label htmlFor="url" className={s.form_label}>
          {t('admin:connectors.ccs.url')} <span className={s.required}>*</span>
          <Tooltip content={t('admin:connectors.ccs.urlDescription')} position="top">
            <span className={s.info_icon}>
              <InfoIcon />
            </span>
          </Tooltip>
        </label>
        <input
          type="text"
          value={formData.url}
          onChange={(e) => handleInputChange('url', e.target.value)}
          className={s.form_input}
          placeholder={t('admin:connectors.ccs.url')}
          required
          autoComplete="off"
          data-testid="ccs-url"
        />
      </div>

      <div className={s.form_group}>
        <label htmlFor="username" className={s.form_label}>
          {t('admin:connectors.ccs.username')} <span className={s.required}>*</span>
          <Tooltip content={t('admin:connectors.ccs.usernameDescription')} position="top">
            <span className={s.info_icon}>
              <InfoIcon />
            </span>
          </Tooltip>
        </label>
        <input
          type="text"
          value={formData.username}
          onChange={(e) => handleInputChange('username', e.target.value)}
          className={s.form_input}
          placeholder={t('admin:connectors.ccs.username')}
          required
          autoComplete="off"
          data-testid="ccs-username"
        />
      </div>

      <div className={s.form_group}>
        <label htmlFor="password" className={s.form_label}>
          {t('admin:connectors.ccs.password')} <span className={s.required}>*</span>
          <Tooltip content={t('admin:connectors.ccs.passwordDescription')} position="top">
            <span className={s.info_icon}>
              <InfoIcon />
            </span>
          </Tooltip>
        </label>
        <div className={s.password_wrapper}>
          <input
            type={showCCSPassword ? 'text' : 'password'}
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            className={s.form_input}
            placeholder={t('admin:connectors.ccs.password')}
            required
            data-lpignore="true"
            data-testid="ccs-password"
          />
          <button
            type="button"
            className={s.password_toggle}
            onClick={() => setShowCCSPassword(!showCCSPassword)}
            aria-label={
              showCCSPassword ? t('admin:connectors.hidePassword') : t('admin:connectors.showPassword')
            }
          >
            {showCCSPassword ? <EyeOffIcon /> : <EyeIcon />}
          </button>
        </div>
      </div>

      <div className={s.form_group}>
        <label htmlFor="account" className={s.form_label}>
          {t('admin:connectors.ccs.account')} <span className={s.required}>*</span>
          <Tooltip content={t('admin:connectors.ccs.accountDescription')} position="top">
            <span className={s.info_icon}>
              <InfoIcon />
            </span>
          </Tooltip>
        </label>
        <input
          type="text"
          value={formData.account}
          onChange={(e) => handleInputChange('account', e.target.value)}
          className={s.form_input}
          placeholder={t('admin:connectors.ccs.account')}
          required
          autoComplete="off"
          data-testid="ccs-account"
        />
      </div>
    </>
  );

  return (
    <div className={clsx(s.card_inner, s.card_inner__flex)}>
      <div className={s.card_inner_left}>
        <img
          src={OnboardingImage}
          alt={t('home:onboarding.title2')}
          loading="eager"
          style={{ width: '360px' }}
        />
      </div>
      <div className={s.card_inner_right}>
        <h2 className={s.card_inner_title}>
          {state.system === 'brio' && t('home:onboarding.titleBrio')}
          {state.system === 'ccs' && t('home:onboarding.titleCCS')}
          <Tooltip content={t('home:onboarding.moreInfo')} position="left">
            <a
              href={`https://support.paperbox.ai/${lang === 'fr' ? 'fr' : 'nl*'}/articles/********-paperbox-brio-integratie-instellen`}
              target={'_blank'}
              rel="noreferrer"
              className={s.info}
            >
              <span>{t('home:onboarding.help')}</span>
              <div className={s.info_icon}>
                <InfoIcon />
              </div>
            </a>
          </Tooltip>
        </h2>

        <form className={s.form_container} onSubmit={(e) => e.preventDefault()}>
          {state.system === 'brio' && renderBrioFields()}
          {state.system === 'ccs' && renderCCSFields()}
        </form>
      </div>
    </div>
  );
};

// Memo the component for performance
export default memo(TenantOnboardingStep2);
