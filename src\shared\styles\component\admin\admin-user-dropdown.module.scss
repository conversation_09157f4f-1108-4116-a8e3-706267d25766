@import "../../vars/_vars";

.container {
  position: relative;
  width: 200px;
  height: 33px;
}

.dropdown_wrapper {
  position: relative;
  user-select: none;
}

.circle {
  @include flex-center;
  font-size: 14px;
  position: absolute;
  z-index: 2;
  top: 0;
  right: 0;
  width: 32px;
  height: 32px;
  cursor: pointer;
  color: #4F6787;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: #fcfbfc;

}

.bottom {
  position: fixed;
  z-index: 5000;

  overflow-x: hidden;
  overflow-y: auto;
  max-height: 214px;
  padding-top: 5px;
  color: $font-color-black;
  border: 1px solid #EEEEEE;
  border-radius: 0 0 5px 5px;

  background: white;
  box-shadow: rgba(0, 0, 0, 0.1) 0 1px 2px;
}

.bottom__above {
  top: auto;
  bottom: 32px;
  padding-top: 0;

}

.bottom_row {
  font-size: 14px;
  display: flex;
  overflow: hidden;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 34px;
  max-height: 34px;
  padding: 10px;
  border-bottom: 1px solid #F7F9FB;

  &:last-of-type {
    border-bottom: none;
  }

  span {
    overflow: hidden;
    max-width: 80%;
    white-space: nowrap;

    text-overflow: ellipsis;
  }
}

.dropdown {
  font-size: 16px;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  width: 200px;
  height: 32px;
  padding: 10px;
  cursor: pointer;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: $white;

}


:global {
  .admin-dropdown-anim-enter {
    transform: scale(0.95) translateY(-30px);
    transform-origin: top center;
    opacity: 0;
  }

  .admin-dropdown-anim-enter-active {
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
    transform: scale(1) translateY(0);
    transform-origin: top center;
    opacity: 1;

  }

  .admin-dropdown-anim-exit {
    transform: scale(1) translateY(0);
    transform-origin: top center;
    opacity: 1;

  }

  .admin-dropdown-anim-exit-active {

    transform: scale(0.95) translateY(-40px);
    transform-origin: top center;
    opacity: 0;

  }
}
