import StyledSelect, { DropdownOption } from '@components/shared/dropdown/StyledSelect';
import OnboardingImage from '@shared/assets/onboarding-2.jpg';
import s from '@shared/styles/component/auth/auth.module.scss';
import { ReactComponent as TrashIcon } from '@svg/trash-icon-alt.svg';
import clsx from 'clsx';
import React, { useEffect, useState, memo } from 'react';
import { useTranslation } from 'react-i18next';
import { InboxItem, OnboardingState, SystemTemplates } from './onboarding-types';

interface Props {
  state: OnboardingState;
  setState: (state: OnboardingState) => void;
  templates: SystemTemplates;
}

const TenantOnboardingStep3: React.FC<Props> = ({ state, setState, templates }) => {
  const { t } = useTranslation();
  const [inboxTypeOptions, setInboxTypeOptions] = useState<DropdownOption[]>([]);
  const [languageOptions, setLanguageOptions] = useState<DropdownOption[]>([]);
  const [selectedInboxType, setSelectedInboxType] = useState<DropdownOption | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<DropdownOption | null>(null);
  const [inboxName, setInboxName] = useState('');

  // Initialize created inboxes from state
  const [createdInboxes, setCreatedInboxes] = useState<InboxItem[]>(state.inboxes || []);

  // Generate inbox type options based on the selected system template
  useEffect(() => {
    if (state.system && templates && templates[state.system]) {
      const options = Object.keys(templates[state.system]).map((key) => ({
        label: key.charAt(0).toUpperCase() + key.slice(1),
        value: key,
      }));
      setInboxTypeOptions(options);

      if (options.length > 0) {
        setSelectedInboxType(options[0]);
      }
    }
  }, [state.system, templates]);

  // Update language options when inbox type changes
  useEffect(() => {
    if (
      selectedInboxType &&
      state.system &&
      templates &&
      templates[state.system] &&
      templates[state.system][selectedInboxType.value]
    ) {
      const languages = templates[state.system][selectedInboxType.value].languages;

      const options = languages.map((lang) => ({
        label: lang.toUpperCase(),
        value: lang,
      }));

      setLanguageOptions(options);

      // Set selected language to first option if it exists
      if (options.length > 0) {
        setSelectedLanguage(options[0]);
      }
    }
  }, [selectedInboxType, state.system, templates]);

  // Auto-generate inbox name based on system, type, and language
  useEffect(() => {
    if (state.system !== 'other' && selectedInboxType && selectedLanguage) {
      const systemName = state.system === 'brio' ? 'Brio' : 'CCS';
      const inboxType = selectedInboxType.label;
      const language = selectedLanguage.value.toUpperCase();
      const generatedName = `${systemName} ${inboxType} ${language}`;
      setInboxName(generatedName);
    }
  }, [state.system, selectedInboxType, selectedLanguage]);

  /**
   * Add a new inbox to the list
   */
  const addInbox = () => {
    if (!inboxName.trim()) return;

    if (state.system === 'other') {
      const newInbox: InboxItem = {
        id: Date.now().toString(),
        name: inboxName.trim(),
        type: 'custom',
        language: 'en',
      };

      addInboxToState(newInbox, 'custom');
      return;
    }

    if (!selectedInboxType || !selectedLanguage) return;

    const connectorType = templates[state.system][selectedInboxType.value].connector;

    const newInbox: InboxItem = {
      id: Date.now().toString(),
      name: inboxName.trim(),
      type: selectedInboxType.value,
      language: selectedLanguage.value,
    };

    addInboxToState(newInbox, connectorType);
  };

  /**
   * Helper function to add inbox to state and update UI
   */
  const addInboxToState = (newInbox: InboxItem, connectorType: string) => {
    // Add to local state
    const updatedInboxes = [...createdInboxes, newInbox];
    setCreatedInboxes(updatedInboxes);
    setInboxName('');

    setState({
      ...state,
      connector: connectorType,
      inboxes: updatedInboxes,
    });
  };

  /**
   * Remove an inbox from the list
   */
  const removeInbox = (id: string) => {
    const updatedInboxes = createdInboxes.filter((inbox) => inbox.id !== id);
    setCreatedInboxes(updatedInboxes);

    setState({
      ...state,
      inboxes: updatedInboxes,
    });
  };

  /**
   * Render the inbox form based on system type
   */
  const renderInboxForm = () => (
    <div className={s.form_container}>
      {state.system !== 'other' && (
        <>
          <div className={s.form_group}>
            <label htmlFor="inboxType" className={s.form_label}>
              {t('admin:inboxes.inboxType')}
            </label>
            <StyledSelect
              options={inboxTypeOptions}
              value={selectedInboxType}
              onChange={(option) => setSelectedInboxType(option as DropdownOption)}
              style={{
                borderRadius: '5px',
                height: '48px',
                minHeight: '48px',
                maxHeight: '48px',
              }}
              data-testid="inbox-type-select"
            />
          </div>

          <div className={s.form_group}>
            <label htmlFor="language" className={s.form_label}>
              {t('admin:inboxes.language')}
            </label>
            <StyledSelect
              options={languageOptions}
              value={selectedLanguage}
              onChange={(option) => setSelectedLanguage(option as DropdownOption)}
              style={{
                borderRadius: '5px',
                height: '48px',
                minHeight: '48px',
                maxHeight: '48px',
              }}
              data-testid="language-select"
            />
          </div>
        </>
      )}

      <div className={s.form_group}>
        <label htmlFor="inboxName" className={s.form_label}>
          {t('admin:inboxes.name')}
        </label>
        <input
          name="inboxName"
          type="text"
          value={inboxName}
          onChange={(e) => setInboxName(e.target.value)}
          className={s.form_input}
          placeholder={t('admin:inboxes.name')}
          data-testid="inbox-name-input"
        />
      </div>

      <button
        onClick={addInbox}
        className={s.add_button}
        disabled={
          state.system === 'other'
            ? !inboxName.trim()
            : !selectedInboxType || !selectedLanguage || !inboxName.trim()
        }
        data-testid="add-inbox-button"
      >
        {t('home:onboarding.addInbox')}
      </button>
    </div>
  );

  /**
   * Render the list of created inboxes
   */
  const renderCreatedInboxes = () => {
    if (createdInboxes.length === 0) return null;

    return (
      <div className={s.created_inboxes}>
        <div className={s.inbox_list}>
          {createdInboxes.map((inbox) => (
            <div key={inbox.id} className={s.inbox_item} data-testid={`inbox-item-${inbox.id}`}>
              <div className={s.inbox_details}>
                <span className={s.inbox_name}>{inbox.name}</span>
                <span className={s.inbox_type}>
                  {state.system === 'other'
                    ? t('admin:inboxes.templates.custom')
                    : `${inbox.type} - ${inbox.language.toUpperCase()}`}
                </span>
              </div>
              <button
                className={s.remove_button}
                onClick={() => removeInbox(inbox.id)}
                aria-label={t('admin:inboxes.removeInbox', { name: inbox.name })}
                data-testid={`remove-inbox-${inbox.id}`}
              >
                <TrashIcon />
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={clsx(s.card_inner, s.card_inner__flex)}>
      <div className={s.card_inner_left}>
        <img
          src={OnboardingImage}
          alt={t('home:onboarding.titleInboxes')}
          loading="eager"
          style={{ width: '100%' }}
        />
      </div>
      <div className={s.card_inner_right}>
        <h2 className={s.card_inner_title}>{t('home:onboarding.subTitleInboxes')}</h2>
        {renderInboxForm()}
        {renderCreatedInboxes()}
      </div>
    </div>
  );
};

export default memo(TenantOnboardingStep3);
