@import "src/shared/styles/vars/_vars";






.button {
  font-size: 14px;
  width: 100%;
  padding: 12px;
  color: white;
  border-radius: 5px;
  background: $paperbox-blue;
  margin:30px 0;
}


.file_input {
  display: none;
}


.data {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(275px, 1fr));
  width: 100%;
  gap: 20px;
  padding:20px 0;

}


.row_header {
  font-weight: 500;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 30px;
  padding: 0px;
  border-radius: 5px;

  .title{
    display: flex;
    align-items: center;

    span{
      margin-top:2px;
    }
  }

  svg{
    width: 18px;
    height: 18px;
    margin-left:0;
    margin-right: 5px;
  }
}


.row {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: stretch;
  border: 1px solid #eeeeee;
  border-radius: 5px;
  padding: 10px;
}


.list {
  display: flex;
  overflow: auto;
  flex-direction: column;
  max-height: 55px;
  margin-top: 10px;
  border-radius: 5px;
  gap: 4px;
  &__faded{
    opacity: 0.5;
    background-color: $white;
  }
  &__empty{
    height: 100%;
    max-height: 100%;
    align-items: center;
    justify-content: center;
  }

}


.list_item {
  padding: 5px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
