@import "./src/shared/styles/vars/_vars";


.container {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  height: 100vh;
  background: #0F1725;
}


.button_warning {
  background-color: $error !important;
}


.card {
  display: flex;
  flex-direction: column;
  width: 360px;
  margin: 0 auto 15vh;
  padding: 26px 26px 16px;
  color: $font-color-black;
  border-radius: 5px;
  background-color: $white;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);

}


.input_container {
  margin-top: 16px;
  color: black !important;


  input {
    font-family: $base-font;

    font-size: rem(14);
    font-weight: 500;
    line-height: 25px;
    //width: 150px;
    height: 33px;
    padding: 10px;
    letter-spacing: 0.01rem;
    border: 1px solid #CACACA;
    border-radius: 5px;
    outline: none !important;
    background: #FFFFFF;
  }
}


.button {
  font-size: rem(14) !important;
  width: 130px;
  min-width: 100px !important;
  margin: 18px auto 0;


  &__alt {
    color: black;
    background: $medium-gray;


    &:hover {
      opacity: 0.8;
      background: $medium-gray;
    }
  }
}


.buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}


.logo {
  width: 200px;
  height: auto;
  margin-bottom: 100px;
  cursor: pointer;
}


.title {
  font-size: rem(18);
  font-weight: 700;
}


.sub {
  font-size: rem(14);
  line-height: 1.25;
  width: 90%;
  margin-top: 16px;


  b {
    font-weight: 700;
  }
}


.code_container {
  display: flex;
  flex-direction: column;
  justify-content: center;
}


.error {
  padding-top: 15px;
  text-align: center;
  color: $error;
}


.code_inputs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  padding: 0 20px;
}


.code_input {
  font-family: $base-font;
  font-size: rem(32);
  font-weight: 700;
  width: 30px !important;
  padding-bottom: 0;
  transition: border-bottom-color 0.2s ease-in-out;
  text-align: center;
  border: none;
  border-bottom: 2px solid #DFE2E5;
  outline: none;

}


.code_input__active {
  transition: border-bottom-color 0.2s ease-in-out;
  border-bottom: 2px solid #4F6787;
}


.code_input__error {
  transform: translate3d(0, 0, 0);
  animation: shake 0.82s cubic-bezier(.36, .07, .19, .97) both;

}


@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }

  30%, 50%, 70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%, 60% {
    transform: translate3d(4px, 0, 0);
  }
  100% {
    border-color: $error;
  }
}
