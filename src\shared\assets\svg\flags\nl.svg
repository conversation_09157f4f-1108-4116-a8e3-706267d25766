<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="21px" height="15px" viewBox="0 0 21 15" version="1.1">
    <title>NL</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-nl1">
            <stop stop-color="#FFFFFF" offset="0%"/>
            <stop stop-color="#F0F0F0" offset="100%"/>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-nl2">
            <stop stop-color="#CA2B39" offset="0%"/>
            <stop stop-color="#AC1F2C" offset="100%"/>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-nl3">
            <stop stop-color="#2C56A2" offset="0%"/>
            <stop stop-color="#244889" offset="100%"/>
        </linearGradient>
    </defs>
    <g id="Symbols" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="NL">
            <rect id="FlagBackground" fill="url(#linearGradient-nl1)" x="0" y="0" width="21" height="15"/>
            <rect id="Rectangle-2" fill="url(#linearGradient-nl2)" x="0" y="0" width="21" height="5"/>
            <rect id="Rectangle-2" fill="url(#linearGradient-nl3)" x="0" y="10" width="21" height="5"/>
            <rect id="Rectangle-2" fill="url(#linearGradient-nl1)" x="0" y="5" width="21" height="5"/>
        </g>
    </g>
</svg>
