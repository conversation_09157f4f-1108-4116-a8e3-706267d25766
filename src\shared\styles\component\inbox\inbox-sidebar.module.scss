@import "../../vars/_vars";


.container {
  display: flex;
  flex-direction: column;
  width: calc(100% - 10px);
  height: 100%;
  border: 1px solid $medium-gray;
  background: $light-gray;
}


.header {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  height: 21px;
  margin-top: 40px;
  margin-bottom: 40px;
  padding: 0 30px;

  a {
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: center;
    gap: 6px;
  }

}


.dropdown {
  padding: 0 28px;
}


.segment {
  position: relative;
  padding: 24px 0;

  &__top {
    border-top: 1px solid $medium-gray;
  }
}


.segment_head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 28px 12px 28px;
}


.segment_icon {
  @include flex-center;
  position: absolute;
  right: 28px;
  width: auto;
  height: 20px;
  margin-right: 20px;
  cursor: pointer;
  color: $font-color-black;
  outline: none;

  svg {
    width: 14px;
    height: 14px;
  }
}


.segment_title {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  height: 24px;
  user-select: none;
}


.search {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 24px;
  margin-right: 10px;
  padding: 0 8px;
  cursor: pointer;
  color: $dark-gray;
  border: 1px solid rgba($dark-gray, 0.2);
  border-radius: 5px;
  background: $white;
  will-change: box-shadow;

  &:focus {
    outline: none;
    box-shadow: 0 0 1px 2px $paperbox-blue--fade;
  }
}


.logo {
  width: auto;
  height: 21px;
}


.tag {
  font-size: 11px;
  font-weight: 800;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  text-transform: uppercase;
  color: $paperbox-blue;
  border-radius: 5px;
  background: rgba($paperbox-blue, 0.1);

  span {
    margin-bottom: -2px;
  }
}


.list {
  display: flex;
  overflow: auto;
  flex-direction: column;
  max-height: calc(100vh - 490px);
}


.item_wrapper {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  border-radius: 7px;
  margin-block: 4px;
  margin-inline: 10px;

  &:active, &:focus, &:hover {
    outline: none;
    background: rgb(255, 255, 255);

  }

  &:focus .item, &:hover .item {
    outline: none;
    background: rgb(255, 255, 255);
  }

}


.item_child_wrapper {
  //border-radius: 0 0 7px 7px;
}


.item {
  font-family: $base-font;
  position: relative;
  display: flex;
  align-items: center;
  flex-grow: 1;
  justify-content: flex-start;
  width: auto;
  height: 39px;
  padding: 0 10px;
  border-radius: 7px;
  scroll-behavior: smooth;

  &:active, &:focus {
    outline: none;
    background: rgb(255, 255, 255);
  }
}


.item_icon {
  width: 20px;
  min-width: 20px;
  height: 100%;
  margin-right: 10px;
  margin-left: 10px;
  color: $font-color-black;
}


.item_child {
  margin-inline: 0px;

  & .item {
    margin: 0;
  }
}


.item_label {
  @include flex-center;
  font-size: rem(12);
  font-weight: 800;
  height: 18px;
  margin-right: 20px;
  margin-left: auto;
  padding: 3px 8px;
  color: $font-color-black;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.1);
}


.item_text {
  font-size: rem(14);
  font-weight: 400;
  line-height: 1.2;
  display: -webkit-box;
  overflow: hidden;
  margin-top: 2px;
  margin-right: rem(8);
  text-transform: capitalize;
  text-overflow: ellipsis;
  word-wrap: break-word;
  color: $font-color-black;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;

}


.item_actions {
  position: absolute;
  top: 10px;
  right: 2px;
  display: flex;
  align-items: flex-end;

}


.item_dropdown {
  width: 20px;
  height: 20px;
  margin-left: auto;
  transition: transform 0.2s ease-in-out;
  transform: rotate(0deg);
  text-align: center;

  svg {
    color: $font-color-black;
  }
}


.item_dropdown__active {
  transition: transform 0.2s ease-in-out;
  transform: rotate(180deg);
}


.item_dots {
  width: 20px;
  height: 20px;
  margin-left: auto;
  cursor: pointer;
  text-align: center;

}


.item__active {
  background: white;
}
.item__faded{
  opacity: 0.5;
    font-style: italic;
}


.item__main_active {
  background: white;
}


.item_wrapper__active {
  background: white;

}


.item_wrapper__active .item_child {
  background: white;
}


.item__active .item_text {
  color: $paperbox-blue;
}


.item__active .item_label {
  color: white;
  background: $paperbox-blue;
}


.item__active .item_icon {
  color: $paperbox-blue;
}


:global {

  .inbox-sidebar-anim-enter {
    height: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
  }

  .inbox-sidebar-anim-enter-active {
    height: 39px;
    padding-top: 7.5px;
    padding-bottom: 7.5px;
    transition: all 0.250s ease-in-out;
    opacity: 1;
  }

  .inbox-sidebar-anim-exit {
    height: 39px;
    padding-top: 7.5px;
    padding-bottom: 7.5px;
    opacity: 1;
  }

  .inbox-sidebar-anim-exit-active {
    height: 0;
    margin: 0;
    padding-top: 0;
    padding-bottom: 0;

    transition: all 0.250s ease-in-out;
    opacity: 0;
  }

  .item__wrapper-enter {
    opacity: 0;
  }

  .item__wrapper-enter-active {
    transition: opacity 250ms ease-in-out;
    opacity: 1;
  }

  .item__wrapper-exit {
    opacity: 1;
  }

  .item__wrapper-exit-active {
    transition: opacity 250ms ease;
    opacity: 0;
  }
}
