@import "src/shared/styles/vars/_vars";


.container {
  font-size: rem(22);
  position: relative;
  display: block;
  width: 17px;
  height: 17px;
  cursor: pointer;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-select: none;
  user-select: none;

}


.hidden {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  cursor: pointer;
  opacity: 0;

  &:disabled + div {
    cursor: not-allowed;
    opacity: 0.3;
  }
}


.checkbox {
  position: absolute;
  top: 0;
  left: 0;
  width: 17px;
  height: 17px;
  border: 1px solid rgba($paperbox-blue-medium, 0.2);
  border-radius: 4px;
  background-color: rgba($paperbox-blue-medium, 0.08);

}


.hidden:checked ~ .checkbox {
  background-color: $paperbox-blue;
}


.hidden:indeterminate ~ .checkbox {
  background-color: $paperbox-blue;
}


.hidden:focus ~ .checkbox {
  background-color: $paperbox-blue--fade;
}


svg.checkmark {
  position: absolute;
  top: 2px;
  left: 3px;
  display: none;
  width: 12px;
  height: auto;
  pointer-events: none;
  color: $white !important;
}


.indeterminate {
  position: absolute;
  top: 7px;
  left: 4px;
  display: none;
  width: 9px;
  height: 3px;
  pointer-events: none;
  border-radius: 4px;
  background: $white !important;
}


.hidden:checked ~ .checkmark {
  display: block;
}


.hidden:focus ~ .checkmark {
  display: block;
}


.hidden:indeterminate ~ .indeterminate {
  display: block;
}


