import { IClientDocType, IClientDocTypeSubType } from '@shared/helpers/converters/doctype.ts';
import { IClientFieldType } from '@shared/helpers/converters/fieldtype.ts';
import { IClientTag } from '@shared/helpers/converters/tag.ts';
import camelcaseKeys from 'camelcase-keys';
import { Timestamp } from 'firebase/firestore';
import snakecaseKeys from 'snakecase-keys';

export interface IRawDocument {
  action?: IRawDocumentAction;
  active: boolean;
  id: string;
  approval_checks: IRawDocumentApprovalCheck[];
  alternative_classification_results?: IRawDocumentAltClassificationResult[];
  available_time?: Timestamp | string;
  breadcrumbs: any; //Not used by frontend
  confidence: number;
  classification?: IRawDocumentClassification;
  dimensions: IRawDocumentDimension[];
  doc_subtype_id?: string;
  doc_type_id: string;
  document_id: string;
  entities: Record<string, IRawDocumentEntity>;
  gcs_location: string; //Not used by frontend
  gcs_raw_location: string; //Not used by frontend
  inbox_id: string;
  initial_approval_checks: IRawDocumentApprovalCheck[];
  language?: string; //Not used by frontend
  languages?: string[]; //Not used by frontend
  last_opened_date?: Timestamp | string;
  last_user_update_by?: string;
  last_user_update_time?: Timestamp | string;
  latest_workflow_run?: IRawDocumentWorkflowRun;
  locker?: string;
  metadata?: Record<string, any>;
  n_active_mutations?: number;
  n_mutations?: number;
  name: string;
  summary?: IRawDocumentSummary;
  processed: boolean;
  processed_messages?: any; //Not used by frontend
  provider_document_id?: string;
  router_id?: string; //Not used by frontend
  tag_type_id?: string;
  tenant_id: string;
  topology: IRawDocumentTopology;
  upload_time: Timestamp | string;
}

export interface IRawDocumentAction {
  actor_email: string;
  actor_id: string;
  metadata?: any;
  timestamp: Timestamp | string;
  type: string;
}
export interface IRawDocumentAltClassificationResult {
  confidence: number;
  doc_type_id: string;
  doc_subtype_id?: string;
}

export interface IRawDocumentClassification {
  results: {
    confidence: number;
    doc_type_id: string;
    doc_subtype_id?: string;
    documents: Record<string, { similarity: number }>;
  }[];
}
export interface IRawDocumentDimension {
  height: number;
  width: number;
}
export interface IRawDocumentEntity {
  confidence: number;
  categoryId?: string;
  isChecked: boolean;
  isSuggestion: boolean;
  ocrConfidence: number;
  pageNo?: number;
  rawValue: any;
  source: string;
  type: string;
  value: IRawDocumentEntityValue;
  valueLocations: IRawDocumentValueLocation[];
}

export type IRawDocumentEntityValue =
  | IRawDocumentEntityTextValue
  | IRawDocumentEntityOptionsValue
  | IRawDocumentEntityBoolValue
  | IRawDocumentEntityImageValue
  | IRawDocumentEntityComplexValue;
export type IRawDocumentEntityTextValue = string;
export type IRawDocumentEntityOptionsValue = string;
export type IRawDocumentEntityBoolValue = boolean;
export type IRawDocumentEntityImageValue = string;
export type IRawDocumentEntityComplexValue = { complex: Record<string, IRawDocumentEntity> };

export interface IRawDocumentValueLocation {
  confidence?: number; //Not used by frontend
  ocrConfidence?: number; //Not used by frontend
  pageNo?: number; //Not used by frontend
  rawValue?: any; //Not used by frontend
  source?: string; //Not used by frontend
  x1: number;
  x2: number;
  y1: number;
  y2: number;
}

export interface IRawDocumentSummary {
  value: string;
}

export interface IRawDocumentApprovalCheck {
  id: string;
  status: 'succeeded' | 'failed' | 'warning' | 'info';
  type: 'system' | 'user';
  details?: {
    title?: IDocumentTLItem;
    description?: IDocumentTLItem;
    items?: IRawDocumentApprovalCheckItem[];
    counter?: {
      count: number;
      requirement: number;
    };
    tab?: 'fields' | 'classification' | 'masterdata';
    target_element?: {
      type: 'entity' | 'classification' | 'entity_type' | 'classification_type';
      id: string;
      child_id?: string;
    };
  };
}

export interface IRawDocumentApprovalCheckItem {
  id: string;
  name: IDocumentTLItem;
  status: 'succeeded' | 'failed' | 'warning' | 'info';
  description?: IDocumentTLItem;
  counter?: {
    count: number;
    requirement: number;
  };
  target_element?: {
    type: 'entity' | 'classification' | 'entity_type' | 'classification_type';
    id: string;
    child_id?: string;
  };
}
export interface IRawDocumentTopology {
  alt_extensions?: string[]; //Not used by frontend
  content_checksum: string; //Not used by frontend
  content_modified: boolean;
  content_ready: boolean;
  content_type: string; //Not used by frontend
  extension: string;
  modified_content_checksum?: string; //Not used by frontend
  name: string;
  transformable: boolean;
  parts: Record<string, IRawDocumentPart>;
  upload_time?: Timestamp | string; //Not used by frontend
}

export interface IRawDocumentPart {
  alt_extensions?: string[]; //Not used by frontend
  alternative_classification_results?: IRawDocumentAltClassificationResult[];
  archived: boolean;
  confidence: number;
  content_checksum: string; //Not used by frontend
  content_id: string; //Not used by frontend
  content_modified: boolean;
  content_type: string; //Not used by frontend
  doc_subtype_id?: string;
  doc_type_id: string;
  extension: string;
  modified_content_checksum?: string; //Not used by frontend
  name: string;
  pages: IRawDocumentPartPage[];
  topology_type: string; // Needs clearer defined typing;
  transformable: boolean;
}

export interface IRawDocumentPartPage {
  archived: boolean;
  bundle_page_no: number;
  page_no: number;
  height: number;
  width: number;
}
export interface IRawDocumentWorkflowRun {
  attempt: number;
  correlation_id: string;
  params: Record<string, any>;
  status: string;
  time_finished?: Timestamp | string;
  time_provisioned?: Timestamp | string;
  time_started?: Timestamp | string;
  workflow_name: string;
}

export interface IDocumentEnriched extends IDocument {
  docTypeDetails?: IDocumentEnrichedDoctype;
  entities: IDocumentEnrichedEntity[];
  tagDetails?: IClientTag;
  topology: IDocumentTopologyEnriched;
}
export interface IDocumentEnrichedDoctype extends IClientDocType {
  subTypeDetails?: IClientDocTypeSubType;
}
export interface IDocumentEnrichedEntity extends IDocumentEntity {
  typeDetails?: IClientFieldType;
  value: boolean | string | IDocumentEnrichedEntityComplexValue;
}
export interface IDocumentEnrichedEntityComplexValue extends IDocumentEntityComplexValue {
  complex: Record<string, IDocumentEnrichedEntity>;
}
export interface IDocumentTopologyPartEnriched extends IDocumentPart {
  docTypeDetails?: IDocumentEnrichedDoctype;
}

export interface IDocument {
  action?: IDocumentAction;
  active: boolean;
  id: string;
  approvalChecks: IDocumentApprovalCheck[];
  alternativeClassificationResults?: IDocumentAltClassificationResult[];
  availableTime?: Date;
  confidence: number;
  classification?: IDocumentClassification;
  dimensions: IDocumentDimension[];
  docSubtypeId?: string;
  docTypeId: string;
  documentId: string;
  entities: IDocumentEntity[];
  inboxId: string;
  initialApprovalChecks: IDocumentApprovalCheck[];
  lastOpenedDate?: Date;
  lastUserUpdateBy?: string;
  lastUserUpdateTime?: Date;
  latestWorkflowRun?: IDocumentWorkflowRun;
  locker?: string;
  metadata?: Record<string, any>;
  nActiveMutations?: number;
  nMutations?: number;
  name: string;
  summary?: IDocumentSummary;
  processed: boolean;
  providerDocumentId?: string;
  tagTypeId?: string;
  tenantId: string;
  topology: IDocumentTopology;
  uploadTime: Date;
  isRowChecked?: boolean; // FRONTEND ONLY
}

export interface IDocumentAction {
  actorEmail: string;
  actorId: string;
  metadata?: any;
  timestamp: Date;
  type: string;
}

export interface IDocumentAltClassificationResult {
  confidence: number;
  docTypeId: string;
  docSubtypeId?: string;
}

export interface IDocumentClassification {
  results: {
    confidence: number;
    docTypeId: string;
    docSubtypeId?: string;
    documents: Record<string, { similarity: number }>;
  }[];
}

export interface IDocumentDimension {
  height: number;
  width: number;
}

export interface IDocumentEntity {
  id: string;
  categoryId?: string;
  confidence: number;
  isChecked?: boolean;
  isSuggestion?: boolean;
  ocrConfidence?: number; // maybe make optional
  isMandatory?: boolean; // FRONTEND ONLY
  pageNo?: number;
  rawValue: any;
  source: string;
  type: string;
  value: IDocumentEntityValue;
  valueLocations: IDocumentValueLocation[];
}

// Lots of duplication here, but it's for future-proofing
export type IDocumentEntityValue =
  | IDocumentEntityTextValue
  | IDocumentEntityOptionsValue
  | IDocumentEntityBoolValue
  | IDocumentEntityImageValue
  | IDocumentEntityComplexValue;
export type IDocumentEntityTextValue = string;
export type IDocumentEntityOptionsValue = string;
export type IDocumentEntityBoolValue = boolean;
export type IDocumentEntityImageValue = string;
export type IDocumentEntityComplexValue = { complex: Record<string, IDocumentEntity> };

export interface IDocumentValueLocation {
  x1: number;
  x2: number;
  y1: number;
  y2: number;
  pageNo?: number;
}

export interface IDocumentTLItem {
  key: string; // Translation key
  values?: Record<string, any>; // Dynamic values for interpolation
  fallback?: string; // Fallback text if translation is missing
}

export interface IDocumentApprovalCheck {
  id: string;
  status: 'succeeded' | 'failed' | 'warning' | 'info';
  type?: 'system' | 'user';
  details?: {
    title?: IDocumentTLItem;
    description?: IDocumentTLItem;
    items?: IDocumentApprovalCheckItem[];
    counter?: {
      count: number;
      requirement: number;
    };
    tab?: 'fields' | 'classification' | 'masterdata';
    targetElement?: {
      type: 'classification' | 'entity' | 'entity_type' | 'classification_type';
      id: string;
      childId?: string;
    };
  };
}

export interface IDocumentApprovalCheckItem {
  id: string;
  name: IDocumentTLItem;
  status: 'succeeded' | 'failed' | 'warning' | 'info';
  description?: IDocumentTLItem;
  counter?: {
    count: number;
    requirement: number;
  };
  targetElement?: {
    type: 'classification' | 'entity' | 'entity_type' | 'classification_type';
    id: string;
    childId?: string;
  };
}
export interface IDocumentSummary {
  value: string;
}

export interface IDocumentTopology {
  contentModified: boolean;
  contentReady: boolean;
  extension: string;
  name: string;
  parts: IDocumentPart[];
  transformable?: boolean;
}
export interface IDocumentTopologyEnriched extends IDocumentTopology {
  parts: IDocumentTopologyPartEnriched[];
}

export interface IDocumentPart {
  id: string;
  altExtensions?: string[]; //Not used by frontend
  alternativeClassificationResults?: IDocumentAltClassificationResult[];
  archived: boolean;
  confidence: number;
  contentModified: boolean;
  contentId: string; //Not used by frontend
  contentChecksum?: string; //Not used by frontend
  contentType?: string; //Not used by frontend
  modifiedContentChecksum?: string; //Not used by frontend
  docSubtypeId?: string;
  docTypeId: string;
  extension: string;
  name: string;
  pages: IDocumentPartPage[];
  topologyType: string;
  transformable: boolean;
}

export interface IDocumentPartPage {
  archived: boolean;
  bundlePageNo: number;
  pageNo: number;
  height: number;
  width: number;
}

export interface IDocumentWorkflowRun {
  attempt: number;
  correlationId: string;
  params: Record<string, any>;
  status: string;
  timeFinished?: Date;
  timeProvisioned?: Date;
  timeStarted?: Date;
  workflowName: string;
}

export function documentRawToClient(raw: IRawDocument): IDocument {
  const entities = Object.entries(raw.entities).map(([key, entity]) => convertDocumentEntity(key, entity));
  const classification = raw.classification
    ? {
        results: raw.classification.results.map((result) => ({
          confidence: result.confidence,
          docTypeId: result.doc_type_id,
          docSubtypeId: result.doc_subtype_id,
          documents: result.documents,
        })),
      }
    : undefined;
  try {
    return {
      active: raw.active,
      action: raw.action ? convertDocumentAction(raw.action) : undefined,
      id: raw.id,
      // Use mock approval checks instead of the ones from the raw document
      approvalChecks: raw.approval_checks.map(convertApprovalCheck),
      availableTime: raw.available_time instanceof Timestamp ? raw.available_time?.toDate() : null,
      alternativeClassificationResults: camelcaseKeys(raw.alternative_classification_results, { deep: true }),
      confidence: raw.confidence,
      classification: classification,
      dimensions: raw.dimensions.map((dim) => ({ height: dim.height, width: dim.width })),
      docSubtypeId: raw.doc_subtype_id,
      docTypeId: raw.doc_type_id,
      documentId: raw.document_id,
      entities: entities,
      inboxId: raw.inbox_id,
      // Use the same mock approval checks for initial checks
      initialApprovalChecks: raw.initial_approval_checks.map(convertApprovalCheck),
      lastOpenedDate: raw.last_opened_date instanceof Timestamp ? raw.last_opened_date?.toDate() : null,
      lastUserUpdateBy: raw.last_user_update_by,
      lastUserUpdateTime:
        raw.last_user_update_time instanceof Timestamp ? raw.last_user_update_time?.toDate() : null,
      latestWorkflowRun: raw.latest_workflow_run ? convertWorkflowRun(raw.latest_workflow_run) : undefined,
      locker: raw.locker,
      metadata: raw.metadata,
      nActiveMutations: raw.n_active_mutations,
      nMutations: raw.n_mutations,
      name: raw.name,
      summary: raw.summary,
      processed: raw.processed,
      providerDocumentId: raw.provider_document_id,
      tagTypeId: raw.tag_type_id,
      tenantId: raw.tenant_id,
      topology: convertTopology(raw.topology),
      uploadTime: raw.upload_time instanceof Timestamp ? raw.upload_time.toDate() : null,
    };
  } catch (e) {
    console.log(`ERROR ON DOCUMENT CONVERSION: ${raw.document_id},${e}`);
  }
}

function convertDocumentAction(raw: IRawDocumentAction): IDocumentAction {
  return {
    actorEmail: raw.actor_email,
    actorId: raw.actor_id,
    metadata: raw.metadata,
    timestamp: raw.timestamp instanceof Timestamp ? raw.timestamp.toDate() : null,
    type: raw.type,
  };
}

function convertApprovalCheck(raw: IRawDocumentApprovalCheck): IDocumentApprovalCheck {
  const result: IDocumentApprovalCheck = {
    id: raw.id,
    status: raw.status,
    type: raw.type || 'user',
  };

  if (raw.details) {
    result.details = {
      title: raw.details.title,
      description: raw.details.description, // This will pass through both string and object formats
      counter: raw.details.counter,
      tab: raw.details.tab,
      targetElement: raw.details.target_element
        ? {
            type: raw.details.target_element.type,
            id: raw.details.target_element.id,
            childId: raw.details.target_element.child_id,
          }
        : undefined,
    };

    if (raw.details.items && raw.details.items.length > 0) {
      result.details.items = raw.details.items.map((item) => ({
        id: item.id,
        name: item.name,
        status: item.status,
        description: item.description, // This will pass through both string and object formats
        counter: item.counter,
        targetElement: item.target_element
          ? {
              type: item.target_element.type,
              id: item.target_element.id,
              childId: item.target_element.child_id,
            }
          : undefined,
      }));
    }
  }

  return result;
}

/**
 * Converts a client approval check to its raw format for API communication
 */
export function approvalCheckToRaw(check: IDocumentApprovalCheck): IRawDocumentApprovalCheck {
  const result: IRawDocumentApprovalCheck = {
    id: check.id,
    status: check.status,
    type: check.type,
  };

  if (check.details) {
    result.details = {
      title: check.details.title,
      description: check.details.description, // This will pass through both string and object formats
      counter: check.details.counter,
      tab: check.details.tab,
      target_element: check.details.targetElement
        ? {
            type: check.details.targetElement.type,
            id: check.details.targetElement.id,
            child_id: check.details.targetElement.childId,
          }
        : undefined,
    };

    if (check.details.items && check.details.items.length > 0) {
      result.details.items = check.details.items.map((item) => ({
        id: item.id,
        name: item.name,
        status: item.status,
        description: item.description, // This will pass through both string and object formats
        counter: item.counter,
        target_element: item.targetElement
          ? {
              type: item.targetElement.type,
              id: item.targetElement.id,
              child_id: item.targetElement.childId,
            }
          : undefined,
      }));
    }
  }

  return result;
}

function convertDocumentEntity(id: string, raw: IRawDocumentEntity): IDocumentEntity {
  let convertedValue: IDocumentEntityValue;
  if (raw.value && typeof raw.value === 'object' && 'complex' in raw.value) {
    const convertedComplex: Record<string, IDocumentEntity> = Object.entries(raw.value.complex).reduce(
      (acc, [key, entity]) => {
        acc[key] = convertDocumentEntity(key, entity);
        return acc;
      },
      {} as Record<string, IDocumentEntity>,
    );
    convertedValue = { complex: convertedComplex };
  } else {
    // For non-complex values, assume they’re just strings (or adjust if needed)
    convertedValue = raw.value as IDocumentEntityTextValue;
  }
  return {
    id: id,
    categoryId: raw.categoryId,
    confidence: raw.confidence,
    isChecked: raw.isChecked,
    isSuggestion: raw.isSuggestion,
    ocrConfidence: raw.ocrConfidence,
    pageNo: raw.pageNo,
    rawValue: raw.rawValue,
    source: raw.source,
    type: raw.type,
    value: convertedValue,
    valueLocations: raw.valueLocations.map(convertValueLocation),
  };
}

function convertValueLocation(raw: IRawDocumentValueLocation): IDocumentValueLocation {
  const item: IDocumentValueLocation = {
    x1: raw.x1,
    x2: raw.x2,
    y1: raw.y1,
    y2: raw.y2,
  };
  if (raw.pageNo) item.pageNo = raw.pageNo;
  return item;
}

function convertTopology(raw: IRawDocumentTopology): IDocumentTopology {
  return {
    contentModified: raw.content_modified,
    contentReady: raw.content_ready,
    extension: raw.extension,
    transformable: raw.transformable,
    name: raw.name,
    parts: Object.entries(raw.parts)
      .map(([key, part]) => convertPart(key, part))
      .sort((a, b) => {
        const firstUnarchivedPageA = a.pages.find((page) => !page.archived);
        const firstUnarchivedPageB = b.pages.find((page) => !page.archived);
        return firstUnarchivedPageA.bundlePageNo - firstUnarchivedPageB.bundlePageNo;
      }),
  };
}

function convertPart(id: string, raw: IRawDocumentPart): IDocumentPart {
  return {
    id: id,
    alternativeClassificationResults: camelcaseKeys(raw.alternative_classification_results, { deep: true }),
    archived: raw.archived,
    confidence: raw.confidence,
    contentModified: raw.content_modified,
    docSubtypeId: raw.doc_subtype_id,
    docTypeId: raw.doc_type_id,
    extension: raw.extension,
    name: raw.name,
    pages: raw.pages.map(convertPartPage),
    topologyType: raw.topology_type,
    transformable: raw.transformable,
    contentChecksum: raw.content_checksum,
    modifiedContentChecksum: raw.modified_content_checksum,
    altExtensions: raw.alt_extensions,
    contentId: raw.content_id,
    contentType: raw.content_type,
  };
}

function convertPartPage(raw: IRawDocumentPartPage): IDocumentPartPage {
  return {
    archived: raw.archived,
    bundlePageNo: raw.bundle_page_no,
    pageNo: raw.page_no,
    height: raw.height,
    width: raw.width,
  };
}

export function convertPartToRaw(part: IDocumentPart): IRawDocumentPart {
  return {
    alternative_classification_results: snakecaseKeys(part.alternativeClassificationResults ?? [], {
      deep: true,
    }),
    archived: part.archived,
    confidence: part.confidence,
    content_modified: part.contentModified,
    doc_subtype_id: part.docSubtypeId,
    doc_type_id: part.docTypeId,
    extension: part.extension,
    name: part.name,
    pages: part.pages.map(convertPartPageToRaw),
    topology_type: part.topologyType,
    transformable: part.transformable,
    alt_extensions: part.altExtensions,
    content_checksum: part.contentChecksum,
    modified_content_checksum: part.modifiedContentChecksum,
    content_id: part.contentId,
    content_type: part.contentType,
  };
}
function convertPartPageToRaw(partPage: IDocumentPartPage): IRawDocumentPartPage {
  return {
    archived: partPage.archived,
    bundle_page_no: partPage.bundlePageNo,
    page_no: partPage.pageNo,
    height: partPage.height,
    width: partPage.width,
  };
}

function convertWorkflowRun(raw: IRawDocumentWorkflowRun): IDocumentWorkflowRun {
  return {
    attempt: raw.attempt,
    correlationId: raw.correlation_id,
    params: raw.params,
    status: raw.status,
    timeFinished: raw.time_finished instanceof Timestamp ? raw.time_finished?.toDate() : null,
    timeProvisioned: raw.time_provisioned instanceof Timestamp ? raw.time_provisioned?.toDate() : null,
    timeStarted: raw.time_started instanceof Timestamp ? raw.time_started?.toDate() : null,
    workflowName: raw.workflow_name,
  };
}

/**
 * Converts a client entity to its raw format for API communication
 * Handles both regular entities and complex nested structures
 */
export function entityToRawPayload(entity: IDocumentEntity): any {
  // Strip the ID as it's typically used as a key/path parameter

  // Create a new object to avoid mutation
  const rawPayload: any = {
    id: entity.id, // Keep the ID for reference
    type: entity.type,
    value: null,
    confidence: entity.confidence,
    isChecked: entity.isChecked,
    isSuggestion: entity.isSuggestion,
    ocrConfidence: entity.ocrConfidence,
    source: entity.source,
    valueLocations: entity.valueLocations,
    rawValue: entity.rawValue,
    pageNo: entity.pageNo,
    categoryId: entity.categoryId,
  };

  // Handle complex values with nested entities
  if (entity.value && typeof entity.value === 'object' && 'complex' in entity.value) {
    const complexValue = entity.value as IDocumentEntityComplexValue;
    const rawComplex = {};

    // Convert each child entity in the complex value
    Object.entries(complexValue.complex).forEach(([childId, childEntity]) => {
      if (!childEntity) return;
      rawComplex[childId] = entityToRawPayload({ id: childId, ...childEntity });
    });

    // Replace with raw complex structure
    rawPayload.value = { complex: rawComplex };
  } else {
    rawPayload.value = entity.value as any;
  }
  return rawPayload;
}
