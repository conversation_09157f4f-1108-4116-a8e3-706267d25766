import React from 'react';
import { useParams } from 'react-router-dom';
import { useSelector } from '../shared/store/store';
import DocumentProvider from '../shared/providers/DocumentProvider';
import { useDocumentOperations } from '../shared/hooks/useDocumentOperations';
import { 
  selectActiveDocument, 
  selectActivePageNo,
  selectIsLoading,
  selectAllLoading
} from '../shared/store/documentSlice';

// Example component that uses the document state and operations
const DocumentContent: React.FC = () => {
  // Use selectors to access only the state you need
  const activeDocument = useSelector(selectActiveDocument);
  const activePageNo = useSelector(selectActivePageNo);
  const isLoading = useSelector(selectIsLoading);
  const allLoading = useSelector(selectAllLoading);
  
  // Get document operations
  const { 
    addEntity, 
    editEntity, 
    deleteEntity, 
    setActivePage,
    handleNavNext,
    handleNavPrev
  } = useDocumentOperations();
  
  if (allLoading) {
    return <div>Loading document...</div>;
  }
  
  if (!activeDocument) {
    return <div>No document found</div>;
  }
  
  return (
    <div>
      <h1>{activeDocument.name}</h1>
      
      <div className="document-navigation">
        <button onClick={() => handleNavPrev()}>Previous Document</button>
        <span>Page {activePageNo}</span>
        <button onClick={() => handleNavNext()}>Next Document</button>
      </div>
      
      <div className="document-actions">
        <button onClick={() => setActivePage(activePageNo - 1)}>Previous Page</button>
        <button onClick={() => setActivePage(activePageNo + 1)}>Next Page</button>
      </div>
      
      {/* Document content would go here */}
      
      <div className="entities-list">
        <h2>Entities</h2>
        {activeDocument.entities?.map(entity => (
          <div key={entity.id} className="entity-item">
            <span>{entity.type}: {entity.value?.toString()}</span>
            <button onClick={() => editEntity({ entityId: entity.id }, { value: 'Updated value' })}>
              Edit
            </button>
            <button onClick={() => deleteEntity({ entityId: entity.id })}>
              Delete
            </button>
          </div>
        ))}
        
        <button onClick={() => addEntity({
          type: 'text',
          value: 'New entity',
          pageNo: activePageNo,
          valueLocations: []
        })}>
          Add Entity
        </button>
      </div>
    </div>
  );
};

// Main document page component
const DocumentPage: React.FC = () => {
  // Get route parameters
  const { inboxId = '', documentId = '' } = useParams<{ inboxId: string; documentId: string }>();
  const historical = window.location.pathname.includes('/historical/');
  
  return (
    <DocumentProvider
      mainDocId={documentId}
      inboxId={inboxId}
      historical={historical}
    >
      <DocumentContent />
    </DocumentProvider>
  );
};

export default DocumentPage;
