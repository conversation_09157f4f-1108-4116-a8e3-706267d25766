@import "../../vars/_vars";


.wrapper {
  position: relative;
  width: 60px;
  margin-left: auto;

}


.inner {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 22px;
  padding: 0px 10px;
  cursor: pointer;
  border-radius: 5px;
  background: $light-gray;

  &__open {
    border: 1px solid #EEEEEE;
  }
}


.arrow {
  width: 16px;
  height: auto;
  margin-left: 6px;

}


.container {
  position: absolute;
  z-index: 100;
  top: 0;
  display: flex;
  flex-direction: column;
  width: 60px;
  padding: 5px;
  transition: transform 0.1s ease-in-out;
  transition-property: top, transform;
  transform: scale(0);
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: $light-gray;

  &__open {
    top: 26px;
    transform: scale(1);
  }
}


.dropdown_item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 30px;
  padding: 10px;
  cursor: pointer;
  opacity: 0.5;
  border-radius: 5px;

  &:hover {
    opacity: 1;
    background: $paperbox-blue--fade-extra;

  }

  &__active {
    opacity: 1;
  }

  .flag {
    width: 20px;

    svg {
      border-radius: 1px;

    }
  }

}


.flag {
  font-size: 14px;
  display: flex;
  width: 16px;
  height: auto;

  svg {
    transition: opacity 0.15s ease-in-out;
    transition-property: opacity;
    border-radius: 3px;
  }

}
