@import "src/shared/styles/vars/_vars";


.button {
  @include flex-center;
  font-family: $base-font;
  font-weight: 500;
  position: relative;
  flex-direction: row;
  height: 35px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  color: $white;
  border-radius: 5px;
  background: $paperbox-blue;

  &:hover:not(.disabled), &:focus:not(.disabled) {
    transition: box-shadow 0.2s ease-in-out;
    outline: none;
    box-shadow: 0 2px 8px rgba($paperbox-blue, 0.4);
  }

  &:active:not(.disabled) {
    background: #005DB3;
  }
}


.disabled {
  transition: all 0.2s ease-in-out;
  opacity: 0.6;
  color: white;
  background: rgba($dark-gray, 0.3);

  &:hover, &:focus {
    cursor: not-allowed;
    outline: none;
  }

  .icon {

    path {
      transition: fill 0.2s ease-in-out;
    }
  }
}


.icon {
  margin-top: -2px;
}


.icon_right {
  margin-left: 8px;

}


.icon_left {
  margin-right: 3px;
}


.hover {
  font-size: 13px;
  line-height: 1.5;
  position: absolute;
  z-index: 100;
  bottom: 45px;
  min-width: 50px;
  padding: 10px 12px;
  transition: opacity 0.1s ease-in-out, transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
  transform: translateY(20px) scale(0.8);
  pointer-events: none;
  opacity: 0;
  color: black;
  border-radius: 10px;
  background: white;
  box-shadow: $shadow-light;

  &__active {
    transform: translateY(0px) scale(1);
    opacity: 1;
  }

}
