// inbox-modal.module.scss

.uploadContainer {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

  &.dragging {
    border: 2px dashed #0085ff;
    background: #f9f9f9;
  }
}


.uploadHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  h2 {
    font-size: 1.5rem;
    margin: 0;
    color: #333;
  }
}


.closeIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  fill: #aaa;

  &:hover {
    fill: #333;
  }
}


.uploadArea {
  padding: 40px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  text-align: center;
  border: 2px dashed #ccc;
  border-radius: 8px;

  &.uploadAreaActive {
    border-color: #0085ff;
    background-color: #e9f5ff;
  }
}


.uploadIcon {
  width: 48px;
  height: 48px;
  margin-bottom: 10px;
  fill: #ccc;

  &.uploadIconActive {
    fill: #0085ff;
  }
}


.uploadButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  margin-bottom: 10px;
  cursor: pointer;
  border: none;
  border-radius: 50%;
  background-color: #0085ff;

  &.uploadButtonActive {
    background-color: #006bbd;
  }
}


.hiddenInput {
  display: none;
}


.instructionText {
  font-size: 1rem;
  margin: 10px 0;
  color: #666;

  &.instructionActive {
    font-weight: bold;
    color: #0085ff;
  }
}


.dragDropText {
  font-size: 0.9rem;
  color: #999;
}


.uploadBody {
  margin-top: 20px;
}


.tableWrapper {
  overflow-x: auto;
}


.tableHeader {
  display: grid;
  padding: 10px 0;
  border-bottom: 1px solid #ddd;
  grid-template-columns: 2fr 2fr 2fr 2fr 1fr;

  .tableCell {
    font-weight: bold;
    color: #333;
  }

  .cellStart {
    text-align: left;
  }

  .cellEnd {
    text-align: right;
  }
}


.fileRow {
  display: grid;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  grid-template-columns: 2fr 2fr 2fr 2fr 1fr;
}


.tableCell {
  font-size: 0.9rem;
  padding: 0 10px;
  color: #555;
}


.fileName {
  font-size: 0.95rem;
  padding: 0 10px;
  color: #555;
}


.actionCell {
  display: flex;
  align-items: center;
  justify-content: center;
}


.loadingRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
}


.loadingBarContainer {
  flex: 1;
  margin-right: 10px;
}


.errorIcon {
  width: 20px;
  height: 20px;
  cursor: pointer;
  fill: #e74c3c;
}


.loadingIndicator {
  display: flex;
  align-items: center;
}


.successIcon {
  width: 20px;
  height: 20px;
  fill: #2ecc71;
}


.deleteCell {
  cursor: pointer;

  &:hover {
    opacity: 0.7;
  }
}


.trashIcon {
  width: 20px;
  height: 20px;
  fill: #e74c3c;
}


.tagDropdown {
  display: flex;
  align-items: center;
  justify-content: center;
}


.uploadFooter {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20px;
}


.footerButton {
  padding: 10px 20px;
  cursor: pointer;
  color: #fff;
  border: none;
  border-radius: 4px;
  background-color: #0085ff;

  &:disabled {
    cursor: not-allowed;
    background-color: #ccc;
  }
}


.actionButtons {
  display: flex;
  align-items: center;
  gap: 10px;
}


.footerAdd {
  cursor: pointer;
  border: none;
  background: none;

  svg {
    width: 24px;
    height: 24px;
    fill: #0085ff;
  }
}
