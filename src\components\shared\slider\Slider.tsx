import React, { useMemo } from 'react';
import clsx from 'clsx';
import s from './slider.module.scss';

interface Props {
  value: number;
  max: number;
  min: number;
  onChange: (value: number) => void;
  hasInput?: boolean;
  inputPosition?: 'left' | 'right';
  disabled?: boolean;
}

const Slider: React.FC<Props> = ({
  value,
  max,
  min,
  onChange,
  inputPosition = 'right',
  hasInput = false,
  disabled = false,
}) => {
  const percentageValue = useMemo(() => {
    return (value / max) * 100;
  }, [value, max]);

  return (
    <div className={clsx(s.container, { [s.container__left]: inputPosition === 'left' })}>
      <input
        disabled={disabled}
        style={{
          background: `
                          linear-gradient(to right, ${disabled ? '#BFCAD9' : '#0085FF'}  0%, ${
            disabled ? '#BFCAD9' : '#0085FF'
          } ${percentageValue}%,  #d7dee8 ${percentageValue}%, #d7dee8 100%)`,
        }}
        id="threshold"
        className={s.slider}
        type="range"
        onChange={(e) => onChange(Number.parseInt(e.target.value))}
        value={value}
        max={max}
        min={min}
      />
      {hasInput && (
        <input
          disabled={disabled}
          max={max}
          min={min}
          value={value}
          onChange={(e) => onChange(Number.parseInt(e.target.value))}
          type="number"
          className={s.number}
        />
      )}
    </div>
  );
};

export default Slider;
