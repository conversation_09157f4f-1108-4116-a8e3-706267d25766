@import "../../vars/_vars";

.wrapper {
  @include flex-center;
  position: relative;
}

.list {
  position: absolute;
  z-index: 1000;
  top: -5px;
  right: 40px;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  height: auto;
  border-radius: 5px;
  background: transparent;
  box-shadow: 0 5px 10px rgba(0, 61, 82, 0.1);
}

.triangle {
  position: absolute;
  z-index: 1001;
  right: 26px;
  transform: rotateZ(90deg);
  filter: drop-shadow(0px -4px 2px rgba(0, 61, 82, 0.1));
}

.container {
  z-index: 99;
  display: flex;
  flex-direction: column;
  width: 380px;
  height: 78px;
  padding: 15px 18px;
  cursor: pointer;
  transition: all 0.25s ease-in-out;
  border-left: solid 3px transparent;
  border-radius: 3px;
  background: white;
  will-change: border, background;

  &:hover {
    cursor: pointer;
    border-left: solid 3px $paperbox-blue;
    background: #F7FBFF;
  }

  & + & {
    border-top: 1px solid $medium-gray;
  }
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 50%;
}

.bottom {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 50%;

}

.icon {
  cursor: pointer;
  outline: none;

  &__warning {
    color: $warning;
  }

  &__error {
    color: $error;
  }

  &__success {
    color: $success;
  }
}

.title {
  font-size: 14px;
  font-weight: 500;
  margin-right: auto;
  margin-left: 8px;

}

.time {
  font-size: 12px;
  opacity: 0.4;
}

.subtitle {
  font-size: 12px;

}

.button {
  font-size: 12px;
  font-weight: 500;
  color: $paperbox-blue;

}

:global {
  .notification-anim-enter {
    transform: scale(0.8);
    transform-origin: right;
    opacity: 0;
  }

  .notification-anim-enter-active {
    transition: opacity 0.150s ease-in-out, transform 0.150s ease-in-out;
    transform: scale(1);
    transform-origin: right;
    opacity: 1;
  }

  .notification-anim-exit {
    transform: scale(1);
    transform-origin: right;

    opacity: 1;
  }

  .notification-anim-exit-active {
    transition: opacity 0.150s ease-in-out, transform 0.150s ease-in-out;
    transform: scale(0.8);
    transform-origin: right;
    opacity: 0;
  }
}
