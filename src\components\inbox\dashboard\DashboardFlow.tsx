import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import { getUserToken, hexToRgb } from '@shared/helpers/helpers.ts';
import { api } from '@shared/store/setup/firebase-setup.ts';
import { useSelector } from '@shared/store/store.ts';
import { userInboxesSelector } from '@shared/store/userSlice.ts';
import sc from '@shared/styles/component/inbox/inbox-content.module.scss';
import s from '@shared/styles/component/inbox/inbox-dashboard.module.scss';
import { ReactComponent as InfoIcon } from '@svg/info-icon.svg';
import { ReactComponent as OverdueIcon } from '@svg/overdue.svg';
import { AxiosResponse } from 'axios';
import clsx from 'clsx';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Skeleton from 'react-loading-skeleton';

import { AnalyticsPeriod, ColoredInbox } from './InboxDashboard.tsx';

export interface FlowComponentHandle {
  fetchFunc: () => Promise<AxiosResponse<any, any>>;
}

interface Props {
  inboxes: ColoredInbox[];
  activeDateRange: AnalyticsPeriod;
}
const DashboardFlow = React.forwardRef<FlowComponentHandle, Props>(({ inboxes, activeDateRange }, ref) => {
  const [flowData, setFlowData] = useState<any>({});

  const { t } = useTranslation();
  const allInboxes = useSelector(userInboxesSelector);
  const demoMode = useSelector((state) => state.dashboard.demoMode);

  const fetchFunc = useCallback(async () => {
    const token = await getUserToken();
    if (!token) return;
    const params = {
      startDate: activeDateRange[0].toISOString(),
      endDate: activeDateRange[1].toISOString(),
      grouping: 'date',
      inboxes: allInboxes.length !== inboxes.length ? inboxes.map((e) => e.id) : null,
      demoMode: demoMode,
    };
    const headers = { Authorization: `Bearer ${token}` };
    return api
      .get(`${import.meta.env.VITE_PAPERBOX_ANALYTICS_URL}/dashboard/flow`, {
        params,
        headers,
      })
      .then((res) => {
        setFlowData(res.data);
        return res;
      });
  }, [demoMode, inboxes, activeDateRange, allInboxes]);

  React.useImperativeHandle(ref, () => ({
    fetchFunc,
  }));

  useEffect(() => {
    fetchFunc();
  }, [fetchFunc]);

  const extendedFlowdata = useMemo(() => {
    if (Object.keys(flowData).length === 0 || !inboxes) return {};

    const categories = ['active', 'inflow', 'outflow', 'automation', 'overdue'];
    const result = categories.reduce((acc, category) => {
      acc[category] = { total: flowData[category]?.total ?? 0, data: [] };
      return acc;
    }, {});

    inboxes.forEach((inbox) => {
      categories.forEach((category) => {
        const item = flowData[category].data.find((e) => e?.key === inbox?.id);
        const count = item?.count ?? 0;
        const percentage = item?.percentage ?? 0;

        result[category].data.push({
          inbox,
          count,
          percentage,
        });
      });
    });

    categories.forEach((category) => {
      const sortBy = category === 'automation' ? 'percentage' : 'count';
      result[category].data.sort((a, b) => b[sortBy] - a[sortBy]);
    });

    return result;
  }, [flowData, inboxes]);

  const [isShowingOverdue, setIsShowingOverdue] = useState(true);
  useEffect(() => {
    if (extendedFlowdata['overdue']?.total === 0 && isShowingOverdue) {
      setIsShowingOverdue(false);
    }
  }, [extendedFlowdata, isShowingOverdue]);

  return (
    <div className={sc.container}>
      <div className={sc.sub_header}>
        <div className={sc.sub_info}>
          <h2 className={sc.sub_title}> {t('home:dashboard.flowTitle')}</h2>

          <Tooltip
            position={'right'}
            alignment="start"
            content={
              <>
                <b
                  style={{
                    fontWeight: 500,
                    fontSize: 15,
                    marginBottom: 10,
                    display: 'block',
                  }}
                >
                  {t('home:dashboard.flowTitle')}
                </b>
                <p style={{ maxWidth: 350 }}>{t('home:dashboard.flowDescription')}</p>
              </>
            }
          >
            <div>
              <InfoIcon />
            </div>
          </Tooltip>
        </div>
      </div>
      <div className={s.card_grid}>
        <div className={s.card}>
          <div className={s.card_top}>
            {isShowingOverdue && extendedFlowdata['overdue']?.total > 0 ? (
              <>
                <span className={s.card_number}>{flowData.overdue?.total ?? '0'}</span>
                <span className={s.card_title}>{t('home:dashboard.totalOverdue')}</span>
              </>
            ) : (
              <>
                <span className={s.card_number}>{flowData.active?.total ?? '0'}</span>
                <span className={s.card_title}>{t('home:dashboard.totalPending')}</span>
              </>
            )}

            {extendedFlowdata['overdue']?.total > 0 && (
              <div style={{ marginLeft: 'auto', marginRight: 0 }}>
                <button
                  onClick={() => setIsShowingOverdue(!isShowingOverdue)}
                  className={clsx(s.card_top_toggle, {
                    [s.card_top_toggle__active]: isShowingOverdue,
                  })}
                >
                  <OverdueIcon />
                </button>
              </div>
            )}
          </div>
          {isShowingOverdue && extendedFlowdata['overdue']?.total > 0 ? (
            <div className={s.card_bottom}>
              {!extendedFlowdata['overdue']?.data ||
                (extendedFlowdata['overdue']?.data?.length === 0 && (
                  <Skeleton count={3} inline style={{ marginBottom: 5 }} className={s.card_bottom_item} />
                ))}
              {extendedFlowdata['overdue']?.data.map((it) => {
                if (it.count === 0) return null;
                return (
                  <div key={it.inbox.id} className={s.card_bottom_item}>
                    <div
                      className={clsx(s.card_bottom_item_counter, {
                        [s.card_bottom_item_counter__overdue]: it.count > 0,
                      })}
                      style={{
                        color: it.inbox.color,
                        background: `rgba(${hexToRgb(it.inbox.color)},0.15)`,
                      }}
                    >
                      <span>{it.count}</span>
                    </div>
                    <span>{it.inbox?.settings.name}</span>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className={s.card_bottom}>
              {!extendedFlowdata['active']?.data ||
                (extendedFlowdata['active']?.data?.length === 0 && (
                  <Skeleton count={3} inline style={{ marginBottom: 5 }} className={s.card_bottom_item} />
                ))}
              {extendedFlowdata['active']?.data.map((it) => {
                const overdueData = extendedFlowdata['overdue']?.data.find((o) => o.inbox.id === it.inbox.id);
                const isOverdue = overdueData.count > 0;

                return (
                  <div key={it.inbox.id} className={s.card_bottom_item}>
                    <div
                      className={clsx(s.card_bottom_item_counter, {
                        [s.card_bottom_item_counter__overdue]: isOverdue,
                      })}
                      style={{
                        color: it.inbox.color,
                        background: `rgba(${hexToRgb(it.inbox.color)},0.15)`,
                      }}
                    >
                      <span>{it.count}</span>
                    </div>
                    <span>{it.inbox?.settings.name}</span>
                  </div>
                );
              })}
            </div>
          )}
        </div>
        <div className={s.seperator} />
        <div className={s.card}>
          <div className={s.card_top}>
            <span className={s.card_number}>{extendedFlowdata['inflow']?.total ?? '0'}</span>
            <span className={s.card_title}>{t('home:dashboard.totalInflow')}</span>
          </div>
          <div className={s.card_bottom}>
            {!extendedFlowdata['inflow']?.data ||
              (extendedFlowdata['inflow']?.data?.length === 0 && (
                <Skeleton count={3} inline style={{ marginBottom: 5 }} className={s.card_bottom_item} />
              ))}
            {extendedFlowdata['inflow']?.data.map((it) => {
              return (
                <div key={it.inbox.id} className={s.card_bottom_item}>
                  <div
                    className={s.card_bottom_item_counter}
                    style={{
                      color: it.inbox.color,
                      background: `rgba(${hexToRgb(it.inbox.color)},0.15)`,
                    }}
                  >
                    {it.count}
                  </div>
                  <span>{it.inbox?.settings.name}</span>
                </div>
              );
            })}
          </div>
        </div>
        <div className={s.card}>
          <div className={s.card_top}>
            <span className={s.card_number}>{extendedFlowdata['outflow']?.total ?? '0'}</span>
            <span className={s.card_title}>{t('home:dashboard.totalOutflow')}</span>
          </div>
          <div className={s.card_bottom}>
            {!extendedFlowdata['outflow']?.data ||
              (extendedFlowdata['outflow']?.data?.length === 0 && (
                <Skeleton count={3} inline style={{ marginBottom: 5 }} className={s.card_bottom_item} />
              ))}
            {extendedFlowdata['outflow']?.data.map((it) => {
              return (
                <div key={it.inbox.id} className={s.card_bottom_item}>
                  <div
                    className={s.card_bottom_item_counter}
                    style={{
                      color: it.inbox.color,
                      background: `rgba(${hexToRgb(it.inbox.color)},0.15)`,
                    }}
                  >
                    {it.count}
                  </div>
                  <span>{it.inbox?.settings.name}</span>
                </div>
              );
            })}
          </div>
        </div>

        <div className={s.card}>
          <div className={s.card_top}>
            <span className={s.card_number}>
              {extendedFlowdata['automation']?.total
                ? `${(extendedFlowdata['automation']?.total * 100).toFixed(1)}%`
                : '0'}
            </span>
            <span className={s.card_title}>{t('home:dashboard.totalAutomation')}</span>
            <div style={{ marginLeft: 'auto', marginRight: 0 }}>
              <Tooltip
                position={'left'}
                alignment="start"
                content={
                  <>
                    <b
                      style={{
                        fontWeight: 500,
                        fontSize: 15,
                        marginBottom: 10,
                        display: 'block',
                      }}
                    >
                      {t('home:dashboard.totalAutomation')}
                    </b>
                    <p style={{ minWidth: 320, maxWidth: 320 }}>{t('home:dashboard.automationTooltip')}</p>
                  </>
                }
              >
                <div className={clsx(s.card_top_info)}>
                  <InfoIcon />
                </div>
              </Tooltip>
            </div>
          </div>
          <div className={s.card_bottom}>
            {!extendedFlowdata['automation']?.data ||
              (extendedFlowdata['automation']?.data?.length === 0 && (
                <Skeleton count={3} inline style={{ marginBottom: 5 }} className={s.card_bottom_item} />
              ))}
            {extendedFlowdata['automation']?.data.map((it) => {
              return (
                <div key={it.inbox.id} className={s.card_bottom_item}>
                  <div
                    className={s.card_bottom_item_counter}
                    style={{
                      color: it.inbox.color,
                      background: `rgba(${hexToRgb(it.inbox.color)},0.15)`,
                    }}
                  >{`${(it.percentage * 100).toFixed(1)}%`}</div>
                  <span>{it.inbox?.settings.name}</span>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
});

export default DashboardFlow;
