import React, { CSSProperties } from 'react';
import clsx from 'clsx';
import s from './cube-loader.module.scss';

interface Props {
  css?: CSSProperties;
  size?: number;
}

const CubeLoader: React.FC<Props> = ({ css, size }) => {
  return (
    <div
      className={s.sk_cube_grid}
      style={{ ...css, '--cube-size': `${size ? size : 13}px` } as React.CSSProperties}
    >
      <div className={clsx(s.sk_cube, s.sk_cube1)} />
      <div className={clsx(s.sk_cube, s.sk_cube2)} />
      <div className={clsx(s.sk_cube, s.sk_cube3)} />
      <div className={clsx(s.sk_cube, s.sk_cube4)} />
      <div className={clsx(s.sk_cube, s.sk_cube5)} />
      <div className={clsx(s.sk_cube, s.sk_cube6)} />
      <div className={clsx(s.sk_cube, s.sk_cube7)} />
      <div className={clsx(s.sk_cube, s.sk_cube8)} />
      <div className={clsx(s.sk_cube, s.sk_cube9)} />
    </div>
  );
};

export default CubeLoader;
