import AdminCharSelector from '@components/admin/components/AdminCharSelector.tsx';
import { DropdownOption } from '@components/shared/dropdown/StyledSelect.tsx';
import StyledSelect from '@components/shared/dropdown/StyledSelect.tsx';
import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import se from '@shared/styles/component/admin/admin-masterdata.module.scss';
import s from '@shared/styles/component/admin/admin-section.module.scss';
import { ReactComponent as InfoIcon } from '@svg/error-icon.svg';
import { ReactComponent as PlusIcon } from '@svg/plus-icon.svg';
import { ReactComponent as SearchIcon } from '@svg/search-icon.svg';
import { ReactComponent as TrashIcon } from '@svg/trash-icon-alt.svg';
import clsx from 'clsx';
import React, { useEffect, useRef } from 'react';

export interface ColumnDefinition {
  key: string;
  label?: string; // Optional header label
  tooltip?: string; // Optional tooltip
  width?: string; // CSS width value (e.g., '120px', 'minmax(150px, 1fr)')
  align?: 'left' | 'center' | 'right';
  type: 'text' | 'dropdown' | 'toggle' | 'button' | 'chars-filter' | 'title';
  options?: DropdownOption[]; // For dropdown type
  placeholder?: string; // For text type
  hideHeader?: boolean; // Hide header for this column
}

export interface RowData {
  id: string;
  level?: number; // For nested indentation
  [key: string]: any;
}

interface Props {
  title?: string; // Optional title
  description?: string; // Optional description
  columns: ColumnDefinition[];
  rows: RowData[];
  onRowChange: (rowId: string, field: string, value: any) => void;
  onRowDelete?: (rowId: string) => void;
  onAddRow?: () => void; // Optional add functionality
  addButtonText?: string; // Optional add button text
  showHeaders?: boolean; // Control header visibility
  className?: string; // Additional CSS classes
}

const DynamicMappingTable: React.FC<Props> = ({
  title,
  description,
  columns,
  rows,
  onRowChange,
  onRowDelete,
  onAddRow,
  addButtonText,
  showHeaders = true,
  className,
}) => {
  const tableRef = useRef<HTMLDivElement>(null);

  // Dynamically set grid template columns based on column definitions
  useEffect(() => {
    if (tableRef.current) {
      const gridColumns = columns
        .filter(() => showHeaders)
        .map((col) => {
          if (col.width) {
            return col.width;
          }
          // Default widths based on column type
          switch (col.type) {
            case 'toggle':
            case 'button':
              return '60px';
            case 'dropdown':
              return '140px';
            case 'chars-filter':
              return 'minmax(120px, 180px)';
            case 'text':
            default:
              return 'minmax(120px, 1fr)';
          }
        })
        .join(' ');

      tableRef.current.style.gridTemplateColumns = gridColumns;
    }
  }, [columns, showHeaders]);

  const renderCellContent = (column: ColumnDefinition, row: RowData) => {
    const value = row[column.key];
    const shouldWrapWithTooltip = column.tooltip && (column.hideHeader || !column.label);

    const wrapWithTooltip = (content: React.ReactNode) => {
      if (!shouldWrapWithTooltip) return content;

      return (
        <Tooltip
          position={'top'}
          tooltipStyle={{ maxWidth: 300 }}
          content={
            <div>
              <b>{column.label || 'Field'}</b> <p>{column.tooltip}</p>
            </div>
          }
        >
          {content as React.ReactElement}
        </Tooltip>
      );
    };

    switch (column.type) {
      case 'text': {
        const textInput = (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => onRowChange(row.id, column.key, e.target.value)}
            className={s.input}
            style={{
              backgroundColor: 'white',
              fontSize: '13px',
              height: '32px',
              width: '100%',
            }}
            placeholder={column.placeholder}
          />
        );
        return wrapWithTooltip(textInput);
      }

      case 'dropdown': {
        const dropdown = (
          <StyledSelect
            style={{ borderColor: '#EEEEEE !important' }}
            value={column.options?.find((opt) => opt.value === value)}
            onChange={(option) => onRowChange(row.id, column.key, option.value)}
            options={column.options || []}
          />
        );
        return wrapWithTooltip(dropdown);
      }

      case 'toggle': {
        const toggle = (
          <div className={se.switcher}>
            <button
              type="button"
              onClick={() => onRowChange(row.id, column.key, !value)}
              className={clsx(se.switcher_option, {
                [se.switcher_option__active]: value,
              })}
            >
              <SearchIcon />
            </button>
          </div>
        );
        return wrapWithTooltip(toggle);
      }

      case 'chars-filter': {
        const charList = Array.isArray(value) ? value : value ? value.split(',').filter((c) => c.trim()) : [];
        const charsFilter = (
          <div className={se.switcher} style={{ padding: '0 12px' }}>
            <AdminCharSelector
              charList={charList}
              onChange={(chars) => onRowChange(row.id, column.key, chars)}
            />
          </div>
        );
        return wrapWithTooltip(charsFilter);
      }

      case 'button': {
        const button = (
          <div className={se.switcher}>
            <button
              type="button"
              onClick={() => onRowDelete(row.id)}
              className={clsx(se.switcher_option, se.switcher_option__delete)}
            >
              <TrashIcon />
            </button>
          </div>
        );
        return wrapWithTooltip(button);
      }
      case 'title':
        return <span style={{ fontSize: 16, color: 'rgb(137, 139, 153)' }}>{value}</span>;

      default:
        return <span>{value}</span>;
    }
  };

  const renderCell = (column: ColumnDefinition, row: RowData) => {
    const visibleColumns = columns.filter((col) => !col.hideHeader || showHeaders);
    const isFirstColumn = visibleColumns[0]?.key === column.key;
    const level = row.level || 0;

    return (
      <div
        key={`${row.id}-${column.key}`}
        className={clsx(se.mapping_row_item, {
          [se.mapping_row_item__center]: column.align === 'center',
        })}
        style={{
          paddingLeft: isFirstColumn ? level * 20 : undefined,
          color: level > 0 && isFirstColumn ? '#898B99' : 'inherit',
          display: 'flex',
          alignItems: 'center',
          justifyContent: column.align === 'center' ? 'center' : 'flex-start',
        }}
      >
        {renderCellContent(column, row)}
      </div>
    );
  };

  return (
    <div className={clsx(s.item, s.item__vertical, className)}>
      {(title || description) && (
        <div className={s.item_text}>
          {title && <h4>{title}</h4>}
          {description && <p>{description}</p>}
        </div>
      )}
      <div className={clsx(s.item_action, se.mapping)}>
        <div className={se.mapping_rows} ref={tableRef}>
          {/* Header Row */}
          {showHeaders &&
            columns.map((column) => {
              if (column.hideHeader) return <div />;
              return (
                <div
                  key={`header-${column.key}`}
                  className={clsx(se.mapping_row_header, {
                    [se.mapping_row_header__center]: column.align === 'center',
                  })}
                >
                  <span>{column.label || ''}</span>
                  {column.tooltip && (
                    <Tooltip content={column.tooltip}>
                      <div>
                        <InfoIcon />
                      </div>
                    </Tooltip>
                  )}
                </div>
              );
            })}

          {/* Data Rows */}
          {rows.map((row) =>
            columns.filter((col) => !col.hideHeader || showHeaders).map((column) => renderCell(column, row)),
          )}
        </div>

        {onAddRow && addButtonText && (
          <button type="button" onClick={onAddRow} className={s.top_button} style={{ marginTop: '16px' }}>
            <PlusIcon /> {addButtonText}
          </button>
        )}
      </div>
    </div>
  );
};

export default DynamicMappingTable;
