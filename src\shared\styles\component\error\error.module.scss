@import "src/shared/styles/vars/_vars";

.container {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  min-height: 100vh;
  background: $light-gray;

  .wrapper {

    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 50%;
    max-width: 675px;
    height: 100%;
    min-height: 50vh;
    padding: 60px 30px 30px 30px;
    border-radius: 10px;
    background: $white;
    box-shadow: 0 4px 8px rgba(13, 21, 33, 0.2);

    svg:first-of-type {
      width: 200px;
    }

    svg {
      width: 450px;
      height: auto;
      margin-bottom: 75px;
    }

    h2 {
      font-size: 20px;
      line-height: 32px;
      width: 570px;
      margin-bottom: 45px;
      text-align: center;
    }

    b {
      font-size: 22px;
      font-weight: 700;
    }
  }

}

.error_container {
  overflow: auto;
  overflow-x: hidden;
  max-height: 200px;
  margin-top: 50px;
  padding: 15px;
  text-align: left;
  border-radius: 5px;
  background: $light-gray;

  p {
    font-size: 14px;
    white-space: normal;
    word-wrap: anywhere;
  }

  &__expanded {
    max-height: 500px;
  }
}
