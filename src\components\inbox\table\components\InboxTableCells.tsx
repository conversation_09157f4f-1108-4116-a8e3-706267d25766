import ApprovalChecks from '@components/shared/approval-checks/ApprovalChecks.tsx';
import Checkbox from '@components/shared/checkbox/Checkbox.tsx';
import Tooltip from '@components/shared/tooltip/Tooltip.tsx';
import { IDocumentEnriched } from '@shared/helpers/converters/document.ts';
import { hexToRgb } from '@shared/helpers/helpers.ts';
import s from '@shared/styles/component/inbox/inbox-table.module.scss';
import { ReactComponent as InfoIcon } from '@svg/error-icon.svg';
import { ReactComponent as LockIcon } from '@svg/lock-icon.svg';
import { ReactComponent as OverdueIcon } from '@svg/overdue.svg';
import { Pulsar } from '@uiball/loaders';

import React from 'react';
import { useTranslation } from 'react-i18next';

interface LoadingCellProps {
  width: string;
}

export const LoadingCell: React.FC<LoadingCellProps> = ({ width }) => {
  return <div className={s.loading} style={{ maxWidth: width }} />;
};

interface LockCellProps {
  isSelected: boolean;
  row: { original: IDocumentEnriched; index: number };
  loading: boolean;
  checkIfLocked: (id: string) => any;
  handleRowCheck: (rowIndex: number, rowData: IDocumentEnriched) => void; //handleRowCheck
}

export const LockCell: React.FC<LockCellProps> = ({
  isSelected,
  row,
  loading,
  checkIfLocked,
  handleRowCheck,
}) => {
  if (loading || !row.original) return <div className={s.loading} style={{ height: 18, width: 18 }} />;

  const locker = checkIfLocked(row.original.id);
  if (locker != null) {
    return (
      <Tooltip content={locker.email} position={'right'}>
        <div>
          <LockIcon className={s.lock} />
        </div>
      </Tooltip>
    );
  }
  return (
    <>
      <div>
        {row.original.processed ? (
          <Checkbox
            onClick={(event) => {
              event.preventDefault();
              event.stopPropagation();
              handleRowCheck(row.index, row.original);
            }}
            checked={isSelected}
          />
        ) : (
          <div />
        )}
      </div>
    </>
  );
};
interface StandardCellProps {
  value: any;
  row: { original: IDocumentEnriched };
  loading: boolean;
  checkIfLocked: (id: string) => any;
}

//
// if (loading) {
//   return <div className={s.loading} style={{ maxWidth: '60%' }} />;
// }
//
// const locker = checkIfLocked(val.row.original.id);
// const references = val.row.original.nMutations;
// const notes = val.row.original.notes;
//
// return (
//     <div data-testid={'table-row-name'} className={s.name_wrapper}>
//               <span className={s.name} style={locker ? { opacity: 0.5 } : {}}>
//                 {val.value}
//               </span>
//
//       {references > 0 && <div className={s.copy_counter}>{references + 1}</div>}
//       {notes?.length > 0 && (
//           <Tooltip
//               lightTheme
//               position={'right'}
//               delay={0}
//               content={`${notes.length} ${notes.length > 1 ? 'Notes' : 'Note'}`}
//           >
//             <div className={s.notes_wrapper}>
//               <div className={s.notes_pip} />
//             </div>
//           </Tooltip>
//       )}
//     </div>
// );

export const NameCell: React.FC<StandardCellProps> = ({ value, row, loading, checkIfLocked }) => {
  if (loading || !row.original) return <LoadingCell width={'60%'} />;

  const locker = checkIfLocked(row.original.id);
  const references = row.original.nMutations;
  const workflowStatus = row.original.latestWorkflowRun.status;
  const isErrorStatus = workflowStatus === 'FAILED' || workflowStatus === 'DEAD';
  const isInProgress = !['FINISHED', 'FAILED', 'DEAD'].includes(workflowStatus);
  const ageThresholdDays = row.original.docTypeDetails?.ageThreshold ?? 0;
  const overdueDate = row.original.uploadTime.getTime() + ageThresholdDays * 24 * 60 * 60 * 1000;
  const isOverdue = new Date().getTime() > overdueDate;
  const isHistorical = row.original.action;
  const { t } = useTranslation();

  return (
    <div data-testid="table-row-name" className={s.name_wrapper}>
      <span className={s.name} style={{ opacity: locker ? 0.5 : 1 }}>
        {value}
      </span>

      {references > 0 && <div className={s.copy_counter}>{references + 1}</div>}
      {isOverdue && !isHistorical && (
        <Tooltip content={t('home:table.overdue')} position={'right'}>
          <div className={s.overdue_icon}>
            <OverdueIcon />
          </div>
        </Tooltip>
      )}
      {isInProgress && (
        <div className={s.loader}>
          <Pulsar size={12} color="#0085FF" />
        </div>
      )}
      {isErrorStatus && (
        <div className={s.loader}>
          <Pulsar size={12} color="#FF5555" />
        </div>
      )}
    </div>
  );
};

export const DateCell: React.FC<StandardCellProps> = ({ value, row, loading, checkIfLocked }) => {
  if (loading || !row.original) return <LoadingCell width={'50%'} />;

  const locker = checkIfLocked(row.original.id);
  return (
    <div style={locker ? { opacity: 0.5 } : {}}>
      {new Date(value).toLocaleString('nl-BE', {
        month: 'short',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })}
    </div>
  );
};

export const DocTypeCell: React.FC<StandardCellProps> = ({ row, loading, checkIfLocked }) => {
  if (loading || !row.original) return <LoadingCell width={'50%'} />;

  const doctypeDetails = row.original.docTypeDetails;
  const locker = checkIfLocked(row.original.id);
  let subType;

  if (row.original.docSubtypeId && doctypeDetails) {
    const subTypeDetails = doctypeDetails.subtypes.find((st) => st.id === row.original.docSubtypeId);
    if (subTypeDetails) subType = subTypeDetails.name;
  }
  if (!doctypeDetails) return null;
  return (
    <div
      style={
        locker ? { opacity: 0.5, maxHeight: 43, overflow: 'hidden' } : { maxHeight: 43, overflow: 'hidden' }
      }
    >
      {doctypeDetails.name}
      {subType && `/${subType}`}
    </div>
  );
};

export const ActorCell: React.FC<StandardCellProps> = ({ value, row, loading, checkIfLocked }) => {
  if (loading || !row.original) return <LoadingCell width={'50%'} />;

  const locker = checkIfLocked(row.original.id);
  const editor = value?.actorEmail;
  if (!editor) return null;
  return (
    <div className={s.label_wrapper} style={locker ? { opacity: 0.5 } : {}}>
      {editor}
    </div>
  );
};

export const ActionCell: React.FC<StandardCellProps> = ({ value, row, loading, checkIfLocked }) => {
  if (loading || !row.original) return <LoadingCell width={'50%'} />;

  const locker = checkIfLocked(row.original.id);
  const editor = value?.toLocaleString();
  if (!editor) return null;

  return (
    <div className={s.label_wrapper} style={locker ? { opacity: 0.5 } : {}}>
      {editor}
    </div>
  );
};

interface UserUpdateCellProps extends StandardCellProps {
  getUserName: (id: string) => string;
}

export const UserUpdateCell: React.FC<UserUpdateCellProps> = ({
  value,
  row,
  loading,
  checkIfLocked,
  getUserName,
}) => {
  if (loading || !row.original) return <LoadingCell width={'50%'} />;

  const locker = checkIfLocked(row.original.id);
  const editor = getUserName(row.original.lastUserUpdateBy);
  if (!editor) return null;
  return (
    <div className={s.label_wrapper} style={locker ? { opacity: 0.5 } : {}}>
      <>
        {new Date(value).toLocaleString('nl-BE', {
          month: 'short',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
        })}
        <Tooltip lightTheme content={editor}>
          <div>
            <InfoIcon className={s.info_icon} />
          </div>
        </Tooltip>
      </>
    </div>
  );
};
export const TagTypeCell: React.FC<StandardCellProps> = ({ value, row, loading }) => {
  if (loading || !row.original) return <LoadingCell width={'75'} />;

  if (!value || !row.original.tagDetails) return null;
  const details = row.original.tagDetails;
  const color = details?.color ?? '#EEEEEE';
  const bgColor = hexToRgb(color);
  return (
    <div className={s.label_wrapper}>
      <div
        style={{
          backgroundColor: `rgba(${bgColor},0.1)`,
          color: color,
        }}
        className={s.label}
      >
        {details?.name}
      </div>
    </div>
  );
};

export const ActionTypeCell: React.FC<StandardCellProps> = ({ value, loading }) => {
  const { t } = useTranslation();
  if (loading) return <LoadingCell width={'75'} />;
  let color = '#EEEEEE';

  if (value === 'approve') color = '#0085FF';
  if (value === 'delete') color = '#FF5555';
  if (value === 'bounce') color = '#FCBF19';
  const bgColor = hexToRgb(color);
  return (
    <div className={s.label_wrapper}>
      <div style={{ backgroundColor: `rgba(${bgColor},0.1)`, color: color }} className={s.label}>
        {t(`document:actions.${value}`)}
      </div>
    </div>
  );
};
export const ConfidenceCell: React.FC<StandardCellProps> = ({ value, row, loading, checkIfLocked }) => {
  if (loading || !row.original) return <LoadingCell width={'25'} />;

  const locker = checkIfLocked(row.original.id);
  const { approvalThreshold } = row.original.docTypeDetails ?? {};

  return (
    <div
      style={locker ? { opacity: 0.5 } : {}}
      className={`${value >= (approvalThreshold / 100) ? s.confidence__high : s.confidence__low}`}
    >
      {(Math.floor(value * 1000.0) / 10).toFixed(1)}
    </div>
  );
};
interface ApprovalChecksCellProps extends StandardCellProps {
  isInitialChecks?: boolean;
}

export const ApprovalChecksCell: React.FC<ApprovalChecksCellProps> = ({ isInitialChecks, row, loading }) => {
  if (loading || !row.original) return <LoadingCell width={'25'} />;
  return (
    <div className={s.checks}>
      <ApprovalChecks isInitialChecks={isInitialChecks} document={row.original} position={'left'} />
    </div>
  );
};
