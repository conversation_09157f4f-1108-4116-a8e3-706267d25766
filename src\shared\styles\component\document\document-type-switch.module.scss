@import "src/shared/styles/vars/_vars";



.label {
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  color: #666A72;

  svg {
    width: auto;
    height: 12px;
    margin-top: 3px;
    margin-left: 8px;
  }
}


.group {
  transition: all 0.2s ease-in-out;

  & + & {
    margin-top: 26px;
  }

  &__horizontal {
    display: flex;
    justify-content: space-between;
  }
}


.group__hidden {
  overflow: hidden;
  max-height: 0;
  margin-top: 0 !important;
  transition: all 0.25s ease-in-out;
  opacity: 0;
}



.content {
  //max-width: 350px;
  padding: 30px 0px 0px 0px;

}




.input {
  font-family: $base-font;
  font-size: 16px;
  width: 100%;
  height: 70px;
  padding: 6px 6px;
  resize: none;
  border: 1px solid #EEEEEE;
  border-radius: 5px;
  background: $white;

  &:hover, &:focus {
    transition: 0.2s box-shadow ease-in-out;
    border: 1px solid #EEEEEE;
    outline: none;

    box-shadow: 0 0 1px 2px $paperbox-blue--fade;
  }
}

