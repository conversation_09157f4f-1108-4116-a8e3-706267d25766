import { sleep } from '@shared/helpers/helpers.ts';
import { useSelector } from '@shared/store/store.ts';
import s from '@shared/styles/component/admin/admin.module.scss';
import clsx from 'clsx';
import React, { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import Skeleton from 'react-loading-skeleton';
import { NavLink, Outlet, useNavigate, useParams } from 'react-router';

interface Props {}

const AdminDatasources: React.FC<Props> = () => {
  const datasources = useSelector((state) => state.admin.datasources) ?? [];
  const datasourceRef = useRef();
  const { datasourceId } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();

  useEffect(() => {
    if (datasources.length > 0 && datasourceId !== 'new') {
      if (datasourceRef.current) {
        const activeDatasourceItem = datasourceRef.current as HTMLDivElement;
        sleep(1).then(() => {
          const sidebar = activeDatasourceItem.closest('nav') as HTMLElement;
          if (sidebar) {
            sidebar.scrollTo({
              top: activeDatasourceItem.offsetTop - sidebar.offsetTop,
              behavior: 'smooth',
            });
          }
        });
      }
    }
  }, [datasources, datasourceId]);

  useEffect(() => {
    let timeout;
    if (!datasourceId && datasources && datasources.length > 0) {
      navigate(`${datasources[0].id}`);
    } else if (datasourceId && datasourceId !== 'new' && datasources && datasources.length > 0) {
      const con = datasources.find((datasource) => datasource.id === datasourceId);
      if (!con) {
        navigate('/admin/datasources/new');
      }
    }
    return () => {
      clearTimeout(timeout);
    };
  }, [datasourceId, navigate, datasources]);

  return (
    <div className={s.subcontent}>
      <div className={s.subsidebar}>
        <div className={s.header}> Datasource</div>
        <NavLink data-testid={'datasource-add'} to={'new'} className={s.add}>
          + {t('admin:connectors.addConnector')}
        </NavLink>
        <nav className={clsx(s.section)}>
          {!datasources && (
            <Skeleton
              count={5}
              height={20}
              width={'calc(100% - 32px '}
              style={{ marginBottom: 28, marginLeft: 16 }}
            />
          )}
          {datasources?.map((datasource) => (
            <NavLink
              data-testid={'datasource-item'}
              ref={datasourceId === datasource.id ? datasourceRef : null}
              key={datasource.id}
              to={`${datasource.id}`}
              className={({ isActive }) => clsx(s.item, { [s.active]: isActive })}
            >
              {datasource.name}
            </NavLink>
          ))}
        </nav>
      </div>
      <Outlet />
    </div>
  );
};

export default AdminDatasources;
