import FormBodyHeader from '@components/admin/components//form/FormBodyHeader.tsx';
import FormInputField from '@components/admin/components//form/FormInputField.tsx';
import FormMultiSelectField from '@components/admin/components//form/FormMultiSelectField.tsx';
import FormRequestField from '@components/admin/components//form/FormRequestField.tsx';
import FormSection from '@components/admin/components//form/FormSection.tsx';
import { MultiSelectDetailsType } from '@components/admin/components/AdminMultiSelectDialog.tsx';
import AdminCCSWebhookEdit from '@components/admin/routes/webhooks/details/AdminCCSWebhookEdit.tsx';
import ConfirmationDialog from '@components/shared/confirmation-dialog/ConfirmationDialog.tsx';
import { DropdownOption } from '@components/shared/dropdown/StyledSelect.tsx';
import { IClientConnector, createEmptyRow } from '@shared/helpers/converters/connector.ts';
import { IClientDocType } from '@shared/helpers/converters/doctype.ts';
import {
  IClientEndpointBrio,
  IClientEndpointCCS,
  IClientEndpointHTTP,
  endpointClientToRaw,
} from '@shared/helpers/converters/endpoint.ts';
import {
  IClientWebhook,
  IClientWebhookHTTP,
  webhookClientToRaw,
} from '@shared/helpers/converters/webhook.ts';
import { errorMap } from '@shared/helpers/helpers.ts';
import useChangeTracker, { ChangeSaveCallback } from '@shared/hooks/useChangeTracker.tsx';
import { useModal } from '@shared/hooks/useModal.tsx';
import {
  clientConnectorsSelector,
  clientWebhookSelector,
  deleteEndpoint,
  deleteWebhook,
  getDocTypesForInboxStatic,
  getMasterdataTablesForInboxStatic,
  patchEndpoint,
  patchWebhook,
  postEndpoint,
  postWebhook,
} from '@shared/store/adminSlice.ts';
import { useSelector } from '@shared/store/store.ts';
import se from '@shared/styles/component/admin/admin-section.module.scss';
import { RaceBy } from '@uiball/loaders';
import { cloneDeep, isEqual } from 'lodash';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router';
import AdminBrioWebhookEdit from './AdminBrioWebhookEdit.tsx';

const AdminWebhookEdit: React.FC = () => {
  const clientWebhooks = useSelector(clientWebhookSelector);
  const clientConnectors = useSelector(clientConnectorsSelector);
  const inboxes = useSelector((state) => state.admin.inboxes);
  const tenantId = useSelector((state) => state.tenant.tenantId);
  const webhooks = useSelector((state) => state.admin.webhooks);
  const sortedWebhooks = [...(webhooks ?? [])].sort((a, b) => a.name.localeCompare(b.name));

  const [activeBrioInboxId, setActiveBrioInboxId] = useState(null);
  const [activeCCSInboxId, setActiveCCSInboxId] = useState(null);
  const { webhookId } = useParams();
  const { t } = useTranslation();
  const { showDialog } = useModal();
  const navigate = useNavigate();

  const [tableData, setTableData] =
    useState<{ inboxId: string; data: { id: string; name: string }[] }[]>(null);

  const [docTypes, setDocTypes] = useState<{ inboxId: string; data: IClientDocType[] }[]>(null);

  const addRow = (type: 'headers' | 'payload' | 'params') => {
    setState((prevState: IClientWebhook) => {
      const copy = cloneDeep(prevState);
      const item = copy.endpoint[type] ?? [];
      copy.endpoint[type] = [...item, createEmptyRow()];
      return copy;
    });
  };

  const deleteRow = (index: number, type: 'headers' | 'payload' | 'params') => {
    setState((fs) => {
      const copy = cloneDeep(fs);
      copy.endpoint[type][index] = { ...copy.endpoint[type][index], markedForDelete: true };
      return copy;
    });
  };

  const editRow = (
    index: number,
    target: 'key' | 'value' | 'valueType' | 'lock' | 'all',
    value: any,
    type: 'headers' | 'payload' | 'params',
  ) => {
    setState((fs) => {
      const copy = cloneDeep(fs);

      // Simplified the item assignment by utilizing the isAuth flag directly in the item assignment.
      const items = copy.endpoint[type] ?? [];
      const item = items[index];

      // Switch statement used to make the code more readable and easier to manage in the future.
      switch (target) {
        case 'value':
          item.value.content = value;
          break;
        case 'key': {
          const newValue = value.replaceAll(' ', '-');
          if (item.error && item.error.key === target && newValue.length >= item.key.length) {
            return copy;
          }
          item.error = undefined;
          // Nested objects validation only when the type is 'payload'.
          if (type === 'payload' && newValue.includes('__')) {
            validateNestedObjects(items, newValue, item);
          }
          item.key = newValue;
          break;
        }
        case 'valueType':
          item.value.type = value;
          item.tempLocked = value === '@PB_SECRET';
          if (value !== '@PB_SECRET') {
            item.locked = false;
          }
          break;
        case 'all':
          items[index] = value;
          break;
        default:
          item.tempLocked = value;
      }

      return copy;
    });
  };
  // Externalized validation logic to its own function for better readability and maintainability.
  const validateNestedObjects = (items: any[], value: string, item: any) => {
    const activeItems = items.filter((e) => !e.markedForDelete);
    const currentKeyGroups: string[] = value.split(/__/g);
    const sameParentKeyItems = activeItems.filter((e) => e?.key.includes(currentKeyGroups[0]));

    sameParentKeyItems.forEach((sameParentItem) => {
      const parentItemGroups = sameParentItem.key.split(/__/g);
      if (parentItemGroups.length === currentKeyGroups.length - 1) {
        const hasDirectParent = isEqual(
          parentItemGroups,
          currentKeyGroups.slice(0, currentKeyGroups.length - 1),
        );
        if (hasDirectParent) {
          item.error = {
            key: 'key',
            msg: 'Cannot create nested object while parent is already defined',
          };
        }
      }
    });
  };

  const handleSave: ChangeSaveCallback<IClientWebhook> = async () => {
    const rawWebhook = webhookClientToRaw(state);
    const rawEndpoint = endpointClientToRaw(state.endpoint);
    if (webhookId && initialState.endpoint?.id) {
      return patchWebhook(state.id, rawWebhook).then(() => {
        patchEndpoint(state.endpoint.id, rawEndpoint);
        navigate(`/admin/webhooks/${state.id}`);
      });
    }
    return postEndpoint(rawEndpoint).then(async (res) => {
      rawWebhook.endpoint_id = res.data.id;
      await postWebhook(rawWebhook).then((res) => {
        navigate(`/admin/webhooks/${res.data.id}`);
      });
    });
  };

  const initialState: IClientWebhook = useMemo(() => {
    if (webhookId !== 'new' && clientWebhooks && clientWebhooks.length > 0)
      return clientWebhooks.find((e) => e.id === webhookId);

    return {
      id: null,
      name: '',
      active: false,
      events: [],
      inboxes: [],
      dependsOn: [],
      actionTypes: [],
      endpoint: {
        id: null,
        name: '',
        type: 'http',
        connector: null,
        useDefaultPayload: true,
        endpointId: 'PostDocActBundleEndpoint',
        path: '',
        payload: [createEmptyRow()],
        headers: [createEmptyRow()],
        params: [createEmptyRow()],
        method: 'POST',
      },
    } as IClientWebhook;
  }, [webhookId, clientWebhooks]);

  const triggerOptions = useMemo(() => {
    return [
      { id: 'document.available', name: 'Document Available' },
      { id: 'document.action', name: 'Document Action' },
    ];
  }, []);

  const triggerActions = useMemo(() => {
    return [
      { id: 'approve', name: 'Approve' },
      { id: 'delete', name: 'Delete' },
      { id: 'bounce', name: 'Bounce' },
    ];
  }, []);

  const connectorOptions = useMemo(() => {
    if (clientConnectors && clientConnectors.length > 0) {
      return clientConnectors
        .filter((ct) => ct?.type === 'http' || ct?.type === 'portimabrio' || ct?.type === 'ccs')
        .map((ct) => ({
          value: ct.id,
          label: ct.name,
          color: ct?.type === 'http' ? '#0085FF' : ct?.type === 'portimabrio' ? '#91C500' : '#7B61FF', // Purple color for CCS
          tag: {
            name: ct.type === 'http' ? 'HTTP' : ct.type === 'portimabrio' ? 'Brio' : 'CCS',
            value: ct.type,
          },
        }))
        .sort((a, b) => {
          const typeComparison = a.tag.name.localeCompare(b.tag.name);
          return typeComparison !== 0 ? typeComparison : a.label.localeCompare(b.label);
        }) as DropdownOption[];
    }
    return [];
  }, [clientConnectors]);

  const connectorDefault = useMemo(() => {
    if (connectorOptions && connectorOptions.length > 0) {
      if (initialState?.endpoint?.connector?.id) {
        return connectorOptions.find((e) => e.value === initialState?.endpoint.connector.id);
      }
      return connectorOptions[0];
    }
    return null;
  }, [initialState, connectorOptions]);

  const { save, saving, hasChanges, state, setState, error, handleInput } = useChangeTracker<IClientWebhook>(
    initialState,
    handleSave,
  );

  const dependsOnOptions = useMemo(() => {
    return sortedWebhooks.map((e) => ({ id: e.id, name: e.name })).filter((e) => e.id !== state.id);
  }, [sortedWebhooks, state.id]);

  const handleDelete = async () => {
    showDialog(
      <ConfirmationDialog
        confirmAction={() => {
          deleteWebhook(webhookId).then(() => {
            deleteEndpoint(state.endpoint.id).then(() => {
              const filtered = sortedWebhooks.filter((e) => e.id !== webhookId);
              if (filtered && filtered.length > 0) {
                navigate(`/admin/webhooks/${filtered[0].id}`);
              } else {
                navigate('/admin/webhooks');
              }
            });
          });
        }}
        text={t('admin:webhooks.deleteDescription')}
      />,
    );
  };

  useEffect(() => {
    if (state?.endpoint?.connector?.type === 'portimabrio' && inboxes?.length > 0) {
      const promiseList = inboxes.map((inbox) =>
        getMasterdataTablesForInboxStatic(inbox.id, tenantId).then((response) => ({
          inboxId: inbox.id,
          data: response,
        })),
      );

      Promise.all(promiseList).then((res) => {
        setTableData(res);
      });
    }
  }, [inboxes, state, tenantId]);

  useEffect(() => {
    if (state.inboxes?.length > 0) {
      const promiseList = state.inboxes.map((inbox) =>
        getDocTypesForInboxStatic(inbox.id, tenantId).then((response) => ({
          inboxId: inbox.id,
          data: response,
        })),
      );

      Promise.all(promiseList).then((res) => {
        setDocTypes(res);
      });
    }
  }, [state, tenantId]);

  useEffect(() => {
    if (state.inboxes) {
      const currentConnectorType = state?.endpoint?.connector?.type;
      const newMapping = {};
      state.inboxes.forEach((inbox) => {
        if (currentConnectorType === 'portimabrio') {
          const currentMapping = (state.endpoint as IClientEndpointBrio).inboxMapping || {};
          if (currentMapping[inbox.id]?.activity_settings) {
            newMapping[inbox.id] = currentMapping[inbox.id];
          } else {
            newMapping[inbox.id] = {
              metadata_field_hierarchy: { claim: undefined, contract: undefined, party: undefined },
              metadata_field_mapping: { activity: {}, document: {} },
              activity_settings: { recipient_default_service: undefined },
            };
          }
        } else if (currentConnectorType === 'ccs') {
          const currentMapping = (state.endpoint as IClientEndpointCCS).inboxMapping || {};

          if (currentMapping[inbox.id]?.metadata_field_mapping?.agenda) {
            newMapping[inbox.id] = currentMapping[inbox.id];
          } else {
            newMapping[inbox.id] = {
              metadata_field_mapping: {
                agenda: {
                  description: undefined,
                  due_date_delta: undefined,
                  employee_number: undefined,
                  reason: undefined,
                  create_agenda: undefined,
                },
                archive: { description: undefined, document_type: undefined, is_secret: undefined },
                claim: {
                  claim_number_office: undefined,
                  company_number: undefined,
                  intermediary_person_number: undefined,
                },
              },
            };
          }
        }
      });
      // Only update state if the new mapping differs from the current mapping
      if (
        currentConnectorType === 'portimabrio' &&
        !isEqual(newMapping, (state.endpoint as IClientEndpointBrio).inboxMapping)
      ) {
        setState((fs) => {
          const updatedState = cloneDeep(fs);
          (updatedState.endpoint as IClientEndpointBrio).inboxMapping = newMapping;
          return updatedState;
        });
      }
      if (
        currentConnectorType === 'ccs' &&
        !isEqual(newMapping, (state.endpoint as IClientEndpointCCS).inboxMapping)
      ) {
        setState((fs) => {
          const updatedState = cloneDeep(fs);
          (updatedState.endpoint as IClientEndpointCCS).inboxMapping = newMapping;
          return updatedState;
        });
      }
    }
  }, [state.inboxes, state.endpoint, setState, initialState]);

  const mappedInboxes: any = useMemo(() => {
    const connectorType = state?.endpoint?.connector?.type;
    if ((connectorType === 'portimabrio' || connectorType === 'ccs') && tableData) {
      return inboxes.map((i) => {
        return {
          id: i.id,
          name: i.settings.name,
          children:
            tableData.find((e) => e.inboxId === i.id)?.data?.map((d) => ({ id: d.id, name: d.name })) || [],
        };
      });
    }
    return inboxes.map((i) => ({ id: i.id, name: i.settings.name }));
  }, [state, tableData, inboxes]);

  const mappedDoctypes = useMemo(() => {
    if (!state.inboxes || !docTypes) return [];
    return inboxes
      .map((i) => {
        return {
          id: i.id,
          name: i.settings.name,
          children:
            docTypes.find((e) => e.inboxId === i.id)?.data?.map((d) => ({ id: d.id, name: d.name })) || null,
        };
      })
      .filter((i) => state?.inboxes.find((e) => e.id === i.id));
  }, [state, docTypes]);

  const handleDropdownChange = useCallback(
    (e) => {
      setState((fs) => {
        const changed = cloneDeep(fs);
        if (!fs || !connectorOptions?.length) return fs;
        if (!changed.endpoint) changed.endpoint = { path: '', connector: null, id: null };
        changed.endpoint.connector = clientConnectors.find((ct) => ct.id === e.value) as IClientConnector;
        return changed;
      });
    },
    [setState, connectorOptions?.length, clientConnectors],
  );

  // Add local state for tables and doctypes selection
  const [selectedTables, setSelectedTables] = useState<{ [inboxId: string]: string[] | null }>({});
  const [selectedDocTypes, setSelectedDocTypes] = useState<{ [inboxId: string]: string[] | null }>({});

  // Sync local state with webhook state when inboxes change
  useEffect(() => {
    if (state.inboxes) {
      const tables: { [inboxId: string]: string[] | null } = {};
      const doctypes: { [inboxId: string]: string[] | null } = {};
      state.inboxes.forEach((inbox) => {
        tables[inbox.id] = inbox.tableIds ?? null;
        doctypes[inbox.id] = inbox.docTypes ?? null;
      });
      setSelectedTables(tables);
      setSelectedDocTypes(doctypes);
    }
  }, [state.inboxes]);

  // Sync main state with local state whenever local state changes
  useEffect(() => {
    // Skip initial sync when selectedTables or selectedDocTypes are empty
    if (Object.keys(selectedTables).length === 0 || Object.keys(selectedDocTypes).length === 0) {
      return;
    }

    setState((prev) => {
      // Check if the state would actually change before updating
      let hasChanges = false;
      const newInboxes = prev.inboxes.map((inbox) => {
        const newTableIds = selectedTables[inbox.id] ?? null;
        const newDocTypes = selectedDocTypes[inbox.id] ?? null;

        if (!isEqual(inbox.tableIds, newTableIds) || !isEqual(inbox.docTypes, newDocTypes)) {
          hasChanges = true;
          return {
            ...inbox,
            tableIds: newTableIds,
            docTypes: newDocTypes,
          };
        }
        return inbox;
      });

      // Only return a new state if there are actual changes
      return hasChanges ? { ...prev, inboxes: newInboxes } : prev;
    });
  }, [selectedTables, selectedDocTypes, setState]);

  if (webhookId !== 'new' && (state.id === '' || state.id === null || state === null))
    return (
      <div className={se.loading}>
        <RaceBy color={'#0085FF'} size={150} />
      </div>
    );

  return (
    <form
      onSubmit={(e) => {
        // No need to merge states here since they're already in sync
        save(e);
      }}
      className={se.form_body}
    >
      <FormBodyHeader
        errorMessage={errorMap[error] ?? null}
        // errorMessage={
        //   errorMap[error] ?? error ?? state?.endpoint?.connector?.type === 'portimabrio'
        //     ? t('admin:webhooks.BRIO_NOT_SUPPORTED')
        //     : null
        // }
        hasChanges={hasChanges}
        saving={saving}
        title={state?.name || t('admin:webhooks.add')}
      />
      <div className={se.sections}>
        <FormSection title={t('admin:webhooks.generalInfo')}>
          <FormInputField
            value={state?.active ?? true}
            type={'toggle'}
            label={t('admin:webhooks.enabled')}
            description={t('admin:webhooks.enabledDescription')}
            onChange={() => handleInput(!state?.active, 'active')}
          />
          <FormInputField
            required
            value={state?.name}
            type={'text'}
            label={t('admin:webhooks.name')}
            description={t('admin:webhooks.nameDescription')}
            onChange={(val) => handleInput(val, 'name')}
            placeholder={'Webhook Name'}
          />
          <FormInputField
            value={webhookId}
            hidden={webhookId === 'new'}
            type={'text'}
            label={t('admin:webhooks.id')}
            description={t('admin:webhooks.idDescription')}
            isPaperboxOnly
            isCopyField
          />
          <FormMultiSelectField
            title={t('admin:webhooks.dependsOn')}
            description={t('admin:webhooks.dependsOnDescription')}
            value={state?.dependsOn?.map((e) => ({ id: e })) || []}
            onChange={(newValue) => setState({ ...state, dependsOn: newValue.map((e) => e.id) })}
            options={dependsOnOptions}
            showDialog={showDialog}
          />
        </FormSection>

        <FormSection title={t('admin:webhooks.connection')}>
          <FormInputField
            hidden={!connectorOptions?.length}
            value={connectorOptions?.find((e) => e.value === state?.endpoint?.connector?.id)}
            label={t('admin:webhooks.connector')}
            description={t('admin:webhooks.connectorDescription')}
            type="dropdown"
            disabled={webhookId !== 'new'}
            dropdownOptions={connectorOptions}
            defaultDropdownOption={connectorDefault}
            onChange={handleDropdownChange}
          />
          {state?.endpoint?.connector?.type === 'portimabrio' && (
            <FormInputField
              value={(state?.endpoint as IClientEndpointBrio)?.endpointId}
              type="text"
              label={t('admin:webhooks.brioEndpointId')}
              description={t('admin:webhooks.brioEndpointIdDescription')}
              onChange={(val) =>
                setState((fs) => {
                  const copy = cloneDeep(fs);
                  (copy.endpoint as IClientEndpointBrio) = val;
                  return copy;
                })
              }
              placeholder={'Endpoint ID'}
              disabled
            />
          )}
          {state?.endpoint?.connector?.type === 'http' && (
            <FormInputField
              value={(state?.endpoint as IClientEndpointHTTP)?.path}
              type="text"
              label={t('admin:webhooks.url')}
              description={t('admin:webhooks.urlDescription')}
              onChange={(val) =>
                setState((fs) => {
                  let newVal = val;
                  const copy = cloneDeep(fs) as IClientWebhookHTTP;
                  if (newVal[0] !== '/') newVal = `/${newVal}`;
                  (copy.endpoint as IClientEndpointHTTP).path = newVal.replace(' ', '-');
                  return copy;
                })
              }
              placeholder={'/path/example'}
            />
          )}
        </FormSection>
        <FormSection title={t('admin:webhooks.triggers')}>
          <FormMultiSelectField
            title={t('admin:webhooks.triggerEvent')}
            description={t('admin:webhooks.triggerEventDescription')}
            value={state?.events.map((e) => ({ id: e }))}
            onChange={(newValue) => setState({ ...state, events: newValue.map((e) => e.id) })}
            options={triggerOptions}
            showDialog={showDialog}
          />

          <FormMultiSelectField
            title={t('admin:webhooks.selectInboxes')}
            description={t('admin:webhooks.selectInboxesDescription')}
            value={state?.inboxes.map((e) => ({ id: e.id }))}
            onChange={(newValue: MultiSelectDetailsType[]) => {
              setState((prevState) => {
                const copy = cloneDeep(prevState);
                // Preserve tableIds and docTypes for existing inboxes
                const existingInboxMap = {};
                copy.inboxes.forEach((inbox) => {
                  existingInboxMap[inbox.id] = {
                    tableIds: inbox.tableIds,
                    docTypes: inbox.docTypes,
                  };
                });

                // Create new inboxes array with preserved data where possible
                copy.inboxes = newValue.map((item) => ({
                  id: item.id,
                  // Use existing tableIds if available, otherwise use null (all selected)
                  tableIds: existingInboxMap[item.id] ? existingInboxMap[item.id].tableIds : null,
                  // Use existing docTypes if available, otherwise use null (all selected)
                  docTypes: existingInboxMap[item.id] ? existingInboxMap[item.id].docTypes : null,
                }));

                return copy;
              });
            }}
            options={inboxes.map((i) => ({ id: i.id, name: i.settings.name }))}
            showDialog={showDialog}
          />

          {state.inboxes.length > 0 && (
            <FormMultiSelectField
              restrictDeleteAll
              title={t('admin:webhooks.selectTables')}
              description={t('admin:webhooks.selectTablesDescription')}
              value={Object.entries(selectedTables).map(([id, children]) => ({ id, children }))}
              onChange={(newValue: MultiSelectDetailsType[]) => {
                const newTables = { ...selectedTables };
                newValue.forEach((item) => {
                  newTables[item.id] = item.children as any;
                });
                setSelectedTables(newTables);
              }}
              options={mappedInboxes.filter((inbox) => state.inboxes.some((i) => i.id === inbox.id))}
              showDialog={showDialog}
              noChildrenText={'All Tables'}
            />
          )}

          {state.inboxes.length > 0 && (
            <FormMultiSelectField
              restrictDeleteAll
              title={t('admin:webhooks.selectDocTypes')}
              description={t('admin:webhooks.selectDocTypesDescription')}
              value={Object.entries(selectedDocTypes).map(([id, children]) => ({ id, children }))}
              onChange={(newValue: MultiSelectDetailsType[]) => {
                const newDocTypes = { ...selectedDocTypes };
                newValue.forEach((item) => {
                  newDocTypes[item.id] = item.children as any;
                });
                setSelectedDocTypes(newDocTypes);
              }}
              options={mappedDoctypes.filter((inbox) => state.inboxes.some((i) => i.id === inbox.id))}
              showDialog={showDialog}
              noChildrenText={'All Document Types'}
            />
          )}

          {state?.events?.includes('document.action') && (
            <FormMultiSelectField
              title={t('admin:webhooks.triggerActionTypes')}
              description={t('admin:webhooks.triggerActionTypesDescription')}
              value={state?.actionTypes.map((e) => ({ id: e }))}
              onChange={(newValue) => setState({ ...state, actionTypes: newValue.map((e) => e.id) })}
              options={triggerActions}
              showDialog={showDialog}
            />
          )}
        </FormSection>
        {state?.endpoint?.connector?.type === 'ccs' && (
          <FormSection title={'Trigger Inbox Configuration'} noStyle>
            {(state.endpoint as IClientEndpointCCS).inboxMapping &&
              Object.entries((state.endpoint as IClientEndpointCCS).inboxMapping).map(([k, v]) => {
                const inboxDetails = inboxes.find((e) => e.id === k);
                return (
                  <AdminCCSWebhookEdit
                    key={k}
                    data={v}
                    isOpen={activeCCSInboxId === k}
                    inboxId={k}
                    inbox={inboxDetails}
                    setIsOpen={(bo) => setActiveCCSInboxId(bo ? k : '')}
                    onChange={(data) => {
                      setState((fs) => {
                        const copy = cloneDeep(fs) as any;
                        copy.endpoint.inboxMapping[k] = data;
                        return copy;
                      });
                    }}
                  />
                );
              })}
          </FormSection>
        )}
        {state?.endpoint?.connector?.type === 'portimabrio' && (
          <FormSection title={'Trigger Inbox Configuration'} noStyle>
            {(state.endpoint as IClientEndpointBrio).inboxMapping &&
              Object.entries((state.endpoint as IClientEndpointBrio).inboxMapping).map(([k, v]) => {
                const inboxDetails = inboxes.find((e) => e.id === k);
                return (
                  <AdminBrioWebhookEdit
                    key={k}
                    data={v}
                    isOpen={activeBrioInboxId === k}
                    inboxId={k}
                    inbox={inboxDetails}
                    setIsOpen={(bo) => setActiveBrioInboxId(bo ? k : '')}
                    onChange={(data) => {
                      setState((fs) => {
                        const copy = cloneDeep(fs) as any;
                        copy.endpoint.inboxMapping[k] = data;
                        return copy;
                      });
                    }}
                  />
                );
              })}
          </FormSection>
        )}
        {state?.endpoint?.connector?.type === 'http' && (
          <FormSection title={t('admin:webhooks.dataConfig')}>
            <FormRequestField
              testId={'data-params'}
              editRow={(index, t, v) => editRow(index, t, v, 'params')}
              description={t('admin:webhooks.queryParamsDescription')}
              label={t('admin:webhooks.queryParams')}
              deleteRow={(index) => deleteRow(index, 'params')}
              addRow={() => addRow('params')}
              items={(state?.endpoint as IClientEndpointHTTP)?.params}
            />

            <FormRequestField
              testId={'data-headers'}
              editRow={(index, t, v) => editRow(index, t, v, 'headers')}
              description={t('admin:webhooks.headersDescription')}
              label={t('admin:webhooks.headers')}
              deleteRow={(index) => deleteRow(index, 'headers')}
              addRow={() => addRow('headers')}
              items={(state?.endpoint as IClientEndpointHTTP).headers}
            />
            <FormInputField
              type={'toggle'}
              value={(state?.endpoint as IClientEndpointHTTP).useDefaultPayload ?? true}
              label={t('admin:webhooks.defaultPayload')}
              description={t('admin:webhooks.defaultPayloadDescription')}
              onChange={() => {
                setState((fs) => {
                  const copy = cloneDeep(fs) as any;
                  copy.endpoint.useDefaultPayload = !(state?.endpoint as IClientEndpointHTTP)
                    .useDefaultPayload;
                  return copy;
                });
              }}
            />
            <FormRequestField
              testId={'data-payload'}
              hidden={(state?.endpoint as IClientEndpointHTTP)?.useDefaultPayload}
              editRow={(index, t, v) => editRow(index, t, v, 'payload')}
              description={t('admin:webhooks.customPayloadDescription')}
              label={t('admin:webhooks.customPayload')}
              deleteRow={(index) => deleteRow(index, 'payload')}
              addRow={() => addRow('payload')}
              items={(state?.endpoint as IClientEndpointHTTP)?.payload}
            />
            {/*<FormInputField*/}
            {/*  hidden={state?.endpoint.useDefaultPayload }*/}
            {/*  value={ true}*/}
            {/*  // value={state?.endpoint.unFlatten ?? true}*/}
            {/*  type={'toggle'}*/}
            {/*  label={t('admin:webhooks.unflatten')}*/}
            {/*  description={t('admin:webhooks.unflattenDescription')}*/}
            {/*  onChange={() => {*/}
            {/*    setState((fs) => {*/}
            {/*      const copy = cloneDeep(fs);*/}
            {/*      copy.endpoint.unFlatten = !state?.endpoint.unFlatten;*/}
            {/*      return copy;*/}
            {/*    });*/}
            {/*  }}*/}
            {/*/>*/}
          </FormSection>
        )}
        <div>
          {webhookId !== 'new' && (
            <FormSection hidden={webhookId === 'new'} title={t('admin:webhooks.dangerZone')}>
              <FormInputField
                testId={'webhook-delete'}
                type={'button'}
                buttonOptions={{
                  type: 'error',
                  text: t('admin:webhooks.delete'),
                  onClick: handleDelete,
                }}
                label={t('admin:webhooks.delete')}
                description={t('admin:webhooks.deleteDescription')}
              />
            </FormSection>
          )}
        </div>
      </div>
    </form>
  );
};

export default AdminWebhookEdit;
