import DocumentTagDropdown from '@components/document/header/DocumentTagDropdown.tsx';
import { sleep } from '@shared/helpers/helpers.ts';
import { useGetActiveInboxQuery, useGetTagTypesQuery } from '@shared/helpers/rtk-query/firestoreApi.ts';
import { useDocumentOperations } from '@shared/hooks/useDocumentOperations';
import { ExtendedTagType } from '@shared/models/document.ts';
import { UrlParams } from '@shared/models/generic.ts';
import {
  selectActiveDocument,
  selectEffectiveNextSiblingId,
  selectEffectivePrevSiblingId,
  selectIsInteractive,
  selectIsMutation,
} from '@shared/store/documentSlice';
import { useSelector } from '@shared/store/store';
import { IClientInboxSettings } from '@src/shared/helpers/converters/inbox';
import { ReactComponent as ArrowLeftIcon } from '@svg/arrow-left-icon.svg';
import { DotPulse } from '@uiball/loaders';
import clsx from 'clsx';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useParams } from 'react-router';
import s from '../../sidebar.module.scss';

const DocumentLabelerSidebarNav: React.FC = () => {
  const { t } = useTranslation();
  const { inboxId }: UrlParams = useParams();
  const location = useLocation();
  const historical = location.pathname.includes('historical');
  const [approving, setApproving] = useState(false);

  // Get state from Redux
  const activeDocument = useSelector(selectActiveDocument);
  const prevSiblingId = useSelector(selectEffectivePrevSiblingId);
  const nextSiblingId = useSelector(selectEffectiveNextSiblingId);
  const isMutation = useSelector(selectIsMutation);
  const isInteractive = useSelector(selectIsInteractive);

  // Get operations from the hook
  const { handleNavNext, handleNavPrev, patchDoc, approveDoc } = useDocumentOperations();
  const { data: activeInbox } = useGetActiveInboxQuery({ inboxId });
  const { data: tagTypes } = useGetTagTypesQuery({ inboxId });
  const { isHidden } = useSelector((state) => state.user.userAccount);

  // Derived data
  const documentEntities = activeDocument?.entities ?? [];
  const { labelingMode } = useMemo(
    () => activeInbox?.settings ?? ({} as IClientInboxSettings),
    [activeInbox],
  );

  // Computed values
  const tagTypeColor = useMemo(() => {
    if (!labelingMode || !tagTypes || historical) return null;
    const activeTagType = tagTypes.find((e) => e.id === activeDocument?.tagTypeId);
    return activeTagType?.color || '#313D4F';
  }, [activeDocument, labelingMode, tagTypes, historical]);

  const allCopiesApproved = useMemo(() => {
    if (!activeDocument) return false;
    return !activeDocument.nMutations || activeDocument.nActiveMutations === 0;
  }, [activeDocument]);

  const hasUnmetApprovalChecks = useMemo(
    () => activeDocument?.approvalChecks?.some((at) => at.status === 'failed') ?? false,
    [activeDocument],
  );

  const isWorkflowRunning = useMemo(
    () => activeDocument?.latestWorkflowRun?.status !== 'FINISHED',
    [activeDocument],
  );

  const isApproveDisabled = useMemo(
    () =>
      !activeDocument?.processed ||
      !isInteractive ||
      !allCopiesApproved ||
      hasUnmetApprovalChecks ||
      isWorkflowRunning ||
      isHidden,
    [activeDocument, isInteractive, allCopiesApproved, hasUnmetApprovalChecks, isWorkflowRunning, isHidden],
  );

  // Process tag types for validation
  const filteredTagTypes = useMemo(() => {
    if (!labelingMode || !documentEntities || !tagTypes) return tagTypes;

    const hasUncheckedEntities = documentEntities.some((e) => {
      if (e.value['complex']) {
        return Object.values(e.value['complex']).some((val: any) => !val.isChecked);
      }
      return !e.isChecked;
    });

    return tagTypes.map((tagType) => {
      if (tagType.id !== 'DONE') return tagType;

      const isDisabled = hasUncheckedEntities || hasUnmetApprovalChecks;
      const disabledReason = hasUncheckedEntities ? t('document:labeling.labelAll') : undefined;

      return isDisabled ? { ...tagType, isDisabled, disabledReason } : tagType;
    }) as ExtendedTagType[];
  }, [labelingMode, documentEntities, tagTypes, hasUnmetApprovalChecks, t]);

  const buttonText = useMemo(() => {
    if (hasUnmetApprovalChecks) return t('document:approveApprovalChecks');
    if (approving) return `${t('document:approving')}...`;

    if (activeDocument) {
      if (activeDocument.nMutations > 0) {
        return !allCopiesApproved ? t('document:finishCopies') : t('document:approveOriginal');
      }
      return isMutation ? t('document:approveCopy') : t('document:approve');
    }

    return t('document:approve');
  }, [hasUnmetApprovalChecks, approving, activeDocument, allCopiesApproved, isMutation, t]);

  // Event handlers
  const handleValidation = async () => {
    if (approving) return;

    setApproving(true);
    await approveDoc();
    await sleep(500);
    setApproving(false);
  };

  const handleSelectTag = (tagId: string) => {
    patchDoc({ tag_type_id: tagId });
  };

  return (
    <div className={s.bar_content} style={{ overflow: 'visible', paddingTop: 0 }}>
      <div className={s.nav}>
        <button
          aria-label="nav-prev-doc"
          data-testid="doc-prev"
          style={{ background: tagTypeColor ?? null }}
          disabled={!prevSiblingId}
          onClick={handleNavPrev}
          className={clsx(s.button_main, s.button_nav, s.button_nav__right)}
        >
          <ArrowLeftIcon />
        </button>

        {historical && (
          <button className={clsx(s.button_main)} style={{ cursor: 'default' }}>
            {t(`document:actions.${activeDocument?.action?.type}`)} - {activeDocument?.action?.actorEmail}
          </button>
        )}
        {labelingMode && (
          <div className={s.button_main}>
            <DocumentTagDropdown
              activeTagId={activeDocument?.tagTypeId}
              className={s.button_tags}
              handleSelectTag={handleSelectTag}
              tagTypes={filteredTagTypes}
              position="top"
            />
          </div>
        )}
        {!historical && (activeDocument?.action || approving) && (
          <button className={clsx(s.button_main)} disabled>
            {t('document:approved')}
          </button>
        )}
        {!historical && !activeDocument?.action && !approving && (
          <button onClick={handleValidation} className={s.button_main} disabled={isApproveDisabled}>
            {isWorkflowRunning ? (
              <DotPulse size={25} speed={0.6} color="rgba(0, 133, 255, 0.5)" />
            ) : (
              buttonText
            )}
          </button>
        )}

        {/*<Button*/}
        {/*  disabled={isApproveDisabled}*/}
        {/*  aria-label="approve-doc"*/}
        {/*  data-testid="doc-approve"*/}
        {/*  onClick={handleValidation}*/}
        {/*  className={clsx(s.button_main, s.button_main__validate)}*/}
        {/*  text={isWorkflowRunning ? <DotPulse size={25} speed={0.6} color="white" /> : buttonText}*/}
        {/*  iconLeft*/}
        {/*  iconElement={isWorkflowRunning ? null : !approving ? <CheckCircleIcon /> : null}*/}
        {/*/>*/}

        <button
          aria-label="nav-next-doc"
          data-testid="doc-next"
          style={{ background: tagTypeColor ?? null }}
          disabled={!nextSiblingId}
          onClick={handleNavNext}
          className={clsx(s.button_main, s.button_nav, s.button_nav__left)}
        >
          <ArrowLeftIcon />
        </button>
      </div>
    </div>
  );
};

export default DocumentLabelerSidebarNav;
