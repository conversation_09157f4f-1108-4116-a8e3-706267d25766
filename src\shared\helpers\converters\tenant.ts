export interface IClientTenant {
  config?: IClientTenantConfig;
  settings?: IClientTenantSettings;
  notice?: IClientTenantNotice;
}

export interface IClientTenantConfig {
  features: IClientTenantFeatures;
  googleLogin: boolean;
  hotjar: boolean;
  subdomain: string;
  multiFactor: boolean;
}

export interface IClientTenantFeatures {
  emailIngestRoute: boolean;
  bounce: boolean;
  documentCopy: boolean;
  documentDownload: boolean;
  fileUpload: boolean;
  labelingMode: boolean;
}
export interface IClientTenantNotice {
  message: string;
  priority: number;
}
export interface IClientTenantSettings {
  allowedDomains?: string[];
  allowedIPs?: string[];
  language: string;
  name: string;
  timezone: string;
  inviteOnly: boolean;
  dashboardRange: number;
  netlifyBeta?: boolean;
}
