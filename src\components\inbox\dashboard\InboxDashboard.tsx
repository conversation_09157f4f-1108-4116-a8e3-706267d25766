import { isSameDate } from '@shared/helpers/helpers.ts';
import { useModal } from '@shared/hooks/useModal';
import { Inbox } from '@shared/models/inbox';
import { useSelector } from '@shared/store/store';
import { userInboxesSelector } from '@shared/store/userSlice.ts';
import sc from '@shared/styles/component/inbox/inbox-content.module.scss';
import { ReactComponent as RotateIcon } from '@svg/rotate-icon.svg';
import clsx from 'clsx';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { DateRangePicker } from 'rsuite';
import { RangeType } from 'rsuite/DateRangePicker';
import { afterToday } from 'rsuite/esm/DateRangePicker/disabledDateUtils';
import { addDays, subDays } from 'rsuite/esm/utils/dateUtils';
import AdminMultiSelectDialog from '../../admin/components/AdminMultiSelectDialog.tsx';
import HeaderProfile from '../../header/profile/HeaderProfile.tsx';
import DashboardAAP from './DashboardAAP.tsx';
// import HeaderProfileDropdown from '../../header/profile/HeaderProfileDropdown.tsx';
import DashboardFlow, { FlowComponentHandle } from './DashboardFlow.tsx';
import DashboardOutflow from './DashboardOutflow.tsx';

interface InboxDashboardProps {}

export interface ColoredInbox extends Inbox {
  color: string;
}
export type AnalyticsPeriod = [Date, Date];
export type AnalyticsGrouping = 'docType' | 'date' | 'user' | 'tag' | 'action';
export const inboxColors = [
  '#0085FF',
  '#FF6700',
  '#FF005A',
  '#FFBA06',
  '#007AFF',
  '#FF2D55',
  '#5856D6',
  '#FF2E63',
  '#FF9500',
  '#5A189A',
];

const demoInboxNamesEN = ['Claims', 'Policy Admin', 'Policy Admin Scans', 'Claims Scans'];
const demoInboxNamesNL = ['Schade', 'Productie', 'Productie Scans', 'Scans'];

const demoInboxesUnmapped = [
  {
    id: 'a9jk6pi10pe15ck8ygmkn7ds0',
    settings: {
      name: 'Schade',
    },
    doc_types: new Map<string, string>(),
    doc_names: new Map<string, string>(),
  },
  {
    id: 'cq18mgup0csrdjpjywryk7byf',
    settings: {
      name: 'Productie',
    },
    doc_types: new Map<string, string>(),
    doc_names: new Map<string, string>(),
  },
  {
    id: 'ejgekygmyzx236dqi6rufrh6q',
    settings: {
      name: 'Productie Scans',
    },
    doc_types: new Map<string, string>(),
    doc_names: new Map<string, string>(),
  },
  {
    id: 'll2ftfifw1fl3ympcmopt3dq',
    settings: {
      name: 'Schade Scans',
    },
    doc_types: new Map<string, string>(),
    doc_names: new Map<string, string>(),
  },
];

const InboxDashboard: React.FC<InboxDashboardProps> = () => {
  const [activeInboxes, setActiveInboxes] = useState<string[]>([]);
  const [isReloading, setIsReloading] = useState(false);
  const { t, i18n } = useTranslation();
  const [activeDateRange, setActiveDateRange] = useState<[Date, Date]>([new Date(), new Date()]);

  const AAPRef = useRef<FlowComponentHandle>(null);
  const outflowRef = useRef<FlowComponentHandle>(null);
  const flowRef = useRef<FlowComponentHandle>(null);

  const userAccount = useSelector((state) => state.user.userAccount);
  const inboxes = useSelector(userInboxesSelector);
  const demoMode = useSelector((state) => state.dashboard.demoMode);
  const { showDialog } = useModal();

  const demoInboxes = useMemo(() => {
    return demoInboxesUnmapped.map((e, i) => {
      const clone = { ...e };
      const map = i18n.language === 'nl' ? demoInboxNamesNL : demoInboxNamesEN;
      clone.settings.name = map[i];
      return clone;
    });
  }, [i18n.language]);

  const coloredInboxes: ColoredInbox[] = useMemo(() => {
    const currentInboxes = demoMode ? demoInboxes : inboxes;
    return currentInboxes.map((inbox, i) => ({ ...inbox, color: inboxColors[i % 10] ?? 'white' }));
  }, [demoInboxes, demoMode, inboxes]);

  const mappedActiveInboxes = useMemo(() => {
    return activeInboxes.map((id) => coloredInboxes.find((i) => i.id === id)).filter((e) => e != null);
  }, [activeInboxes, coloredInboxes]);

  useEffect(() => {
    if (activeInboxes.length === 0 && coloredInboxes.length > 0) {
      setActiveInboxes(coloredInboxes.map((i) => i.id));
    }
  }, [demoMode, activeInboxes, coloredInboxes]);

  const refreshData = () => {
    setIsReloading(true);
    const promises = [];
    promises.push(AAPRef.current?.fetchFunc(), outflowRef.current?.fetchFunc(), flowRef.current?.fetchFunc());
    Promise.all(promises).finally(() => {
      setIsReloading(false);
    });
  };

  const predefinedBottomRanges: RangeType[] = [
    {
      label: t('home:dashboard.rangeDay'),
      value: [new Date(), new Date()],
    },
    {
      label: t('home:dashboard.rangeYesterday'),
      value: [addDays(new Date(), -1), addDays(new Date(), -1)],
    },
    {
      label: t('home:dashboard.rangeWeek'),
      value: [subDays(new Date(), 6), new Date()],
    },
    {
      label: t('home:dashboard.rangeMonth'),
      value: [subDays(new Date(), 29), new Date()],
    },
  ];

  return (
    <>
      <div className={sc.container} style={{ marginBottom: 0 }}>
        <div className={sc.header}>
          <h3 className={sc.title} data-testid="inbox-title">
            {t('home:dashboard.title')}
          </h3>
          <HeaderProfile />
        </div>
        <div className={sc.sub_header}>
          <div className={sc.tags_wrapper}>
            <div className={sc.tags}>
              <button
                onClick={() => {
                  showDialog(
                    <AdminMultiSelectDialog
                      title={t('home:dashboard.inboxesTitle')}
                      description={t('home:dashboard.inboxesDescription')}
                      selectedTypes={activeInboxes?.map((i) => ({ id: i }))}
                      handleCheckTypes={(e) => {
                        setActiveInboxes(e.map((i) => i.id));
                      }}
                      detailedList={coloredInboxes.map((i) => ({ name: i.settings.name, ...i }))}
                    />,
                  );
                }}
                className={clsx(sc.tag, sc.tag__active)}
              >
                <Trans
                  i18nKey={'home:dashboard.inboxes'}
                  defaults={'{{count}} Visible'}
                  values={{ count: activeInboxes.length }}
                />
              </button>
            </div>
          </div>
          <div className={sc.tags_wrapper} style={{ marginRight: 'auto' }}>
            <div className={sc.tags}>
              <button
                onClick={refreshData}
                className={clsx(sc.tag, { [sc.tag__loading]: isReloading })}
                style={{ padding: 10 }}
              >
                <RotateIcon />
              </button>
            </div>
          </div>

          <div className={sc.tags_wrapper}>
            <DateRangePicker
              size={'lg'}
              ranges={predefinedBottomRanges}
              shouldDisableDate={afterToday()}
              placeholder="Filter Date Range"
              placement={'auto'}
              editable={false}
              cleanable={false}
              renderValue={(value) => {
                const test = predefinedBottomRanges.find(
                  (e) =>
                    isSameDate(new Date(e.value[0]), new Date(value[0])) &&
                    isSameDate(new Date(e.value[1]), new Date(value[1])),
                );
                return (
                  <div>
                    {test
                      ? test.label
                      : `${value[0].toLocaleDateString()} - ${value[1].toLocaleDateString()}`}
                  </div>
                );
              }}
              value={activeDateRange}
              isoWeek
              character={'  -  '}
              onChange={(e) => {
                if (!e) {
                  setActiveDateRange(undefined);
                } else {
                  setActiveDateRange(e);
                }
              }}
            />
          </div>
        </div>
      </div>
      <div className={sc.wrapper}>
        <DashboardFlow ref={flowRef} inboxes={mappedActiveInboxes} activeDateRange={activeDateRange} />
        <DashboardOutflow ref={outflowRef} inboxes={mappedActiveInboxes} activeDateRange={activeDateRange} />
        {userAccount?.isAdmin && (
          <DashboardAAP ref={AAPRef} inboxes={mappedActiveInboxes} activeDateRange={activeDateRange} />
        )}
      </div>
    </>
  );
};

export default InboxDashboard;
