@import "../../vars/_vars";


.container {
  position: relative;
  display: flex;
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  transition: opacity 0.2s ease-in-out;
  background: $white;
}


.close {
  position: fixed;
  bottom: 20px;
  left: 20px;
  color: #6F758A;

  &:hover {
    color: $error;
  }
}


.body {
  display: flex;
  flex-direction: column;
  width: 100%;
}


.sidebar {
  width: 80px;
  max-width: 80px;
  border-right: 1px solid #EEEEEE;

  .header {
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    padding: 20px;
    color: black;
    border-bottom: 1px solid #EEEEEE;

    svg {
      cursor: pointer;
      color: black;

      &:hover {
        color: $error;
      }
    }
  }
}


.subcontent {
  display: flex;
  flex-direction: row;
  height: 100%;
}


.subsidebar {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  width: 265px;
  height: 100%;
  border-right: 1px solid #EEEEEE;
  background: white;
  gap: 8px;

  .header {
    font-size: 18px;
    font-weight: 500;
    margin: 24px 24px;
  }

  .add {
    margin: 12px 24px;
    color: $paperbox-blue;
  }

  .section {
    position: relative;
    display: flex;
    overflow: auto;
    flex-direction: column;
    padding: 0 8px;
    gap: 4px;

    &__top {
      overflow: auto;
      height: 260px;
      max-height: 260px;
    }

    &:not(:first-of-type) {
      margin-top: 16px;
      padding-top: 32px;
      border-top: 1px solid #EDEFF1;
    }

    .item {
      font-size: 16px;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      justify-content: space-between;
      height: 42px;
      padding: 0 20px;
      color: black;
      border: 1px solid transparent;
      border-radius: 7px;
      gap: 8px;
      span{
        flex-shrink: 1;
        white-space: wrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      svg {
        width: auto;
        height: 16px;
        opacity: 0.7;
        flex-shrink: 0;
      }

      &:hover {
        color: $paperbox-blue;
      }

      &.active {
        color: $paperbox-blue;
        border-color: #EDEFF1;
        background: #FBFCFD;

        svg {
          opacity: 1;
        }
      }
    }
  }
}


.sidebar_header {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 66px;
  margin-bottom: 20px;
  color: #6F758A;

  svg {
    width: 28px;
    height: auto;
  }

  &:hover {
    cursor: pointer;
    color: $paperbox-blue-light;
  }
}


.sidebar_item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 60px;
  transition: color 0.2s ease-in-out;

  color: #6F758A;

  & + & {
    margin-top: 10px;
  }

  &:after {
    position: absolute;
    right: 0;
    width: 4px;
    height: 100%;
    content: "";
    transition: background-color 0.2s ease-in-out;
    border-radius: 5px 0 0 5px;
    background: transparent;
  }

  &:hover {
    cursor: pointer;
    color: $paperbox-blue-light;

    &:after {
      background: $paperbox-blue-light;
    }
  }

  &__active {
    color: $paperbox-blue;

    &:after {
      background: $paperbox-blue;
    }
  }
}


.content {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #FBFCFD;
}


.dialog_cross {
  cursor: pointer;
  color: #898B99;

  &:hover {
    color: $error;
  }
}


.dialog_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 42px;
  padding: 24px;
  border-bottom: 1px solid rgba($dark-gray, 0.3);
}


.dialog_body {
  height: auto;
  padding: 24px 24px 0;
  background: #FCFBFC;
}


.dialog_text {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 21px;
}


.drag_handle {
  min-width: 14px;
  max-width: 14px;
  min-height: 16px;
  max-height: 16px;
  margin-left: -8px;
  color: #A0A0A0;
}


.multi_select_search {
  position: relative;
  width: 100%;

  .search_icon {
    position: absolute;
    right: 12px;
    bottom: 8px;
    color: #969FAD;
  }

  .search {
    font-family: $base-font;
    font-size: 14px;
    width: 100%;
    min-height: 38px;
    padding-right: 36px;
    padding-left: 12px;
    border: 1px solid #EEEEEE;
    border-radius: 3px;
    outline: none;
    background: white;

    &::placeholder {
      color: #969FAD;
    }
  }
}


.multi_select_row {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: black;
  border: 1px solid $paperbox-blue;
  border-radius: 4px;

  &:hover {
    background-color: #F4F6F9;
  }

  span {
    user-select: none;
    pointer-events: none;
  }

  // Add more depths if needed

  .arrow_icon {
    margin-right: 8px;
    transition: transform 0.3s ease;
    color: black;

    &.collapsed {
      transform: rotate(-90deg);
    }
  }
}


.multi_select_children {
  display: flex;
  flex-direction: column;
  margin-top: 2px;
  margin-left: 6px;
  gap: 2px;
}


.multi_select_container {
  overflow-y: auto;
  max-height: 600px;
  padding: 0px;
  border-radius: 4px;

  &::-webkit-scrollbar {
    width: 5px;
  }
}


.multi_select_wrapper {
  margin-right: -10px;
  margin-left: -10px;
  padding: 12px;
  border-radius: 7px;
}


.multi_select_container {
  display: flex;
  overflow: auto;
  flex-direction: column;
  max-height: 300px;
  border-radius: 5px;
  gap: 4px;
}


.multi_select_search {
  position: relative;
  width: 100%;

  .search_icon {
    position: absolute;
    right: 12px;
    bottom: 8px;
    color: #969FAD;
  }

  .search {
    font-family: $base-font;
    font-size: 14px;
    width: 100%;
    min-height: 38px;
    padding-right: 36px;
    padding-left: 12px;
    border: 1px solid #EEEEEE;
    border-radius: 3px;
    outline: none;
    background: white;

    &::placeholder {
      color: #969FAD;
    }
  }
}


.multi_select_row {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 38px;
  padding: 4px 8px 4px 12px;
  cursor: pointer;
  border: 1px solid #EEEEEE;
  border-radius: 3px;
  background: white;
}


.multi_select_wrapper {
  margin-right: -10px;
  margin-left: -10px;
  padding: 12px;
  border-radius: 7px;
}


//.multi_select_container {
//  display: flex;
//  overflow: auto;
//  flex-direction: column;
//  max-height: 410px;
//  border-radius: 5px;
//  gap: 4px;
//}

.multi_select_children {
  padding-left: 12px;
}


.arrow_icon {
  margin-right: 8px;
  margin-left: auto;
  cursor: pointer;

  &.collapsed {
    transform: rotate(-90deg);
  }
}


.segment_category {
  display: flex;
  align-items: center;
  min-width: 100%;
  padding: 30px 0;
  //margin-top: 30px;
  border-radius: 5px;

  & + & {
    border-top: 1px solid #EEEEEE;
  }
}


.dialog_container {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  width: 450px;
  border-radius: 5px;
  background: $white;
}


.dialog_cross {
  cursor: pointer;
  color: #898B99;

  &:hover {
    color: $error;
  }
}


.dialog_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 42px;
  padding: 24px;
  border-bottom: 1px solid rgba($dark-gray, 0.3);
}


.dialog_body {
  height: auto;
  padding: 24px 24px 0;
}


.dialog_text {
  font-size: 14px;
  margin-bottom: 21px;
}


.popup_button {
  font-size: 14px;
  width: 90px;
  margin: 20px auto;
}


::-webkit-scrollbar-track {
  background: red;
}
