@import "@shared/styles/vars/_vars";


.tooltip_wrapper {
  font-family: $base-font;
  position: absolute;
  z-index: 100;
  display: inline-block;
}


.tooltip_tip {
  font-family: sans-serif;
  font-size: 13px;
  line-height: 1.625;
  width: max-content;
  padding: 12px 16px;
  -webkit-transform: none;
  transform: none;
  animation: growIn 0.15s ease-in-out;
  white-space: pre-line;
  border-radius: 7px;
  background: #0F1725;
  box-shadow: $shadow-light;

}


@keyframes growIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }

}


:global {

  .tooltip-enter-done {
    transition: opacity 200ms ease, transform 0.2s ease;
    transform: scale(1);
    opacity: 1;

    &::before {
      transition: opacity 200ms ease;
      opacity: 1;
    }

  }

  .tooltip-enter-active {
    transform: scale(0.8);
    opacity: 0;

    &::before {
      opacity: 0;
    }
  }

  .tooltip-enter {
    opacity: 0;

    &::before {
      opacity: 0;
    }
  }

  .tooltip-exit {
    transform: scale(0.8);
    opacity: 0;

    &::before {
      opacity: 0;
    }
  }
}

