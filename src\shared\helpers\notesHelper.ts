import { DocumentNote } from '@shared/models/document';

const NOTES_LAST_READ_KEY = 'paperbox_notes_last_read';

/**
 * Get the last read timestamps for notes from localStorage
 * @returns A map of document IDs to last read timestamps
 */
export const getNotesLastReadTimestamps = (): Record<string, number> => {
  const storedTimestamps = localStorage.getItem(NOTES_LAST_READ_KEY);
  if (!storedTimestamps) {
    return {};
  }

  try {
    return JSON.parse(storedTimestamps);
  } catch (error) {
    console.error('Error parsing notes last read timestamps from localStorage', error);
    return {};
  }
};

/**
 * Update the last read timestamp for a document's notes
 * @param docId The document ID
 */
export const updateNotesLastReadTimestamp = (docId: string): void => {
  const timestamps = getNotesLastReadTimestamps();
  timestamps[docId] = Date.now();
  localStorage.setItem(NOTES_LAST_READ_KEY, JSON.stringify(timestamps));
};

/**
 * Check if a document has any unread notes
 * @param docId The document ID
 * @param notes The notes to check
 * @returns True if there are unread notes, false otherwise
 */
export const hasUnreadNotes = (docId: string, notes: DocumentNote[]): boolean => {
  if (!notes || notes.length === 0) {
    return false;
  }

  const timestamps = getNotesLastReadTimestamps();
  const lastReadTimestamp = timestamps[docId];

  // If there's no last read timestamp, all notes are unread
  if (lastReadTimestamp === undefined) {
    return true;
  }

  // Check if any note is newer than the last read timestamp
  return notes.some(note => note.timestamp.getTime() > lastReadTimestamp);
};
