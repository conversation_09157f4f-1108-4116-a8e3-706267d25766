{"actionType": {"alreadyExists": "Un champ de rebond avec ce nom existe déjà.", "config": "Configuration", "dangerZone": "Zone dangereuse", "deleteType": "Supp<PERSON><PERSON> le champ de rebond", "deleteTypeDescription": "Supprimer ce type de rebond le retirera des options que les utilisateurs peuvent sélectionner lors du déclenchement d'une action de rebond.", "error": "Une erreur est survenue, veuillez réessayer.", "id": "Identifiant", "idDescription": "Cela sera utilisé pour identifier le type dans un service connecté. \nPar exemple, Webhook, API, ...", "name": "Nom", "nameDescription": "Le nom qui sera visible par les utilisateurs dans l'interface.", "newOption": "Nouvelle option de rebond", "newType": "Nouveau champ de rebond", "optionAlreadyExists": "Une option avec ce nom existe déjà.", "optionDelete": "Supprimer l'option", "optionDeleteDescription": "Supprimer cette option la retirera des options qu'un utilisateur peut sélectionner lors du déclenchement d'une action de rebond.", "providerId": "Identifiant du fournisseur", "providerIdDescription": "Cela sera utilisé pour identifier le type dans un service connecté, lorsque l'identifiant du fournisseur n'est pas fourni, l'ID normal sera utilisé.", "sensitive": "<PERSON><PERSON><PERSON><PERSON>", "sensitiveDescription": "Lorsqu'un type est marqué comme sensible, Paperbox anonymisera les données.", "valueOptions": "Options autorisées", "valueOptionsDescription": "Options qui seront disponibles pour l'utilisateur lors de la saisie de ce champ.", "valueType": "Type de valeur", "valueTypeDescription": "Changez cette option pour sélectionner le type de valeur de cette option.", "values": "Valeurs"}, "connectors": {"addConnector": "A<PERSON>ter un connecteur", "auth": {"dataConfig": "Configuration des données", "enabled": "Actif", "enabledDescription": "Activer ou désactiver la requête OAuth. Lorsqu'elle est activée, vous pouvez utiliser une requête OAuth supplémentaire pour autoriser chaque requête utilisant ce connecteur.", "headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headersDescription": "Paires clé-valeur à ajouter comme en-têtes dans la requête.", "payload": "Charge utile de la requête OAuth", "payloadDescription": "Ce sont des paires clé-valeur qui formeront la partie données de la requête OAuth.", "queryParams": "Paramètres de requête", "queryParamsDescription": "Ces paramètres seront attachés en tant que paramètres de requête à votre URL d'authentification.", "responseTokenKey": "Clé de jeton de réponse", "responseTokenKeyDescription": "Nom de la clé dans la réponse OAuth qui contient le jeton d'authentification.", "title": "Configuration d'authentification", "url": "URL d'authentification", "urlDescription": "L'URL qui sera utilisée lors de la requête d'authentification."}, "ccs": {"account": "<PERSON><PERSON><PERSON>", "accountDescription": "Le compte utilisé pour s'authentifier sur le serveur CCS.", "password": "Mot de passe", "passwordDescription": "Le mot de passe utilisé pour s'authentifier sur le serveur CCS.", "url": "URL", "urlDescription": "L'URL qui sera utilisée lors des requêtes effectuées avec ce connecteur.", "username": "Nom d'utilisateur", "usernameDescription": "Le nom d'utilisateur utilisé pour s'authentifier sur le serveur CCS."}, "dangerZone": "Zone dangereuse", "dataConfig": "Configuration des données", "delete": "<PERSON><PERSON><PERSON><PERSON> le connecteur", "deleteDescription": "La suppression de ce connecteur peut perturber les connexions existantes qui l'utilisent encore, veuillez vérifier s'il y a des webhooks ou APIs existants qui utilisent toujours ce connecteur.", "environment": "Environnement", "environmentDescription": "L'environnement de l'élément de base de données.", "generalInfo": "Informations générales", "headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headersDescription": "Paires clé-valeur à ajouter comme en-têtes dans toutes les requêtes.", "mailboxes": "Boîtes aux lettres", "ms365": {"addRule": "Ajouter une nouvelle règle", "approve": "Documents approuvés", "bounce": "Documents rejetés", "connected": "Connecté", "connectedTo": "Connecté à", "connectedToMS365": "Connecter avec Microsoft 365", "delete": "Documents supprimés", "description": "<PERSON><PERSON>, vous pouvez approuver Paperbox pour accéder à votre locataire Microsoft 365.", "editMailboxes": "Modifier les boîtes aux lettres connectées", "emailPicker": {"disableAll": "<PERSON><PERSON><PERSON><PERSON> tout", "disabled": "Désactivé", "enableAll": "Activer tout", "enabled": "Actif", "selectEmails": "Sélectionner les comptes de messagerie actifs"}, "inbound": "<PERSON><PERSON><PERSON> d'entrée", "inboundDescription": "Configurez le mappage des emails Microsoft 365 pour les diriger vers des boîtes de réception Paperbox ou des types de documents spécifiques.", "mailbox": "Boîte aux lettres", "outbound": "Destinations de sortie", "outboundDescription": "Configurez le dossier de la boîte de réception Microsoft pour les documents traités dans Paperbox.", "title": "Connexion Microsoft 365", "userPicker": {"selectPath": "Sélectionner un dossier de messagerie"}}, "name": "Nom", "nameDescription": "Le nom utilisé pour identifier le connecteur dans les paramètres de l'espace de travail.", "newConnector": "Nouveau connecteur", "officeId": "ID du bureau", "officeIdDescription": "L'ID de l'élément bureau/base de données auquel vous souhaitez vous connecter.", "payload": "Charge utile de base", "payloadDescription": "Ce sont des paires clé-valeur qui seront ajoutées à la partie données de chaque requête utilisant ce connecteur.", "pi2Key": "Clé PI2", "pi2KeyDescription": "La clé utilisée pour se connecter à la base de données Portima Brio appropriée.", "queryParams": "Paramètres de requête", "queryParamsDescription": "Ces paramètres seront ajoutés en tant que paramètres de requête à l'URL de base.", "save": "Enregistrer les modifications", "saving": "Enregistrement", "sftp": {"ip": "Adresse IP", "ipDescription": "L'adresse IP du serveur SFTP.", "password": "Mot de passe", "passwordDescription": "Le mot de passe utilisé pour s'authentifier sur le serveur SFTP.", "port": "Port", "portDescription": "Le port du serveur SFTP.", "privateKey": "Clé privée", "privateKeyDescription": "La clé privée utilisée pour s'authentifier sur le serveur SFTP.", "username": "Nom d'utilisateur", "usernameDescription": "Le nom d'utilisateur utilisé pour s'authentifier sur le serveur SFTP."}, "subOfficeId": "ID du sous-bureau", "subOfficeIdDescription": "L'ID de l'utilisateur associé à l'élément bureau/base de données.", "title": "Connecteurs", "type": "Type de connecteur", "typeDescription": "Définir le type de connecteur.", "url": "URL de base", "urlDescription": "L'URL de base qui sera utilisée lors des requêtes effectuées à l'aide de ce connecteur."}, "docType": {"add": "Confirmer", "ageThreshold": "Âge maximal du document", "ageThresholdDescription": "Ajustez l'âge maximal qu'un document peut atteindre avant d'être marqué comme en retard.", "alreadyExists": "Un type de document avec ce nom existe déjà.", "approvalChecks": "Contrôles de validation", "approvalThreshold": "Seuil de confiance de classification", "approvalThresholdDescription": "Modifiez le seuil qu'un document de ce type doit atteindre pour être marqué comme 'bon'.", "autoApprove": "Validation automatique", "autoApproveDescription": "Modifiez cette option pour activer/désactiver l'approbation automatique de ce type de document lorsque certains critères sont remplis.", "automation": "Automatisation", "categories": "Catégories de champs", "categoriesDescription": "Celles-ci sont utilisées pour regrouper certains champs dans l'espace de travail d'étiquetage.", "categoriesEdit": {"addFieldDescription": "Sélectionnez les types de champs que vous souhaitez inclure dans cette catégorie.", "addFieldTitle": "Sélectionner les types de champs"}, "config": "Configuration", "dangerZone": "Zone dangereuse", "deleteType": "Supprimer le type de document", "deleteTypeDescription": "Supprimer ce type de document empêchera tout futur document d'être détecté comme ce type. \nLes documents actuels de ce type ne seront pas supprimés.", "docSubTypes": "Sous-types de documents", "docSubTypesNotFound": "Aucun sous-type trouvé", "docSubTypesSearch": "Rechercher des sous-types", "entityTypes": "Types de champs", "entityTypesDescription": "Sélectionnez les types de champs que ce type de document peut utiliser. Ceux-ci peuvent être réorganisés par glisser-déposer.", "fieldAutomation": "Confiance en l'automatisation", "fieldName": "Nom", "fieldOccurrences": "Occurrences uniques", "fieldSearchDescription": "Sélectionnez les types de champs que vous souhaitez inclure dans ce type de document.", "fields": "<PERSON><PERSON>", "fixed": "Type fixe", "fixedDescription": "Lorsque cette option est activée, les utilisateurs ne pourront pas modifier le type de document pour les documents avec ce type, mais pourront modifier le sous-type.", "id": "Identifiant", "idDescription": "Cela sera utilisé pour identifier le type dans un service connecté. \nPar exemple, Webhook, API, ...", "masterdataCheck": "Vérification des données de référence", "masterdataCheckDescription": "Lorsque cette option est activée, Paperbox utilisera la table de données de référence fournie pour rechercher des données sur ce type de document.", "metaSearchDescription": "Sélectionnez les types de métadonnées que vous souhaitez inclure dans ce type de document.", "metadata": "Métadonnées", "metadataKeys": "Types de métadonnées", "metadataKeysDescription": "Sélectionnez les types de métadonnées autorisés assignés à ce type de document. Lorsqu'une option est sélectionnée pour un type, cette valeur sera automatiquement assignée lorsque le document est traité.", "name": "Nom", "nameDescription": "Le nom qui sera visible par les utilisateurs dans l'interface.", "newType": "Nouveau type de document", "ocrThreshold": "Seuil de confiance OCR", "ocrThresholdDescription": "Modifiez le seuil que le texte d'un document de ce type doit atteindre pour être marqué comme 'bon'.", "promptConfig": {"description": "Description", "descriptionDescription": "Ce champ ajoute un contexte pour aider le modèle de GenIA à mieux comprendre et classifier les documents.\nPar exemple, spécifier 'Un document médical ...' pour un rapport de santé aide à une catégorisation plus précise.", "title": "Configuration GenAI"}, "providerId": "Identifiant du fournisseur", "providerIdDescription": "Cela sera utilisé pour identifier le type dans un service connecté, lorsque l'identifiant du fournisseur n'est pas fourni, l'ID normal sera utilisé.", "subType": {"delete": "Supprimer le sous-type", "deleteDescription": "Supprimer ce sous-type empêchera tout futur document d'être détecté comme ce sous-type. \nLes documents actuels de ce sous-type ne seront pas supprimés."}, "type": "Type", "typeDescription": "Détermine les scénarios applicables pour ce type de document."}, "errors": {"401": "Vous n'êtes pas autorisé à effectuer cette action.", "403": "Vous n'avez pas la permission d'effectuer cette action.", "404": "La ressource demandée est introuvable.", "409": "Un élément avec cet ID existe déjà.", "500": "Une erreur s'est produite. Veuillez réessayer plus tard."}, "fieldType": {"alreadyExists": "Un type de champ avec ce nom existe déjà.", "complexFields": "Champs complexes", "complexFieldsDescription": "Définissez les champs qui sont présents en tant qu'éléments dans le champ complexe. Vous pouvez également choisir si le champ est obligatoire ou non.", "config": "Configuration", "dangerZone": "Zone dangereuse", "deleteType": "Supprimer le type de champ", "deleteTypeDescription": "Supprimer ce type de champ empêchera tout futur champ d'être détecté/étiqueté en tant que ce type. Les champs actuels de ce type ne seront pas supprimés.", "groups": {"basic": "Champs de base", "data": "Champs de données", "complex": "Champs complexes", "media": "<PERSON><PERSON> média"}, "id": "Identifiant", "idDescription": "Cela sera utilisé pour identifier le type dans un service connecté. \nPar exemple, Webhook, API, ...", "name": "Nom", "nameDescription": "Le nom qui sera visible par les utilisateurs dans l'interface.", "newType": "Nouveau type de champ", "options": "Valeurs", "optionsDescription": "Les valeurs qui seront disponibles pour l'utilisateur lors de la création d'un champ de ce type.", "promptConfig": {"description": "Description", "descriptionDescription": "Ce champ améliore la capacité du modèle de GenIA à identifier ce champ dans un document.\nPar exemple, spécifier 'Un identifiant de police consiste en 6 à 8 caractères commençant par BE' permet au modèle de reconnaître et de traiter ce champ plus précisément.", "title": "Configuration GenAI"}, "providerId": "Identifiant du fournisseur", "providerIdDescription": "Cela sera utilisé pour identifier le type dans un service connecté, lorsque l'identifiant du fournisseur n'est pas fourni, l'ID normal sera utilisé.", "sensitive": "<PERSON><PERSON><PERSON><PERSON>", "sensitiveDescription": "Lorsqu'un type est marqué comme sensible, Paperbox anonymisera les données.", "tableFields": "Champs de la table", "tableFieldsDescription": "Définissez les champs présents en tant que colonnes dans la table.", "tableMandatory": "Obligatoire", "tableSelectDescription": "Sélectionnez les types de champs que vous souhaitez inclure dans cette table", "tableSelectTitle": "Sélectionner les types de champs", "type": "Type", "typeDescription": "Cette option définit le type de l'entité, utilisé lors de l'étiquetage dans l'interface.", "usage": {"unused": "<PERSON>uti<PERSON><PERSON>", "usedIn": "Utilisé dans :", "andMore": "Et {{count}} autres"}}, "inboxes": {"actionTypes": "Champs de rebond", "actionTypesDescription": "Définissez les options disponibles que les utilisateurs peuvent sélectionner lorsqu'ils renvoient un document.", "addInbox": "Ajouter une boîte de réception", "autoDelete": {"actionType": "Type d'action", "actionTypeDescription": "Choisissez l'action à effectuer lorsqu'un document est traité, choisissez entre 'Rejet', 'Suppression', 'Validation' ou 'aucune action'. \n\nLorsque l'option 'aucune action' est sélectionnée, un webhook ne sera pas déclenché.", "enabled": "Règles de nettoyage", "enabledDescription": "Avec les règles de nettoyage, vous pouvez traiter automatiquement les documents après un laps de temps prédéfini. Vous pouvez l'activer/désactiver ici.", "options": {"approve": "Validation", "bounce": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "noAction": "Aucune action"}, "time": "Période de temps", "timeDescription": "Spécifiez la période après laquelle les documents seront automatiquement traités."}, "createInbox": "<PERSON><PERSON><PERSON> une boîte de ré<PERSON>", "days": "jours", "docTypes": "Types de documents", "docTypesDescription": "Définissez les types utilisés pour classifier chaque document, divisés en trois catégories : types de liasses, types de documents, et types de courriers.", "documentRetentionTime": "Durée de conservation des documents", "documentRetentionTimeDescription": "Le nombre de jours pendant lesquels les documents seront conservés par Paperbox après leur traitement.", "fieldTypes": "Types de champs", "fieldTypesDescription": "Configurez les champs que Paperbox doit extraire du document et qui seront disponibles dans l'écran d'étiquetage des documents.", "id": "Identifiant", "idDescription": "Utilisé pour identifier la boîte de réception dans nos API.", "inboxMove": {"enabled": "Utiliser la liste blanche", "enabledDescription": "Lorsque cette fonctionnalité est activée, les utilisateurs ne pourront déplacer les documents qu'aux boîtes de réception spécifiées dans la liste ci-dessous.", "list": "Boîtes de réception en liste blanche", "listDescription": "Sélectionnez les boîtes de réception vers lesquelles les utilisateurs seront autorisés à déplacer les documents. Pa<PERSON> d<PERSON><PERSON><PERSON>, toutes les boîtes de réception sont en liste blanche.", "title": "Déplacement de la boîte de réception des documents"}, "inboxSettings": "Paramètres de la boîte de réception", "masterdata": "<PERSON><PERSON><PERSON> de référence", "masterdataDescription": "Toute la configuration liée à la recherche et à la consultation dans les tables de données de référence. Les tables de données de référence peuvent être téléchargées via les paramètres de l'espace de travail ou via l'API d'intégration de Paperbox (voir <1>https://docs.paperbox.ai</1>).", "metadataTypes": "Types de métadonnées", "metadataTypesDescription": "Configurez les champs que Paperbox ne doit pas extraire directement du document, mais qui seront disponibles dans la sortie et/ou pour la recherche.", "name": "Nom", "nameDescription": "Le nom qui sera visible par les utilisateurs dans l'interface.", "saveChanges": "Enregistrer les modifications", "saving": "Enregistrement", "sections": {"actionTypeDelete": "Êtes-vous sûr de vouloir supprimer ce champ de rebond ?", "actionTypeOptionDelete": "Êtes-vous sûr de vouloir supprimer cette option de champ de rebond ?", "actionTypeSearch": "Rechercher des champs de rebond", "addNew": "Ajouter un nouveau", "docTypeDelete": "Êtes-vous sûr de vouloir supprimer ce type de document ?", "docTypeMultiDelete": "Êtes-vous sûr de vouloir supprimer ces types de documents ?", "docTypeSearch": "Rechercher des types de documents", "fieldTypeDelete": "Êtes-vous sûr de vouloir supprimer ce type de champ ?", "fieldTypeMultiDelete": "Êtes-vous sûr de vouloir supprimer ces types de champs ?", "fieldTypeSearch": "Rechercher des types de champs", "masterdataSearch": "Rechercher la liste des tables", "masterdataTableDelete": "Êtes-vous sûr de vouloir supprimer cette table de données maîtres ?", "metadataTypeDelete": "Êtes-vous sûr de vouloir supprimer ce type de métadonnée ?", "metadataTypeSearch": "Rechercher des types de métadonnées", "noActionTypeFound": "Aucun champ de rebond trouvé", "noDocTypeFound": "Aucun type de document trouvé", "noFieldTypeFound": "Aucun type de champ trouvé", "noMasterdataFound": "Aucune table de données de référence trouvée", "noMetadataTypeFound": "Aucun type de métadonnée trouvé", "noTagTypeFound": "Aucun tag trouvé", "tagTypeDelete": "Êtes-vous sûr de vouloir supprimer ce tag ?", "tagTypeMultiDelete": "Êtes-vous sûr de vouloir supprimer ces tags ?", "tagTypeSearch": "Rechercher un tag"}, "settings": {"autoAdvance": "Avancement automatique", "autoAdvanceDesc": "Cette fonctionnalité permet aux utilisateurs de passer automatiquement d'un document au suivant dans la file d'attente une fois un document terminé, augmentant ainsi l'efficacité du traitement des documents.", "bounce": "Rejet de document", "bounceDesc": "La fonctionnalité de rejet de document fournit une option pour rejeter un document et le renvoyer. Cela est utile lorsqu'un document ne contient pas les informations nécessaires pour être correctement traité.", "documentCopy": "Copie de document", "documentCopyDesc": "La fonctionnalité de copie de document permet aux utilisateurs de créer des doublons d'un document. Cela est utile pour créer différentes versions d'un document qui pourraient nécessiter un traitement distinct.", "documentDownload": "Téléchargement de documents", "documentDownloadDesc": "Cette fonctionnalité permet aux utilisateurs de télécharger des documents directement sur leur machine locale pour une utilisation hors ligne ou une analyse plus approfondie.", "documentTransform": "Transformations de documents", "documentTransformDesc": "Cette fonctionnalité permet aux utilisateurs de diviser des sections spécifiques d'un lot de documents, comme une pièce jointe à un e-mail, et de classer chaque section individuellement dans le même lot. En outre, lorsque la fonctionnalité de copie de document est activée, les utilisateurs peuvent créer des copies partielles en excluant certaines parties du document.", "fileUpload": "Téléchargement via l'interface", "fileUploadDesc": "Permet aux utilisateurs de télécharger des documents directement depuis l'interface de Paperbox, utile à des fins de test.", "labelingMode": "Mode d'étiquetage", "labelingModeDesc": "En mode d'étiquetage, l'espace de travail modifie divers paramètres pour être optimisé afin d'étiqueter efficacement et de revoir les documents étiquetés pour un entraînement ultérieur.", "mailroom": "<PERSON><PERSON><PERSON> de ré<PERSON> 'Mailroom'", "mailroomDesc": "Activer le mode 'Mailroom' pour cette boîte de réception."}, "tableCols": "Colonnes de la table de la boîte de réception", "tableColsDescription": "Configurez les colonnes qui seront visibles pour les utilisateurs sur l'écran de vue d'ensemble des documents de Paperbox.", "tagTypes": "Tags", "tagTypesDescription": "Les tags peuvent être utilisés pour marquer les documents avec un état spécifique (par exemple : Priorité, Statut, ...). Vous pouvez également utiliser ces tags pour filtrer vos documents.", "title": "Configuration de la boîte de réception", "uploadMail": "Adresse e-mail de téléchargement de documents", "uploadMailDescription": "Adresse e-mail associée où les documents peuvent être téléchargés pour être traités par Paperbox.", "workflow": "Workflow actif", "workflowDescription": "Version du workflow actuellement active sur cette boîte de réception."}, "masterdata": {"boost": "Priorité", "boostDescription": "Définit la pertinence de cette table par rapport aux autres tables. \nLors de la consultation et de la recherche, la table avec la priorité la plus élevée sera affichée en premier ou priorisée. (0 - 10)", "config": "Configuration", "copyId": "ID de la table de données de référence copié dans le presse-papiers.", "dangerZone": "Zone dangereuse", "delete": "Supprimer la table de données maîtres", "deleteDescription": "Supprimer cette table supprimera toutes les données de Paperbox. Cette action est irréversible.", "downloadCSV": "Télécharger CSV", "downloadCSVDescription": "Télécharger les données qui ont été téléchargées pour la dernière fois dans cette table au format CSV.", "errorExists": "Une table de données de référence portant ce nom existe déjà.", "errorGeneric": "Une erreur s'est produite, veuillez réessayer plus tard.", "errorMismatch": "Incompatibilité de colonne dans le fichier téléchargé. Veuillez vérifier et réessayer.", "fileSelect": "Sé<PERSON><PERSON><PERSON> le fichier", "fileUpload": "Télécharger", "fileUploaded": "Téléchargement réussi", "fileUploading": "Téléchargement en cours", "headerChars": "Caractères filtrés", "headerCharsInput": "Entrez un caractère", "headerCharsTooltip": "Ajou<PERSON>z les caractères qui doivent être filtrés lors de la recherche dans cette colonne spécifique.", "headerDisplay": "Champ / Nom d'affichage", "headerDisplayTooltip": "Lorsqu'aucun champ ou nom d'affichage n'est configuré\ncette colonne de données de référence sera ignorée.", "headerLabel": "Étiquette de la table", "headerLabelTooltip": "Nom de la colonne dans les données téléchargées", "headerMapping": "Mappage", "headerMappingTooltip": "Bascule entre :\n- Non mappé / Caché\n- Nom d'affichage de base\n- Champ Paperbox", "headerPin": "Épingler l'élément", "headerPinTooltip": "<PERSON><PERSON><PERSON> un élément le fera apparaître plus haut dans les résultats de recherche.", "headerSearchable": "Recherchable", "headerSearchableTooltip": "Lors de l'utilisation de la recherche de données de référence, cette colonne sera consultable.", "headerType": "Type", "headerTypeTooltip": "Bascule entre les types optimisés pour la recherche :\n- Identifiants (par exemple, un ID de réclamation ou un numéro SAP)\n- Texte intégral (par exemple, une adresse ou un nom)", "id": "Identifiant", "idDescription": "Ceci sera utilisé pour identifier la table dans un service connecté. \nPar exemple, Webhook, API, ...", "latestUpload": "Dernier téléchargement", "latestUploadDescription": "Indique la date de la dernière mise à jour des données pour cette table.", "lookup": "Consultation", "lookupDescription": "Lors<PERSON><PERSON>'elle est activée, les données de cette table seront utilisées pendant le processus de traitement des documents.", "name": "Tables de données maîtres", "tableMapping": "Mappage de la table", "tableMappingDescription": "<PERSON><PERSON>, vous pouvez configurer le mappage des données de votre table de données de référence afin qu'elles puissent être utilisées dans Paperbox.", "tableSearch": "<PERSON><PERSON><PERSON>", "tableSearchDescription": "<PERSON><PERSON><PERSON>", "title": "Ajouter une table", "totalLineCount": "Nombre de lignes", "totalLineCountDescription": "Indique le nombre total de lignes qui ont été ajoutées à cette table lors du dernier téléchargement.", "uploadData": "Télécharger de nouvelles données", "uploadDataDescription": "Permet de télécharger de nouvelles données pour cette table qui - après traitement - seront accessibles aux utilisateurs."}, "metadataType": {"alreadyExists": "Un type de métadonnée avec ce nom existe déjà.", "config": "Configuration", "dangerZone": "Zone dangereuse", "deleteType": "Supprimer le type de métadonnée", "deleteTypeDescription": "Supprimer ce type de métadonnée le supprimera de la liste des types de métadonnées pouvant être ajoutés au document. \nLes documents actuels contenant ce type de métadonnée ne seront pas supprimés.", "error": "Une erreur est survenue, veuillez réessayer.", "hidden": "Interne", "hiddenDescription": "Lorsq<PERSON>'elle est désignée comme 'interne', cette métadonnée reste cachée des utilisateurs dans l'interface. Cela est particulièrement utile pour les identifiants internes et les métadonnées supplémentaires pertinentes pour les systèmes en aval, mais non pertinentes pour les utilisateurs.", "id": "Identifiant", "idDescription": "Cela sera utilisé pour identifier le type dans un service connecté. \nPar exemple, Webhook, API, ...", "name": "Nom", "nameDescription": "Le nom qui sera visible par les utilisateurs dans l'interface.", "newType": "Nouveau type de métadonnée", "providerId": "Identifiant du fournisseur", "providerIdDescription": "Cela sera utilisé pour identifier le type dans un service connecté, lorsque l'identifiant du fournisseur n'est pas fourni, l'ID normal sera utilisé.", "sensitive": "<PERSON><PERSON><PERSON><PERSON>", "sensitiveDescription": "Lorsqu'un type est marqué comme sensible, Paperbox anonymisera les données.", "useTopologyPartsToAggregate": "Agrégation en métadonnée de liasse", "useTopologyPartsToAggregateDescription": "Lorsque cette option est activée, Paperbox combinera toutes les valeurs mappées de ce champ de métadonnées au sein d'une liasse en un seul champ, en utilisant l'ordre des options spécifié.\nCela est utile pour des scénarios comme l'utilisation de la valeur de priorité la plus élevée d'un champ de métadonnées dans toutes les parties d'une liasse.", "valueType": "Type de valeur", "valueTypeDescription": "Le type de la valeur", "values": {"addDescription": "Entrez la valeur que vous souhaitez rendre disponible en tant qu'option.", "addNew": "Ajouter une nouvelle", "addTitle": "Entrer une nouvelle valeur", "description": "Voici les choix dont vous disposerez lors de la configuration de ce champ de métadonnée pour un type de document.\nL'ordre dans lequel vous les organisez déterminera leur priorité en cas de conflit.", "error": "L'option de métadonnée existe déjà.", "title": "Valeurs"}}, "multiSave": {"changeLogDescription": "Ces champs seront modifiés sur {{number}} élément(s).", "description": "Seuls les champs que vous avez modifiés seront changés, ces modifications seront appliquées à {{number}} élément(s).", "title": "Êtes-vous sûr ?", "unsavedChangeLogDescription": "Les modifications des champs suivants seront perdues."}, "multiSelect": {"confirm": "Confirmer", "deselect": "<PERSON><PERSON>", "select": "<PERSON><PERSON>"}, "page": {"backToOverview": "Retour à l'aperçu", "next": "Suivant", "previous": "Précédent"}, "tagType": {"alreadyExists": "Un tag avec ce nom existe déjà.", "color": "<PERSON><PERSON><PERSON>", "colorDescription": "Cela sera utilisé pour distinguer visuellement les tags dans l'interface.", "config": "Configuration", "dangerZone": "Zone dangereuse", "deleteType": "Supprimer le tag", "deleteTypeDescription": "Supprimer ce tag le retirera de la liste des tags pouvant être ajoutés à un document. \nLes documents actuels contenant ce tag ne seront pas supprimés.", "error": "Une erreur est survenue, veuillez réessayer.", "id": "Identifiant", "idDescription": "Cela sera utilisé pour identifier le type dans un service connecté. \nPar exemple, Webhook, API, ...", "name": "Nom", "nameDescription": "Le nom qui sera visible par les utilisateurs dans l'interface.", "newType": "Nouveau tag", "providerId": "Identifiant du fournisseur", "providerIdDescription": "Cela sera utilisé pour identifier le type dans un service connecté, lorsque l'identifiant du fournisseur n'est pas fourni, l'ID normal sera utilisé."}, "tenant": {"apiKey": "Clé API", "apiKeyDescription": "Récupérez la clé API qui peut être utilisée pour authentifier les requêtes API à cet espace de travail. <1/><0>Consultez notre documentation pour plus d'informations.</0>", "domains": "Domaines en liste blanche", "domainsAddDescription": "Veuillez entrer ci-dessous un nouveau nom de domaine que vous souhaitez ajouter à la liste blanche. ex. gmail.com", "domainsAddTitle": "Ajouter un domaine", "domainsDescription": "Définir des domaines dans cette liste blanche permettra aux utilisateurs ayant ce domaine e-mail de se connecter à l'espace de travail.", "fetchApiKey": "Récupérer la clé API", "fetchPrivateKey": "Récupérer la clé privée", "general": "Paramètres généraux", "id": "Identifiant", "idDescription": "L'ID de cet espace de travail", "language": "Langue par défaut", "languageDescription": "Vous pouvez ici configurer la langue par défaut qui sera utilisée dans l'interface pour les utilisateurs, ces derniers pourront changer leur langue ultérieurement.", "locale": "Région", "name": "Nom", "nameDescription": "Configurez le nom de votre espace de travail, il sera utilisé dans les emails personnalisés, les réinitialisations de mot de passe, les invitations aux utilisateurs, etc.", "netlifyBeta": "Fonctionnalités bêta", "netlifyBetaDescription": "Activer/Désactiver les fonctionnalités bêta de Paperbox pour tous les utilisateurs de ce locataire. Les modifications prendront effet après rechargement de la page.", "privateKey": "Clé privée", "privateKeyDescription": "Récupérez la clé privée qui peut être utilisée pour récupérer en toute sécurité les jetons JWT pour l'authentification. <1/><0>Consultez notre documentation pour plus d'informations.</0>", "timeZone": "<PERSON><PERSON> ho<PERSON>", "timeZoneDescription": "Définir le fuseau horaire de votre espace de travail affectera le tableau de bord et les rapports.", "title": "Paramètres de l'espace de travail"}, "unsavedChanges": {"description": "Il semble que vous ayez modifié quelque chose. Si vous partez sans enregistrer, vos modifications seront perdues.", "title": "Modifications non enregistrées"}, "users": {"adminAccess": "Accès administrateur", "createUser": "Ajouter un utilisateur", "creatingUser": "Création", "deleteText": "Cette action est irréversible. L'utilisateur ne pourra plus se connecter tant qu'un nouveau compte n'aura pas été créé.", "emailSent": "Email envoy<PERSON> à", "filter": "Filtrer les utilisateurs", "inboxes": {"addDescription": "Sélectionnez les boîtes de réception à attribuer à cet utilisateur.", "addTitle": "Attribuer des boîtes de réception", "title": "Boî<PERSON> de ré<PERSON>"}, "resetText": "<PERSON><PERSON> enverra un e-mail de réinitialisation du mot de passe à l'utilisateur sélectionné.", "success": "Su<PERSON>ès", "title": "Gestion des utilisateurs"}, "webhookValues": {"@ACTION": "L'action qui a été effectuée sur le document. Peut être 'approuver', 'supprimer' ou 'rejeter'.", "@ACTION_METADATA": "Les métadonnées de l'action qui a été effectuée sur le document.", "@ACTOR": "<PERSON><PERSON> de l'acteur, peut être soit 'paperbox' soit un utilisateur.", "@CONFIDENCE": "Le niveau de confiance auquel un document a été traité. Peut être une valeur de 0 à 1.", "@DOCUMENT_CLASS": "Le type de document du document traité.", "@DOCUMENT_SUBCLASS": "Le sous-type de document du document traité.", "@ENTITIES": "Les types de champs du document traité.", "@ID": "ID du document fourni à Paperbox lors de l'ingestion.", "@METADATA": "Les métadonnées du document traité.", "@MUTATION_ID": "L'ID d'une mutation si le document traité n'est pas l'original.", "@MUTATION_TYPE": "Le type de mutation si le document traité n'est pas l'original.", "@PAPERBOX_ID": "L'ID Paperbox du document.", "@PB_JSON_PAYLOAD": "Charge utile JSON par défaut de Paperbox", "@PB_PRIMITIVE_JSON_PAYLOAD": "Charge utile JSON primitive de Paperbox", "@PB_RANDOM_UUIDv4": "Un UUIDv4 généré aléatoirement", "@PB_STRING_PAYLOAD": "Charge utile Paperbox sous forme de chaîne", "@PROCESSED_DATE": "Chaîne ISO indiquant quand le document a été traité.", "placeholder": "Insérer des données ou du texte d'entrée"}, "webhooks": {"BRIO_NOT_SUPPORTED": "Configuration du webhook Brio non encore supportée", "add": "Ajouter un Webhook", "auth": {"customPayload": "Charge utile de la requête OAuth", "customPayloadDescription": "Ce sont des paires clé-valeur qui formeront la partie données de la requête OAuth.", "dataConfig": "Configuration des données", "extend": "<PERSON><PERSON><PERSON> la configuration d'authentification du connecteur", "extendDescription": "Activez cette option pour développer les paramètres déjà configurés dans votre configuration de connecteur.", "headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headersDescription": "Paires clé-valeur à ajouter comme en-têtes dans la requête.", "queryParams": "Paramètres de requête", "queryParamsDescription": "Ces paramètres seront ajoutés en tant que paramètres de requête à votre URL.", "responseTokenKey": "Clé de jeton de réponse", "responseTokenKeyDescription": "Nom de la clé dans la réponse OAuth qui contient le jeton d'authentification.", "title": "Configuration OAuth", "url": "URL d'authentification", "urlDescription": "L'URL qui sera utilisée lors de la requête d'authentification."}, "brio": {"activitySettings": {"description": "Configurer les paramètres par défaut pour les nouvelles activités", "rds": "Service par Défaut", "rdsDescription": "Saisissez l'ID du service Brio par défaut qui doit être utilisé lorsque le champ administrateur est vide.", "title": "Paramètres d'Activité"}, "docTypeConfig": {"add": "Ajouter un type de document", "addDescription": "Ajouter un nouveau type de document à la configuration Brio.", "addTitle": "Ajouter un nouveau type de document", "createActivity": "Créer une activité", "description": "Configurer le traitement des différents types de documents vers BRIO.", "docType": "Type de document", "hierarchy": "Hiérarchie préf<PERSON>", "options": {"claims": "Réclamations", "contracts": "Contrats", "documents": "Documents", "parties": "Parties"}, "title": "Configuration du type de document"}, "metadataActivityMapping": {"archive_code": "Code d'archive", "archive_codeDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme code d'archive dans Brio.", "description": "Description", "descriptionDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme description de l'activité dans Brio.", "due_date_days_delta": "<PERSON><PERSON>t de jours de la date d'échéance", "due_date_days_deltaDescription": "Sélectionnez le type de métadonnée dont la valeur numérique sera ajoutée en jours à la date de réception du document pour déterminer la date d'échéance de l'activité.", "handler": "Responsable", "handlerDescription": "Sélectionnez le type de métadonnée dont la valeur déterminera l'utilisateur Brio auquel l'activité est assignée.", "is_completed": "Est complété", "is_completedDescription": "Sélectionnez le type de métadonnée dont la valeur indiquera si cet élément est complété dans Brio.", "is_visible_my_broker": "Visible dans Mon Courtier", "is_visible_my_brokerDescription": "Sélectionnez le type de métadonnée dont la valeur indiquera si une activité sera visible dans MonCourtier depuis Brio.", "priority": "Priorité", "priorityDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme priorité de l'activité dans Brio.", "sub_type": "Sous-type", "sub_typeDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme sous-type dans Brio.", "title": "Mapper les métadonnées configurées de Paperbox aux valeurs d'activité correspondantes dans Brio", "type": "Type", "typeDescription": "Sélectionnez le type de métadonnée dont la valeur déterminera le type d'activité créée."}, "metadataDocumentMapping": {"archive_delay": "<PERSON><PERSON><PERSON>'archivage", "archive_delayDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme délai d'archivage dans Brio.", "carrier_type": "Support de document", "carrier_typeDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme le support du document dans Brio.", "category": "<PERSON><PERSON><PERSON><PERSON>", "categoryDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme catégorie dans Brio; basé sur la table 962 de Brio.", "delete_delay": "<PERSON><PERSON><PERSON>", "delete_delayDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme délai de suppression dans Brio.", "description": "Description", "descriptionDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme description du document dans Brio.", "description_my_broker": "Description Mon <PERSON>", "description_my_brokerDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme description du courtier dans Brio.", "is_favorite": "Est favori", "is_favoriteDescription": "Sélectionnez le type de métadonnée dont la valeur indiquera si ce document est un favori dans Brio.", "is_visible_my_broker": "Visibilité du document MonCourtier", "is_visible_my_brokerDescription": "Sélectionnez le type de métadonnée dont la valeur indiquera si un document sera visible dans MonCourtier depuis Brio.", "language": "<PERSON><PERSON>", "languageDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme langue dans Brio.", "origin": "Origine", "originDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme origine dans Brio; Le document est-il reçu ou envoyé.", "qualifier": "Type de document Brio", "qualifierDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme type de document Brio; basé sur la table 970 de Brio.", "reference": "Référence", "referenceDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme référence dans Brio.", "security_level": "Niveau de sécurité", "security_levelDescription": "Sélectionnez le type de métadonnée dont la valeur apparaîtra comme niveau de sécurité dans Brio.", "title": "Configurer les mappages entre les champs de métadonnées Paperbox et les champs Brio, afin que lors du traitement d'un document, les valeurs de métadonnées spécifiées soient automatiquement renseignées dans Brio."}, "metadataHierarchy": {"description": "Mapper les champs configurés de Paperbox depuis les données référentielles vers les emplacements BRIO corrects.", "title": "Mappage des données référentielles"}}, "brioEndpointId": "ID de point de terminaison Brio", "brioEndpointIdDescription": "L'ID interne du point de terminaison Brio à utiliser pour ce webhook.", "ccs": {"agendaMapping": {"description": "Mapper les métadonnées de Paperbox aux champs de l'agenda CCS pour un peuplement automatique.", "fields": {"create_agenda": "<PERSON><PERSON>er un agenda", "create_agendaDescription": "Sélectionnez le type de métadonnées pour déterminer si un agenda doit être créé.", "description": "Description", "descriptionDescription": "Sélectionnez le type de métadonnées pour la description de l'agenda dans CCS.", "due_date_delta": "Delta de la date d'échéance", "due_date_deltaDescription": "", "employee_number": "Numéro <PERSON>", "employee_numberDescription": "Sélectionnez le type de métadonnées pour le numéro d'employé.", "reason": "<PERSON>son", "reasonDescription": "Sélectionnez le type de métadonnées pour le motif de l'agenda dans CCS."}, "title": "Mapping de l'agenda CCS"}, "archiveMapping": {"description": "Mapper les métadonnées de Paperbox aux champs d'archivage CCS", "fields": {"description": "Description", "descriptionDescription": "Sélectionnez le type de métadonnées pour la description de l'archive dans CCS.", "document_type": "Type de document", "document_typeDescription": "Sélectionnez le type de métadonnées pour le type de document dans CCS.", "is_secretDescription": "Sélectionnez le type de métadonnées pour déterminer si l'archive est marquée comme 'secrète'."}, "title": "Mapping de l'archive CCS"}, "claimMapping": {"description": "Mapper les métadonnées de Paperbox aux champs de réclamation CCS", "fields": {"claim_number_office": "Bureau du numéro de réclamation\n", "claim_number_officeDescription": "Sélectionnez le type de métadonnées pour le numéro de réclamation de bureau dans CCS.", "company_number": "Numéro d'entreprise", "company_numberDescription": "Sélectionnez le type de métadonnées pour le numéro d'entreprise dans CCS.", "intermediary_person_number": "Numéro de personne intermédiaire", "intermediary_person_numberDescription": "Sélectionnez le type de métadonnées pour le numéro de personne intermédiaire dans CCS."}, "title": "Mapping des réclamations CCS"}, "docTypeConfig": {"add": "Ajouter un type de document", "addDescription": "Ajouter un nouveau type de document à la configuration CCS", "addTitle": "Ajouter un nouveau type de document", "agendaReason": "<PERSON><PERSON><PERSON> de l'agenda", "description": "Configurer le traitement des différents types de documents en CSS.", "docType": "Type de document", "title": "Configuration du type de document"}}, "connection": "Paramètres de connexion", "connector": "Connecteur", "connectorDescription": "Sélectionnez quel connecteur est utilisé pour ce webhook. Toutes les requêtes du webhook utiliseront les paramètres/configurations définis dans le connecteur.", "customPayload": "Charge utile personnalisée", "customPayloadDescription": "Ce sont des paires clé-valeur qui formeront la partie données de la requête.", "dangerZone": "Zone dangereuse", "dataConfig": "Configuration des données", "dataExtend": "<PERSON><PERSON><PERSON> la configuration des données du connecteur", "dataExtendDescription": "Activez cette option pour développer les paramètres déjà configurés dans votre connecteur.", "defaultPayload": "Utiliser la charge utile par défaut", "defaultPayloadDescription": "Lors de l'activation de cette option, la charge utile de la requête sera la charge utile par défaut définie par Paperbox.", "delete": "Supp<PERSON><PERSON> le webhook", "deleteDescription": "La suppression d'un webhook actif entraînera la rupture de toutes les connexions existantes avec le système connecté, veuillez vous assurer que vous n'avez plus besoin de ce webhook avant de le supprimer.", "dependsOn": "Dépendances", "dependsOnDescription": "Sélectionnez les webhooks qui doivent être déclenchés avant que ce webhook puisse être déclenché.", "enabled": "Actif", "enabledDescription": "<PERSON>r ou dés<PERSON>r le webhook. Lorsqu'il est désactivé, il ne sera plus utilisé.", "generalInfo": "Informations générales", "headers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "headersDescription": "Paires clé-valeur à ajouter comme en-têtes dans toutes les requêtes.", "id": "Identifiant", "idDescription": "L'ID interne de ce webhook.", "name": "Nom", "nameDescription": "Le nom utilisé pour identifier le webhook dans l'interface.", "queryParams": "Paramètres de requête", "queryParamsDescription": "Ces paramètres seront ajoutés en tant que paramètres de requête à votre URL.", "request": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "Enregistrer les modifications", "saving": "Enregistrement", "selectDocTypes": "Sélectionner les types de documents", "selectDocTypesDescription": "Choisissez les types de documents (bundle) auxquels ce webhook s’applique.", "selectInboxes": "Sélectionner les boîtes de réception", "selectInboxesDescription": "Choisissez les boîtes de réception auxquelles ce webhook s’applique.", "selectTables": "Sélectionner les tables", "selectTablesDescription": "Choisissez les tables liées auxquelles ce webhook s’applique.", "title": "Configuration du Webhook", "triggerActionTypes": "Types d'action déclencheurs", "triggerActionTypesDescription": "Le webhook ne se déclenchera que pour les types d'action sélectionnés lorsqu'il est déclenché par l'événement d'action. Par défa<PERSON>, tous les types d'action.", "triggerDocTypes": "Types de documents", "triggerDocTypesDescription": "Choisissez les types de documents (bundle) auxquels ce webhook s’applique.", "triggerEvent": "<PERSON><PERSON><PERSON><PERSON>", "triggerEventDescription": "Spécifiez les événements qui déclenchent ce webhook. Le webhook ne sera déclenché que lors des événements sélectionnés.", "triggerInboxes": "Boîtes de réception d<PERSON>", "triggerInboxesDescription": "Définissez les boîtes de réception sur lesquelles ce webhook doit être déclenché.", "triggerTables": "Tables", "triggers": "<PERSON><PERSON><PERSON>nch<PERSON><PERSON>", "unflatten": "Désimbriquer", "unflattenDescription": "Par dé<PERSON><PERSON>, nous convertissons les champs avec des doubles underscores en dictionnaires imbriqués. Désactivez cette option pour désactiver ce comportement.", "url": "Chemin d'URL", "urlDescription": "Le chemin qui sera utilisé pour le webhook, ce chemin sera 'attaché' à l'URL de base définie dans votre connecteur sélectionné."}}