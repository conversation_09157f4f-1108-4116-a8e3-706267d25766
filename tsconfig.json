{"compilerOptions": {"target": "ES2021", "useDefineForClassFields": true, "lib": ["ES2021", "dom", "dom.iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": false, "noImplicitThis": true, "strictNullChecks": false, "types": ["vite/client", "node"], "baseUrl": "./", "paths": {"@src/*": ["src/*"], "@components/*": ["src/components/*"], "@shared/*": ["src/shared/*"], "@svg/*": ["src/shared/assets/svg/*"]}}, "include": ["src", "./src/shared/types/*", "vite-env.d.ts"], "references": [{"path": "./tsconfig.node.json"}]}