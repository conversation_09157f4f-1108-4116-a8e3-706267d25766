import DynamicMappingTable, {
  ColumnDefinition,
  RowData,
} from '@components/admin/components/DynamicMappingTable.tsx';
import {
  EvaluationEngine,
  IDerivedMapping,
  IDerivedProperty,
  MappingType,
} from '@shared/helpers/converters/datasource.ts';
import { cloneDeep } from 'lodash';
import React from 'react';

interface Props {
  derivedMappings: IDerivedMapping;
  onChange: (derivedMappings: IDerivedMapping) => void;
  title: string;
  description: string;
}

const AdminDatasourceDerivedMappingEditor: React.FC<Props> = ({
  derivedMappings,
  onChange,
  title,
  description,
}) => {
  // Column definitions for the derived mapping table
  const columns: ColumnDefinition[] = [
    {
      key: 'name',
      label: 'Field Name',
      tooltip: 'The name of the derived field',
      type: 'text',
      width: 'minmax(150px,1fr)',
      placeholder: 'Field name',
    },
    {
      key: 'type',
      label: 'Type',
      tooltip: 'The data type for this derived field',
      type: 'dropdown',
      width: '120px',
      align: 'center',
      options: [
        { label: 'Text', value: 'text' },
        { label: 'Number', value: 'number' },
        { label: 'Boolean', value: 'boolean' },
        { label: 'Date', value: 'date' },
        { label: 'Object', value: 'object' },
        { label: 'ID', value: 'id' },
        { label: 'Nested', value: 'nested' },
      ],
    },
    {
      key: 'evaluation_engine',
      label: 'Engine',
      tooltip: 'The evaluation engine (JEXL or Jinja)',
      type: 'dropdown',
      align: 'center',
      options: [
        { label: 'JEXL', value: 'jexl' },
        { label: 'Jinja', value: 'jinja' },
      ],
    },
    {
      key: 'evaluation',
      label: 'Expression',
      tooltip: 'The evaluation expression for this derived field',
      type: 'text',
      width: 'minmax(200px, 1fr)',
      placeholder: 'e.g., data.field1 + data.field2',
    },
    {
      hideHeader: true,
      width: 'auto',
      key: 'is_searchable',
      label: 'Searchable',
      tooltip: 'Whether this derived field can be searched',
      type: 'toggle',
      align: 'center',
    },
    {
      hideHeader: true,
      width: 'auto',
      key: 'chars_to_filter',
      label: 'Filter Chars',
      tooltip: 'Characters to filter from this field',
      type: 'chars-filter',
    },
    {
      hideHeader: true,
      width: 'auto',
      key: 'actions',
      label: 'Actions',
      tooltip: 'Delete this derived field',
      type: 'button',
      align: 'center',
    },
  ];

  const isDerivedProperty = (value: IDerivedProperty | IDerivedMapping): value is IDerivedProperty => {
    return value && typeof value === 'object' && 'evaluation' in value;
  };

  // Convert derived mappings to row data for the dynamic table
  const convertDerivedMappingsToRows = (mapping: IDerivedMapping, parentPath = '', level = 0): RowData[] => {
    const result: RowData[] = [];

    Object.entries(mapping).forEach(([key, value]) => {
      const currentPath = parentPath ? `${parentPath}.${key}` : key;

      if (isDerivedProperty(value)) {
        result.push({
          id: currentPath,
          name: key,
          type: value.type,
          evaluation_engine: value.evaluation_engine,
          evaluation: value.evaluation,
          is_searchable: value.is_searchable || false,
          chars_to_filter: value.chars_to_filter || [],
          level,
        });
      } else {
        // For nested mappings, flatten recursively
        result.push(...convertDerivedMappingsToRows(value, currentPath, level + 1));
      }
    });

    return result;
  };

  const rows = convertDerivedMappingsToRows(derivedMappings);

  const handleAddRow = () => {
    const newMappings = cloneDeep(derivedMappings);
    const fieldName = `new_derived_field_${Date.now()}`;

    newMappings[fieldName] = {
      type: MappingType.TEXT,
      evaluation: '',
      evaluation_engine: EvaluationEngine.JEXL,
      is_searchable: false,
      chars_to_filter: null,
    };

    onChange(newMappings);
  };

  const handleRowChange = (rowId: string, field: string, value: any) => {
    const newMappings = cloneDeep(derivedMappings);
    const pathParts = rowId.split('.');
    const fieldName = pathParts.pop();

    let current = newMappings;
    for (const part of pathParts) {
      current = current[part] as IDerivedMapping;
    }

    if (field === 'name') {
      // Handle field renaming
      if (!value.trim() || value === fieldName) return;
      if (current[value]) return; // Name already exists

      current[value] = current[fieldName];
      delete current[fieldName];
    } else if (isDerivedProperty(current[fieldName])) {
      // Handle other field updates
      if (field === 'chars_to_filter') {
        (current[fieldName] as IDerivedProperty).chars_to_filter =
          Array.isArray(value) && value.length > 0 ? value : null;
      } else {
        const property = current[fieldName] as IDerivedProperty;
        (property as any)[field] = value;
      }
    }

    onChange(newMappings);
  };

  const handleRowDelete = (rowId: string) => {
    const newMappings = cloneDeep(derivedMappings);
    const pathParts = rowId.split('.');
    const fieldName = pathParts.pop();

    let current = newMappings;
    for (const part of pathParts) {
      current = current[part] as IDerivedMapping;
    }

    delete current[fieldName];
    onChange(newMappings);
  };

  return (
    <DynamicMappingTable
      title={title}
      description={description}
      columns={columns}
      rows={rows}
      onRowChange={handleRowChange}
      onRowDelete={handleRowDelete}
      onAddRow={handleAddRow}
      addButtonText="Add Derived Field"
    />
  );
};

export default AdminDatasourceDerivedMappingEditor;
