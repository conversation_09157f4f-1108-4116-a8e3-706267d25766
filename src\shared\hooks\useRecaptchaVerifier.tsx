import { auth } from '@shared/store/setup/firebase-setup';
import { RecaptchaVerifier } from 'firebase/auth';
import { useCallback } from 'react';

const useRecaptchaVerifier = () => {
  const showVerifier = useCallback(() => {
    if (!window['recaptchaVerifier']) {
      window['recaptchaVerifier'] = new RecaptchaVerifier(auth, 'recaptcha-container', {
        size: 'invisible',
      });
      window['recaptchaVerifier'].render().then((widgetId) => {
        window['recaptchaWidgetId'] = widgetId;
      });
    }
  }, []);
  const removeVerifier = useCallback(() => {
    window['recaptchaVerifier'] = null;
  }, []);

  return { showVerifier, removeVerifier };
};

export default useRecaptchaVerifier;
