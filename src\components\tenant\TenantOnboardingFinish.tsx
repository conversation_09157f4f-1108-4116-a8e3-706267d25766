import OnboardingImage from '@shared/assets/onboarding-3.jpg'; // Reusing an existing image for now
import s from '@shared/styles/component/auth/auth.module.scss';
import { ReactComponent as CheckIcon } from '@svg/checkmark-icon.svg';
import clsx from 'clsx';
import React, { memo } from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
  createdInboxCount: number;
}

const TenantOnboardingFinish: React.FC<Props> = () => {
  const { t } = useTranslation();

  return (
    <div className={clsx(s.card_inner, s.card_inner__flex)}>
      <div className={s.card_inner_left}>
        <img
          src={OnboardingImage}
          alt={t('home:onboarding.titleFinish')}
          loading="eager"
          style={{ width: '360px' }}
        />
      </div>
      <div className={s.card_inner_right}>
        <h2 className={s.card_inner_title}>{t('home:onboarding.titleFinish')}</h2>

        <div className={s.next_steps}>
          <h3>{t('home:onboarding.nextStepsTitle')}</h3>
          <ul className={s.next_steps_list}>
            <li>
              <CheckIcon className={s.check_icon} />
              <span>{t('home:onboarding.nextStep1')}</span>
            </li>
            <li>
              <CheckIcon className={s.check_icon} />
              <span>{t('home:onboarding.nextStep2')}</span>
            </li>
            <li>
              <CheckIcon className={s.check_icon} />
              <span>{t('home:onboarding.nextStep3')}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default memo(TenantOnboardingFinish);
