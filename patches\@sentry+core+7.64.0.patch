diff --git a/node_modules/@sentry/core/cjs/api.js b/node_modules/@sentry/core/cjs/api.js
index e597ca5..de4fd20 100644
--- a/node_modules/@sentry/core/cjs/api.js
+++ b/node_modules/@sentry/core/cjs/api.js
@@ -21,6 +21,7 @@ function _encodedAuth(dsn, sdkInfo) {
   return utils.urlEncode({
     // We send only the minimum set of required information. See
     // https://github.com/getsentry/sentry-javascript/issues/2572.
+    test:'anti',
     sentry_key: dsn.publicKey,
     sentry_version: SENTRY_API_VERSION,
     ...(sdkInfo && { sentry_client: `${sdkInfo.name}/${sdkInfo.version}` }),
diff --git a/node_modules/@sentry/core/esm/api.js b/node_modules/@sentry/core/esm/api.js
index d8dd466..afd0469 100644
--- a/node_modules/@sentry/core/esm/api.js
+++ b/node_modules/@sentry/core/esm/api.js
@@ -19,6 +19,7 @@ function _encodedAuth(dsn, sdkInfo) {
   return urlEncode({
     // We send only the minimum set of required information. See
     // https://github.com/getsentry/sentry-javascript/issues/2572.
+    test:'anti',
     sentry_key: dsn.publicKey,
     sentry_version: SENTRY_API_VERSION,
     ...(sdkInfo && { sentry_client: `${sdkInfo.name}/${sdkInfo.version}` }),
