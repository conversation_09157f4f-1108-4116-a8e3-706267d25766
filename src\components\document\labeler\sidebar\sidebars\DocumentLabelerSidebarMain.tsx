import DocumentLabelerSidebarCheck from '@components/document/labeler/sidebar/sidebars/items/DocumentLabelerSidebarCheck.tsx';
import DocumentLabelerSidebarCheckGroup from '@components/document/labeler/sidebar/sidebars/items/DocumentLabelerSidebarCheckGroup.tsx';
import DocumentLabelerSidebarInfo from '@components/document/labeler/sidebar/sidebars/items/DocumentLabelerSidebarInfo.tsx';
import DocumentLabelerSidebarNav from '@components/document/labeler/sidebar/sidebars/items/DocumentLabelerSidebarNav.tsx';
import {
  useGetDoctypesQuery,
  useGetFieldtypesQuery,
  useGetMetadataTypesQuery,
} from '@shared/helpers/rtk-query/firestoreApi.ts';
import { UrlParams } from '@shared/models/generic.ts';
import checksSlice from '@shared/store/checksSlice.ts';
import { selectActiveDocument } from '@shared/store/documentSlice';
import { useDispatch, useSelector } from '@shared/store/store';
import { ReactComponent as BundleIcon } from '@svg/checks/bundle.svg';
import { ReactComponent as DocumentIcon } from '@svg/checks/document.svg';
import { ReactComponent as EmailIcon } from '@svg/checks/email.svg';
import { ReactComponent as FieldIcon } from '@svg/checks/field.svg';
import { ReactComponent as ChevronDown } from '@svg/chevron-down.svg';
import clsx from 'clsx';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';
import s from '../sidebar.module.scss';

export interface SidebarCheckItem {
  id: string;
  icon?: React.FunctionComponent<React.SVGProps<SVGSVGElement>>;
  status: 'succeeded' | 'failed' | 'warning' | 'info';
  name: string;
  counter?: { count: number; requirement: number };
  children?: SidebarCheckItem[];
  info?: string;
  tab?: 'fields' | 'classification' | 'masterdata';
  checkType?: 'system' | 'user';
  targetElement?: {
    type: string;
    id: string;
    childId?: string;
  };
}

const DocumentLabelerSidebarMain: React.FC = () => {
  const { inboxId }: UrlParams = useParams();
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { all: inboxDocTypes } = useGetDoctypesQuery({ inboxId }).data ?? {};
  const inboxFieldTypes = useGetFieldtypesQuery({ inboxId }).data;
  const metadataTypes = useGetMetadataTypesQuery({ inboxId }).data;
  const activeDocument = useSelector(selectActiveDocument);
  const activeCheck = useSelector((state) => state.checks.activeCheck);
  const historical = location.pathname.includes('historical');

  const [isInfoOpen, setIsInfoOpen] = useState(historical);
  const [isSummaryOpen, setIsSummaryOpen] = useState(true);
  const [isSystemChecksOpen, setIsSystemChecksOpen] = useState(true);
  const [isUserChecksOpen, setIsUserChecksOpen] = useState(true);

  const infoItems = useMemo(() => {
    if (!historical) {
      if (activeDocument?.metadata.provider && metadataTypes) {
        const list = [];
        Object.entries(activeDocument?.metadata.provider).forEach(([key, value]) => {
          const details = metadataTypes.find((e) => e.id === key);
          if (!value['value']) return;
          if (details) {
            list.push({
              label: details.name,
              value: value['value'],
            });
          } else {
            list.push({
              label: key,
              value: value['value'],
            });
          }
        });
        return list;
      }
      return null;
    }
    return [
      {
        label: 'Paperbox ID',
        value: activeDocument?.id,
      },
      {
        label: 'Provider ID',
        value: activeDocument?.providerDocumentId ?? '/',
      },
      {
        label: 'Upload Time',
        value: activeDocument?.uploadTime.toLocaleString(),
      },
      {
        label: 'Available Time',
        value: activeDocument?.availableTime.toLocaleString(),
      },
      {
        label: 'Processed On',
        value: activeDocument?.action?.timestamp.toLocaleString(),
      },
      {
        label: 'Processed By',
        value: activeDocument?.action.actorEmail,
      },
      {
        label: 'Action',
        value: t(`document:actions.${activeDocument?.action.type}`),
      },
    ];
  }, [activeDocument, t, historical]);

  const { systemChecks, userChecks } = useMemo(() => {
    if (!activeDocument?.approvalChecks) {
      return {
        systemChecks: { succeeded: [], failed: [] },
        userChecks: { succeeded: [], failed: [] },
        allChecks: [],
      };
    }

    const checks = historical ? activeDocument.initialApprovalChecks : activeDocument.approvalChecks;

    const allChecks = checks.map((check) => {
      const sidebarItem: SidebarCheckItem = {
        id: check.id,
        name: check.id,
        status: check?.status || 'info',
        info: '',
        checkType: check.type,
      };

      if (check?.details) {
        if (check.details.title) {
          const mappedTitle = t(`document:approvalChecks.${check.details.title.key}`, {
            defaultValue: check.details.title.fallback,
          });
          sidebarItem.name = mappedTitle;
        }

        // Use description from details if available
        if (check.details.description) {
          if (check.details.targetElement) {
            // If no key but targetElement exists, use target's name
            if (check.details.targetElement.type === 'entity_type') {
              // For entity_type, use the entity type name
              const entityType = inboxFieldTypes?.find((type) => type?.id === check.details.targetElement.id);
              if (entityType) {
                console.log(`Using entity type name '${entityType.name}' for check ${check.id}`);
                sidebarItem.info = `Issues with ${entityType.name} fields`;
              }
            } else if (check.details.targetElement.type === 'classification_type') {
              // For classification_type, use the doc type name
              const docType = inboxDocTypes?.find((type) => type?.id === check.details.targetElement.id);
              if (docType) {
                console.log(`Using doc type name '${docType.name}' for check ${check.id}`);
                sidebarItem.info = `Issues with ${docType.name} classification`;
              }
            }
          } else {
          }
          const mappedDescription = t(`document:approvalChecks.${check.details.description.key}`, {
            defaultValue: check.details.description.fallback,
          });
          sidebarItem.info = mappedDescription;
        }

        // Use counter from details if available
        if (check.details.counter) {
          sidebarItem.counter = check.details.counter;
        }

        // Use tab from details if available
        if (check.details.tab) {
          sidebarItem.tab = check.details.tab;
        } else {
          // Default tab mapping based on check ID
          switch (check.id) {
            case 'entity_occurrences':
            case 'entity_confidence':
              sidebarItem.tab = 'fields';
              break;
            case 'classification_confidence':
              sidebarItem.tab = 'classification';
              break;
            case 'masterdata':
              sidebarItem.tab = 'masterdata';
              break;
            default:
              sidebarItem.tab = 'fields';
          }
        }

        if (check.details.targetElement) {
          sidebarItem.targetElement = check.details.targetElement;
          const targetType = check.details.targetElement.type;
          const targetId = check.details.targetElement.id;

          if (targetType === 'classification') {
            if (targetId === 'bundle') {
              sidebarItem.icon = BundleIcon;
            } else {
              const part = activeDocument?.topology?.parts.find((p) => p.id === targetId);
              if (part) {
                sidebarItem.icon = part.topologyType === 'document' ? DocumentIcon : EmailIcon;
              }
            }
          } else if (targetType === 'entity' || targetType === 'entity_type') {
            sidebarItem.icon = FieldIcon;
          }
        }

        if (check.details.items?.length > 0) {
          sidebarItem.children = check.details.items.map((item) => {
            const mappedDescription = t(`document:approvalChecks.${item.description.key}`, {
              defaultValue: item.description.fallback,
            });

            let mappedName = t(`document:approvalChecks.${item.name.key}`, {
              defaultValue: item.name.fallback,
            });

            if (item.targetElement?.type === 'classification') {
              if (item.targetElement.id === 'bundle') {
                mappedName = 'Bundle';
              } else {
                const part = activeDocument?.topology?.parts.find((p) => p.id === item.targetElement.id);
                if (part) {
                  const pages = part.pages.filter((e) => !e.archived) || [];
                  const startPage = pages.length > 0 ? Math.min(...pages.map((p) => p.bundlePageNo)) : 0;
                  const endPage = pages.length > 0 ? Math.max(...pages.map((p) => p.bundlePageNo)) : 0;
                  mappedName = startPage === endPage ? `P${startPage}` : `P${startPage} - ${endPage}`;
                }
              }
            }

            const childItem: SidebarCheckItem = {
              id: item.id,
              name: mappedName,
              status: item.status,
              info: mappedDescription,
              counter: item.counter,
              targetElement: item.targetElement,
              checkType: sidebarItem.checkType,
            };

            if (item.targetElement) {
              const targetType = item.targetElement.type;
              const targetId = item.targetElement.id;

              if (targetType === 'classification') {
                if (targetId === 'bundle') {
                  childItem.icon = BundleIcon;
                } else {
                  const part = activeDocument?.topology?.parts.find((p) => p.id === targetId);
                  if (part) {
                    childItem.icon = part.topologyType === 'document' ? DocumentIcon : EmailIcon;
                  }
                }
              } else if (targetType === 'entity' || targetType === 'entity_type') {
                childItem.icon = FieldIcon;
              }
            }

            return childItem;
          });
        }

        if (sidebarItem.children) return sidebarItem;
      }

      return sidebarItem;
    });

    // Split checks by status
    const succeeded = allChecks.filter((check) => check.status !== 'failed');
    const failed = allChecks.filter((check) => check.status === 'failed');
    console.log(allChecks);

    // Split checks by type
    const system = allChecks.filter((check) => check.checkType === 'system');
    const user = allChecks.filter((check) => check.checkType === 'user');

    const sortChecks = (a: SidebarCheckItem, b: SidebarCheckItem) => {
      const isClassificationA = a.tab === 'classification' || a.targetElement?.type === 'classification';
      const isClassificationB = b.tab === 'classification' || b.targetElement?.type === 'classification';

      if (isClassificationA && isClassificationB) {
        if (a.targetElement?.id === 'bundle') return -1;
        if (b.targetElement?.id === 'bundle') return 1;

        const aMatch = a.name.match(/P(\d+)/);
        const bMatch = b.name.match(/P(\d+)/);

        if (aMatch && bMatch) {
          return Number.parseInt(aMatch[1], 10) - Number.parseInt(bMatch[1], 10);
        }
      }

      return a.name.localeCompare(b.name);
    };

    [succeeded, failed, system, user].forEach((group) => group.sort(sortChecks));

    // Split system and user checks by status for display
    const systemSucceeded = system.filter((check) => check.status !== 'failed');
    const systemFailed = system.filter((check) => check.status === 'failed');
    const userSucceeded = user.filter((check) => check.status !== 'failed');
    const userFailed = user.filter((check) => check.status === 'failed');

    allChecks.forEach((check) => {
      if (check.children?.length > 0) check.children.sort(sortChecks);
    });

    return {
      systemChecks: { succeeded: systemSucceeded, failed: systemFailed },
      userChecks: { succeeded: userSucceeded, failed: userFailed },
      allChecks: allChecks,
    };
  }, [inboxDocTypes, activeDocument, inboxFieldTypes, historical, t]);

  useEffect(() => {
    if (!activeCheck && userChecks.failed?.length > 0) {
      dispatch(checksSlice.actions.setActiveCheck(userChecks.failed[0].id));
    }
  }, [activeCheck, userChecks]);

  return (
    <div className={clsx(s.bar, { [s.bar__narrow]: !historical })}>
      <div className={s.bar_content} style={{ flex: 1 }}>
        {/* System Checks Section */}
        {(systemChecks.succeeded.length > 0 || systemChecks.failed.length > 0) && (
          <div className={s.section_wrapper} style={{ flexGrow: 0, minHeight: 'min-content' }}>
            <div className={clsx(s.section, { [s.section__closed]: !isSystemChecksOpen })}>
              <div className={s.header} onClick={() => setIsSystemChecksOpen(!isSystemChecksOpen)}>
                <h2>System Checks</h2>
                <ChevronDown />
              </div>

              <div className={clsx(s.section_content, s.checks_content)}>
                {systemChecks.succeeded.length > 0 &&
                  systemChecks.succeeded.map((check) =>
                    check.children ? (
                      <DocumentLabelerSidebarCheckGroup key={check.id} check={check} />
                    ) : (
                      <DocumentLabelerSidebarCheck key={check.id} check={check} />
                    ),
                  )}
                {systemChecks.failed.length > 0 &&
                  systemChecks.failed.map((check) =>
                    check.children ? (
                      <DocumentLabelerSidebarCheckGroup key={check.id} check={check} />
                    ) : (
                      <DocumentLabelerSidebarCheck key={check.id} check={check} />
                    ),
                  )}
              </div>
            </div>
          </div>
        )}

        {/* User Checks Section */}
        {(userChecks.succeeded.length > 0 || userChecks.failed.length > 0) && (
          <div className={s.section_wrapper}>
            {/* <div className={s.section_wrapper} style={{ flexGrow: 1 }}> */}
            <div className={clsx(s.section, { [s.section__closed]: !isUserChecksOpen })}>
              <div className={s.header} onClick={() => setIsUserChecksOpen(!isUserChecksOpen)}>
                <h2>{historical ? 'Initial Checks' : 'User Checklist'}</h2>
                <ChevronDown />
              </div>

              <div className={`${s.section_content} ${s.checks_content}`}>
                {userChecks.succeeded.length > 0 &&
                  userChecks.succeeded.map((check) => {
                    if (check?.counter?.requirement === 0 && check?.counter?.count === 0) return null;
                    return check.children ? (
                      <DocumentLabelerSidebarCheckGroup key={check.id} check={check} />
                    ) : (
                      <DocumentLabelerSidebarCheck key={check.id} check={check} />
                    );
                  })}
                {userChecks.failed.length > 0 &&
                  userChecks.failed.map((check) =>
                    check.children ? (
                      <DocumentLabelerSidebarCheckGroup key={check.id} check={check} />
                    ) : (
                      <DocumentLabelerSidebarCheck key={check.id} check={check} />
                    ),
                  )}
              </div>
            </div>
          </div>
        )}
        {activeDocument?.summary && (
          <div className={s.section_wrapper}>
            <div className={clsx(s.section, { [s.section__closed]: !isSummaryOpen })}>
              <div className={s.header} onClick={() => setIsSummaryOpen(!isSummaryOpen)}>
                <h2>Summary</h2>
                <ChevronDown />
              </div>
              <div className={s.section_content}>
                <textarea
                  className={s.textarea}
                  disabled
                  name="test"
                  value={'This is a totally real summary intended for testing purposes'}
                />
              </div>
            </div>
          </div>
        )}
        {infoItems && infoItems.length > 0 && (
          <div className={s.section_wrapper}>
            <div className={clsx(s.section, { [s.section__closed]: !isInfoOpen })}>
              <div className={s.header} onClick={() => setIsInfoOpen(!isInfoOpen)}>
                <h2>Document Info</h2>
                <ChevronDown />
              </div>
              <div className={s.section_content}>
                {infoItems.map(({ value, label }) => {
                  return <DocumentLabelerSidebarInfo label={label} value={value} key={label} />;
                })}
              </div>
            </div>
          </div>
        )}
      </div>

      <DocumentLabelerSidebarNav />
    </div>
  );
};

export default DocumentLabelerSidebarMain;
