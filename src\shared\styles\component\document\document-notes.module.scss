@import "../../vars/_vars";


.wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  margin-left: auto;
  padding-left: 10px;
}


.button {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  width: 80px;
  color: white;
  gap: 8px;
}


.button_icon {
  width: 16px;
  height: 16px;
}


.button_dot {
  position: absolute;
  top: 10px;
  left: 8px;
  width: 8px;
  height: 8px;
  border-radius: 10px;
  background: $paperbox-blue;

  &__unread{
    background: $error;
    animation: pulse 1s infinite;
  }
}
@keyframes pulse{
  0% {
    transform: scale(1);
    box-shadow: none;
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 10px 2px $error;
  }
  100% {
    transform: scale(1);
    box-shadow: none;
  }
}


.modal {
  position: absolute;
  z-index: 100;
  top: 30px;
  left: -225px;
  width: 400px;
  padding: 14px;
  pointer-events: none;
  opacity: 0;

  border-radius: 5px;
  background: $light-gray;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.1);

  &__active {
    z-index: 1000;
    animation: grow 0.3s;
    pointer-events: auto;
    opacity: 1;
    will-change: opacity;
  }
}


@keyframes grow {
  0% {
    transform: translateY(-50px);
    opacity: 0;
  }
  99% {
    transform: translateY(0);
    opacity: 1;
  }
  100% {
    transform: unset;
    opacity: 1;
    pointer-events: auto;

  }
}


.notes {
  display: flex;
  overflow: auto;
  flex-direction: column;
  max-height: 50vh;
  gap: 10px;
}


.note {
  position: relative;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  flex-shrink: 0;
  padding: 10px 20px;
  border: 1px solid $medium-gray;
  border-radius: 10px;
  background: white;
}


.note_empty {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 70%;
  margin: 0 auto;
  text-align: center;
  color: #898B99;

}


.note_loader {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.7);

}


.note_date {
  font-size: 12px;
  margin-top: 2px;
  margin-right: 0;
  margin-left: auto;
  color: #898B99;
}


.note_author {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
}


.note_author_avatar {
  width: 32px;
  height: 32px;
  border-radius: 32px;

}


.note_author_name {
  font-size: 14px;
  color: $font-color-black;

}


.note_actions {
  display: flex;
  margin-right: 0;
  margin-bottom: 2px;
  gap: 2px;

}


.note_action {
  width: 20px;
  height: 20px;
  color: #898B99;

  svg {
    width: auto;
    height: 100%;
  }

  &:hover {
    color: $paperbox-blue;
  }

  &__delete {
    &:hover {
      color: $error;
    }

  }
}


.note_content {
  font-size: 14px;
  line-height: 1.4;
  margin-top: 10px;
  color: #898B99;

}


.input_wrapper {
  position: relative;
  width: 100%;
  height: 35px;
  margin-bottom: 6px;
}


.input_button {
  position: absolute;
  top: 12px;
  right: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;

  svg {

    width: 16px;
    height: 16px;
    color: #898B99;

  }
}


.input {
  font-size: 12px;
  width: 100%;
  height: 35px;
  margin-top: 10px;
  padding: 10px 35px 10px 10px;
  color: $font-color-black;
  border: 1px solid $medium-gray;
  border-radius: 10px;

  &::placeholder {
    color: #898B99;
  }

  &:focus {
    outline: none;
  }
}


