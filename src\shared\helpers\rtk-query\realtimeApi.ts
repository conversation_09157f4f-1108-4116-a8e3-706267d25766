import { createApi, fakeBaseQuery } from '@reduxjs/toolkit/query/react';
import { realtime } from '@shared/store/setup/firebase-setup';
import { RootState } from '@shared/store/store.ts';
import { off, onValue, ref } from 'firebase/database';

export const realtimeApi = createApi({
  reducerPath: 'realtimeApi',
  baseQuery: fakeBaseQuery(),
  endpoints: (builder) => ({
    getLockersList: builder.query<Record<string, any>, { inboxId: string }>({
      queryFn: async () => ({ data: null }),
      async onCacheEntryAdded({ inboxId }, { updateCachedData, cacheEntryRemoved, getState }) {
        const state = getState() as RootState;
        // const email = state.user.userAccount.email;
        const tenantId = state.tenant.tenantId;
        const lockRef = ref(realtime, `tenants/${tenantId}/inboxes/${inboxId}/lockers`);
        // Set up the realtime listener.
        const unsubscribe = onValue(lockRef, (snapshot) => {
          const locks = snapshot.val() || {};
          updateCachedData(() => locks);
        });

        await cacheEntryRemoved;
        off(lockRef);
        unsubscribe();
      },
    }),
  }),
});

export const { useGetLockersListQuery } = realtimeApi;
