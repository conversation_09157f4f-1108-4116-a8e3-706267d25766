import * as path from 'node:path';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react-swc';
import { defineConfig, loadEnv, transformWithEsbuild } from 'vite';
import { VitePWA } from 'vite-plugin-pwa';
import svgr from 'vite-plugin-svgr';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  return {
    resolve: {
      alias: {
        '@components': path.resolve(__dirname, 'src/components'),
        '@shared': path.resolve(__dirname, 'src/shared'),
        '@svg': path.resolve(__dirname, 'src/shared/assets/svg'),
        '@src': path.resolve(__dirname, 'src'),
      },
    },
    server: {
      port: 3000,
    },
    build: {
      outDir: 'build',
      sourcemap: true,
    },
    plugins: [
      {
        name: 'treat-js-files-as-jsx',
        async transform(code, id) {
          if (!id.match(/src\/.*\.js$/)) return null;
          return transformWithEsbuild(code, id, {
            loader: 'jsx',
            jsx: 'automatic',
          });
        },
      },
      svgr(),
      react(),
      VitePWA({
        strategies: 'injectManifest',
        injectManifest: {
          injectionPoint: undefined,
        },
      }),
      sentryVitePlugin({
        org: 'paperbox',
        project: 'react',
        release: {
          name: env.VITE_PAPERBOX_RELEASE,
          inject: true,
        },
        disable: env.VITE_PAPERBOX_ENVIRONMENT !== 'production',
      }),
    ],

    optimizeDeps: {
      force: true,
      esbuildOptions: {
        loader: {
          '.js': 'jsx',
        },
      },
    },
  };
});
