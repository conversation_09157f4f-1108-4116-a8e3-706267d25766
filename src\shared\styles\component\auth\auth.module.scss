@import "../../vars/_vars";


.container {

  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: $gradient-blue;
}


.card {
  line-height: 1.5;
  position: relative;
  display: flex;
  overflow: hidden;
  flex-direction: column;
  min-width: 500px;
  max-width: 500px;
  padding: 75px 75px 75px 75px;
  transition: height 0.2s;
  border-radius: 5px;
  background: white;
  box-shadow: $shadow-medium;

  &__center {
    align-items: center;
    width: 100%;
    text-align: center;
  }

  &__small {
    min-width: 425px;
    max-width: 425px;
    padding: 75px 50px 50px 50px;
  }

  &__wide {
    max-width: 1100px;
    padding: 50px;
    width: 80vw;
  }

}

.onboarding_buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  width: 100%;
  margin-top: 40px;

  &__centered {
    justify-content: center;
  }

  .button_group {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .nav_button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 24px;
    border-radius: 5px;
    font-weight: 500;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &__prev {
      background-color: unset;
      border: none;
      color: #444f6b;

      &:hover:not(:disabled) {
        background-color: #e0e0e0;
      }
    }

    &__next {
      background-color: $paperbox-blue;
      border: 1px solid $paperbox-blue;
      color: white;

      &:hover:not(:disabled) {
        background-color: darken($paperbox-blue, 5%);
        transform: translateX(2px);
      }
    }

    &__finish {
      padding: 12px 32px;
      font-size: 16px;
      font-weight: 600;
      min-width: 280px;
      justify-content: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .nav_button_icon {
    font-size: 18px;
    margin-top: -1px;
    transition: transform 0.2s ease-in-out;
  }

  .nav_button__prev:hover:not(:disabled) .nav_button_icon {
    transform: translateX(-3px);
  }

  .nav_button__next:hover:not(:disabled) .nav_button_icon {
    transform: translateX(3px);
  }

  .skip_button {
    padding: 10px 16px;
    background: none;
    border: none;
    color: $medium-dark-gray;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: underline;

    &:hover {
      color: $paperbox-blue;
    }
  }
}

.step_header {
  text-align: center;
  margin-bottom: 20px;
}

.step_title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: black;
}

.step_subtitle {
  font-size: 14px;
  color: $medium-dark-gray;
}

.step_indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 40px;
  position: relative;
  padding: 0 40px;
}

.step_indicator {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #f5f5f5;
  border: 2px solid #e0e0e0;
  display: flex;
  padding: 3px 0 0 1px;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: $medium-dark-gray;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease-in-out;
  cursor: default;

  &__active {
    background-color: $paperbox-blue;
    border-color: $paperbox-blue;
    color: white;
  }

  &__completed {
    background-color: $paperbox-blue;
    border-color: $paperbox-blue;
    color: white;
    cursor: pointer;
  }
}

.step_connector {
  position: absolute;
  top: 50%;
  left: 40px;
  right: 40px;
  height: 2px;
  background-color: #e0e0e0;
  z-index: 1;
  transform: translateY(-50%);
}

.step_connector_progress {
  height: 100%;
  background-color: $paperbox-blue;
  transition: width 0.3s ease-in-out;
}

.steps_container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.card_inner {
  position: relative;
  width: 100%;
  margin-bottom: 25px;
  transition: height 0.2s ease-out;

  &__flex {
    min-height: 355px;
    display: flex;
    flex-direction: row;
    gap: 100px;
    margin-bottom: 0;
    align-items: center;
  }
}

// Transition styles
:global {
  .fade-enter {
    opacity: 0;
    transform: translateX(20px);
  }

  .fade-enter-active {
    opacity: 1;
    transform: translateX(0);
    transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
  }

  .fade-exit {
    opacity: 1;
    transform: translateX(0);
  }

  .fade-exit-active {
    opacity: 0;
    transform: translateX(-20px);
    transition: opacity 300ms ease-in-out, transform 300ms ease-in-out;
  }
}

.card_inner_left {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  width: 100%;
}

.card_inner_right {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
  gap: 40px;
  width: 100%;



}

.card_inner_title {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  font-family: $headings-font;
  display: flex;
  justify-content: space-between;
  align-items: center;
  svg{
    width: 20px !important;
    height: 20px !important;
  }

}

.options {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;
    cursor: pointer;
    border: 1px solid transparent;

    padding: 20px;
    height: 48px;
    border-radius: 5px;
    transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;

    &__active {
      border-color: $paperbox-blue;

      background: $paperbox-blue--fade-extra;
      font-weight: 500;
    }

    &:hover:not(&__active) {
      border-color: $paperbox-blue--fade-extra;
    }

  }
}

.system_options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.system_option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-radius: 5px;
  border: 1px solid rgba(68, 79, 107, 0.2);
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    border-color: $paperbox-blue;
    background-color: rgba(233, 239, 255, 0.3);
  }

  &__active {
    border-color: $paperbox-blue;
    background-color: $paperbox-blue--fade-extra;
  }
}

.system_option_content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.system_option_title {
  font-size: 16px;
  font-weight: 500;
  color: $dark-gray;
  margin: 0;
}

.system_option_description {
  font-size: 14px;
  color: $medium-dark-gray;
  margin: 0;
}



.system_option_radio {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid rgba(68, 79, 107, 0.3);
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;

  &__active {
    border-color: $paperbox-blue;
  }
}

.system_option_radio_inner {
  width: 0;
  height: 0;
  border-radius: 50%;
  background-color: $paperbox-blue;
  transition: all 0.2s ease-in-out;
}

.system_option_radio__active .system_option_radio_inner {
  width: 10px;
  height: 10px;
}

.form_container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.form_group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form_label {
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
}

.form_input {
  height: 48px;
  padding: 0 16px;
  border: 1px solid rgba(68, 79, 107, 0.2);
  border-radius: 5px;
  font-size: 14px;
  transition: border-color 0.2s ease-in-out;
  width: 100%;

  &:focus {
    outline: none;
    border-color: $paperbox-blue;
    box-shadow: 0 0 1px 2px rgba(0, 133, 255, 0.3);
  }

  &::placeholder {
    color: #aaa;
  }
}

.add_button {
  margin-top: 10px;
  height: 48px;
  background-color: $paperbox-blue;
  color: white;
  border: none;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;

  &:hover:not(:disabled) {
    background-color: darken($paperbox-blue, 5%);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.section_title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
}

.created_inboxes {
  margin-top: 20px;
  width: 100%;
}

.inbox_list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 140px;
  overflow: auto;
  margin-top: -40px;
}

.inbox_item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: #f5f8ff;
  border-radius: 5px;
  border: 1px solid #e9efff;
}

.inbox_details {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;
}

.inbox_name {
  font-weight: 500;
  font-size: 14px;
}

.inbox_type {
  font-size: 12px;
  color: $medium-dark-gray;
}

.finish_message {
  margin-bottom: 20px;
  font-size: 16px;
  line-height: 1.5;
  color: $dark-gray;
}

.next_steps {
  margin-top: 20px;
}

.next_steps_list {
  list-style: none;
  padding: 0;
  margin: 15px 0 0 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.next_steps_list li {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 15px;
  line-height: 1.4;
}

.check_icon {
  color: $paperbox-blue;
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.remove_button {
  background: none;
  border: none;
  cursor: pointer;
  color: $medium-dark-gray;
  transition: color 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: $error;
  }

  svg {
    width: 16px;
    height: 16px;
  }
}


.loading {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  gap: 20px;

}


.reset_password {
  font-size: 14px;
  //position: absolute;

  display: inline-block;
  width: 100%;
  margin-top: 20px;
  margin-bottom: -30px;
  cursor: pointer;
  text-align: center;
  color: #969696;
  margin-inline: auto;

  span {
    border-bottom: 1px solid #EEEEEE;

  }
}


.error {}


.buttons {
  position: absolute;
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  gap: 20px;

  // Ensure proper spacing for login buttons
  .button_main {
    margin-bottom: 0; // Remove any default margins since we're using gap
  }

  // Specific spacing for form elements within buttons container
  form {
    display: flex;
    flex-direction: column;
    gap: 0; // Form has its own spacing via .input class
  }
}


.logo {
  margin-bottom: 20px;

}


.disclaimer {
  font-size: 14px;
  width: 95%;
  margin-top: 20px;
  color: #969696;
  margin-inline: auto;

}


.title {
  font-size: 18px;
  font-weight: 400;
  margin-bottom: 75px;
  color: #969696;
}


.header {
  font-size: 32px;
  font-weight: 500;
}


.sub_header {
  font-size: 16px;
  font-weight: 400;
  margin-bottom: 60px;
  color: #969696;
}


.button {
  @include flex-center;
  font-weight: 500;
  width: 100%;
  height: 50px;
  padding: 0;
  cursor: pointer;
  border-radius: 5px;
  outline: none;

}


.button_main {
  font-weight: 400;
  display: flex;
  overflow: hidden;
  transition: opacity 0.2s, box-shadow 0.2s, color 0.2s, background-color 0.2s;
  color: white;
  border: 1px solid #4285F4;

  &:hover {
    opacity: 0.8;
    box-shadow: $shadow-light;
  }

  .icon {
    width: auto;
    height: 14px;
  }

  &__left {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    height: 100%;
    background: white;

  }

  &__right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;

  }
}


.button_google {
  background: #4285F4;
}


.button_continue {
  color: $paperbox-blue;
  border: 1px solid $paperbox-blue;
  background: white;

  &_active {
    color: white;
    background: $paperbox-blue;
  }
}


.button_sso {
  border: 1px solid #1F63D3;
  background: #1F63D3;

  .icon {
    color: #1F63D3
  }
}


.divider {
  position: relative;
  width: 100%;
  height: 1px;
  margin-top: 25px;
  margin-bottom: 25px;
  color: #D2D2D2;
  background: #D2D2D2;

  &_text {
    font-size: 12px;
    position: absolute;
    top: 0;
    right: 43%;
    bottom: 0;
    left: 43%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    height: auto;
    background: white;
  }
}


.input {
  margin-bottom: 22px;
}


.mfa {
  margin-bottom: 50px;
}


.otp {
  position: absolute;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;

  &__relative {
    position: relative;
  }

  svg {
    width: 45%;
    height: auto;
    margin: 20px;
    transform: translateY(-25px);
  }

  p {
    text-align: center;
  }

  b {
    font-weight: 600;
    display: block;
  }
}


:global {

  .auth-slide-enter {
    transform: scale(0.5);
    opacity: 0;
  }

  .auth-slide-enter-active {
    transition: opacity 0.4s ease-in-out, transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform: scale(1);
    opacity: 1;

  }

  .auth-slide-exit {
    transform: scale(1);
    opacity: 1;

  }

  .auth-slide-exit-active {
    transition: opacity 0.15s ease-in-out, transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform: scale(0.5);
    opacity: 0;

  }
}

.required {
  color: #ff3b30;
  margin-left: 3px;
}

.password_wrapper {
  position: relative;
  width: 100%;
}

.password_toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: #777;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  z-index: 2;

  svg {
    width: 18px;
    height: 18px;
  }
}

.info{
  font-size: 12px;
  color: #777;
  display: flex;
  align-items: center;
  span{
    margin-top: 1px;
  }
}
.info_icon {
  display: inline-flex;
  margin-left: 5px;
  color: #777;
  cursor: pointer;
  height: 16px;
  width: 16px;
  vertical-align: middle;
  align-items: center;

  svg {
    width: 14px;
    height: 14px;
  }
}

.loading_overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  gap: 20px;
  border-radius: 5px;
  backdrop-filter: blur(2px);
}

.loading_message {
  font-size: 18px;
  font-weight: 500;
  margin-top: 20px;
  text-align: center;
}

.loading_progress {
  font-size: 16px;
  color: #444;
  margin-top: 10px;
}

.close_onboarding {
  position: absolute;
  top: 10px;
  right: 10px;
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: $medium-dark-gray;
  transition: color 0.2s ease-in-out;
  z-index: 10;

  &:hover {
    color: $dark-gray;
  }
}
