import s from '@components/document/labeler/sidebar/sidebar.module.scss';
import Tooltip from '@components/shared/tooltip/Tooltip';
import { setActiveCheck } from '@shared/store/checksSlice.ts';
import { setActiveEntityPair } from '@shared/store/labelerSlice.ts';
import { useDispatch, useSelector } from '@shared/store/store.ts';
import { ReactComponent as WarningIcon } from '@svg/alert-triangle.svg';
import { ReactComponent as CheckmarkIcon } from '@svg/checkmark-icon.svg';
import { ReactComponent as InfoIcon } from '@svg/info-icon.svg';
import clsx from 'clsx';
import React, { useMemo } from 'react';
import { SidebarCheckItem } from '../DocumentLabelerSidebarMain';

interface Props {
  check: SidebarCheckItem;
  isChild?: boolean;
}

const DocumentLabelerSidebarCheck: React.FC<Props> = ({ check, isChild }) => {
  const activeCheck = useSelector((state) => state.checks.activeCheck);
  const dispatch = useDispatch();
  const historical = location.pathname.includes('historical');

  const icon = useMemo(() => {
    const iconClasses = clsx(s.icon, {
      [s.checkmark]: check.status === 'succeeded' || check.status === 'info',
      [s.failed]: check.status === 'failed',
      [s.warning]: check.status === 'warning',
    });

    return check.status === 'failed' || check.status === 'warning' ? (
      <WarningIcon className={iconClasses} />
    ) : (
      <CheckmarkIcon className={iconClasses} />
    );
  }, [check.status]);

  return (
    <div
      className={clsx(
        s.row_item,
        { [s.row_item__activeCheck]: activeCheck === check.id && !historical },
        { [s.row_item__faded]: !isChild && activeCheck !== check.id && !historical },
      )}
      onClick={() => {
        if (isChild && check.targetElement?.type === 'entity') {
          dispatch(
            setActiveEntityPair({
              entityId: check.targetElement.id,
              childId: check.targetElement.childId,
              locationIndex: 0,
            }),
          );
        } else if (!isChild) {
          dispatch(setActiveCheck(check.id));
        }
      }}
    >
      <div className={s.row_item_left}>
        {check.icon && <check.icon className={clsx(s.target_icon)} />}
        <Tooltip
          position="right"
          content={
            <>
              <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>{check.name}</div>
              {check.info && <div style={{ marginTop: '5px', fontStyle: 'italic' }}>{check.info}</div>}
            </>
          }
          tooltipStyle={{ maxWidth: '300px' }}
        >
          {isChild ? (
            <div data-status={check.status === 'failed' && 'failed'} className={s.row_badge}>
              <span>{check.name}</span>
            </div>
          ) : (
            <span>{check.name}</span>
          )}
        </Tooltip>
      </div>
      <div className={s.row_item_right}>
        {check.info && (
          <Tooltip
            position="right"
            content={
              <div>
                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>Information</div>
                <div>{check.info}</div>
              </div>
            }
            tooltipStyle={{ maxWidth: '300px' }}
          >
            <div>
              <InfoIcon className={clsx(s.info, s.icon)} />
            </div>
          </Tooltip>
        )}
        {check.counter ? (
          <div className={s.row_badge} data-status={check.status}>
            <span>{`${check.counter.count} / ${check.counter.requirement}`}</span>
          </div>
        ) : (
          <div>{icon}</div>
        )}
      </div>
    </div>
  );
};

export default DocumentLabelerSidebarCheck;
