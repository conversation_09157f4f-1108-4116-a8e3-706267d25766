import { IClientFieldType } from '@shared/helpers/converters/fieldtype.ts';

export interface ActiveEntityPair {
  entityId: string;
  locationIndex: number;
  childId?: string;
}

export interface PaginatedToken {
  text: string;
  page: number;
  index: number;
  id: string;
}

export interface PageTokens {
  page: Page;
  tokens: Token[];
  uriMapping?: Record<string, string>;
}

export interface Token {
  x: number;
  y: number;
  height: number;
  width: number;
  text: string;
  separator?: string;
  uriRef?: string;
}

export interface Page {
  pageNo: number;
  bundlePageNo: number;
  width: number;
  height: number;
  archived: boolean;
}

export interface Bounds {
  x1: number;
  x2: number;
  y1: number;
  y2: number;
  pageNo?: number;
}

export interface DocumentNote {
  id: string;
  timestamp: Date;
  userId: string;
  message: string;
  isLoading?: boolean;
}

export type ApprovalCheckStatus = 'succeeded' | 'failed' | 'warning' | 'info';

export interface DocTypeCategory {
  name: string;
  id: string;
  color?: string;
  entityTypes: IClientFieldType[];
}

export interface TagType {
  id: string;
  providerId?: string;
  name: string;
  color: string;
  isArchived?: boolean;
}
export interface ExtendedTagType extends TagType {
  isDisabled?: boolean;
  disabledReason?: string;
}
//Mark as deprecated
export interface ActionType {
  id: string;
  name: string;
  providerId?: string;
  isSensitive?: boolean;
  isArchived?: boolean;
  type?: ActionTypeType;
  options?: ActionTypeOption[];
}
export type ActionTypeType = 'boolean' | 'text' | 'choice' | 'multi-choice';

export interface ActionTypeOption {
  id: string;
  name: string;
  providerId?: string;
}

export interface SearchResult {
  score: number;
  fields: SearchResultField[];
  id: string;
  index: SearchResultIndexInfo;
}
export interface SearchResultIndexInfo {
  indexName?: string;
  rowId?: string;
  tableId?: string;
  tableName?: string;
  versionId?: string;
}

export interface MasterDataMapping {
  charsToFilter?: string[];
  reference: string;
  referenceType: string;
  displayName: string;
  isHidden?: boolean;
  isSearchable?: boolean;
  isPinned?: boolean;
  type: 'id' | 'text';
  id?: string;
}

export interface SearchResultField {
  name: string;
  highlight: string | string[];
  match: boolean;
  value: string | string[];
  mapping?: MasterDataMapping;
}

export type SearchList = Map<string, string>;
